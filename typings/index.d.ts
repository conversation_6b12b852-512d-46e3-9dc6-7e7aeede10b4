/// <reference path="./types/index.d.ts" />

interface IAppOption {
  globalData: {
    userInfo?: WechatMiniprogram.UserInfo,
    buyNowItem?: {
      skuId: number
      skuCode: string
      productName: string
      skuAttributes: string
      unitPrice: number
      quantity: number
      subtotal: number
      productImageUrl: string
    },
    cartSelectedMap?: { [cartItemId: number]: boolean },
    selectedCoupons?: {
      selectedCoupons: any[]
      totalDiscount: number
      finalAmount: number
    }
  }
  userInfoReadyCallback?: WechatMiniprogram.GetUserInfoSuccessCallback,
}