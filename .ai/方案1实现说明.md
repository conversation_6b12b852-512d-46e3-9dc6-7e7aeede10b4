# 方案1：分离登录和获取用户信息 - 实现说明

## 🎯 实现目标

1. **弹框只做登录**：登录弹框不获取用户信息，只完成基础登录
2. **【我的】页面显示【完善信息】**：登录后用户昵称显示为"完善信息"
3. **用户点击后获取信息**：用户主动点击"完善信息"后调用getUserProfile获取真实头像和昵称

## ✅ 已实现的功能

### 1. 修改登录逻辑 (`utils/auth.ts`)

#### 原来的登录方法
```typescript
// ❌ 原来：登录时强制获取用户信息
async wechatLogin(needUserInfo: boolean = false): Promise<LoginResponse> {
  // 总是尝试获取用户信息
  userInfo = await this.getUserProfile() // 这里会报错
}
```

#### 现在的登录方法
```typescript
// ✅ 现在：只做基础登录
async wechatLogin(): Promise<LoginResponse> {
  // 1. 获取微信授权码
  const loginRes = await this.wxLogin()
  
  // 2. 发送登录请求（不传用户信息）
  const backendResult = await this.sendLoginRequest(loginRes.code, null)
  
  // 3. 保存基础用户信息
  const user = {
    id: backendResult.data.userInfo.id,
    nickName: '完善信息', // 默认显示"完善信息"
    avatarUrl: '/images/default-avatar.png', // 使用默认头像
    hasRealInfo: false // 标记未获取真实用户信息
  }
}
```

### 2. 新增完善信息方法

```typescript
// ✅ 新增：完善用户信息方法
async completeUserProfile(): Promise<{ success: boolean; message: string; userInfo?: any }> {
  // 检查是否已登录
  if (!this.isLoggedIn()) {
    return { success: false, message: '请先登录' }
  }

  // 获取用户信息（必须在用户点击事件中调用）
  const userInfo = await this.getUserProfile()
  
  // 更新本地存储的用户信息
  const updatedUser = {
    ...currentUser,
    nickName: userInfo.nickName,
    avatarUrl: userInfo.avatarUrl,
    hasRealInfo: true // 标记已获取真实用户信息
  }
  
  this.saveUserInfo(updatedUser)
  return { success: true, message: '信息完善成功', userInfo: updatedUser }
}
```

### 3. 修改【我的】页面 (`pages/profile/profile.ts`)

#### 登录调用修改
```typescript
// ❌ 原来：传递参数
const result = await AuthManager.wechatLogin(true)

// ✅ 现在：不传参数
const result = await AuthManager.wechatLogin()
```

#### 新增完善信息方法
```typescript
// ✅ 新增：完善用户信息方法
async completeUserInfo() {
  wx.showLoading({ title: '获取用户信息...' })

  try {
    const result = await AuthManager.completeUserProfile()
    
    if (result.success) {
      wx.showToast({ title: '信息完善成功', icon: 'success' })
      this.getUserInfo() // 更新页面显示
    } else {
      wx.showToast({ title: result.message, icon: 'none' })
    }
  } catch (error) {
    wx.showToast({ title: '获取用户信息失败', icon: 'none' })
  } finally {
    wx.hideLoading()
  }
}
```

### 4. 修改页面模板 (`pages/profile/profile.wxml`)

```xml
<!-- ✅ 新的用户信息显示 -->
<view class="user-details">
  <!-- 如果已登录且昵称是"完善信息"，显示可点击的完善信息按钮 -->
  <view wx:if="{{isLoggedIn && userInfo.nickname === '完善信息'}}" class="username-container">
    <text class="username complete-info" bindtap="completeUserInfo">{{userInfo.nickname}}</text>
    <text class="complete-tip">点击完善头像和昵称</text>
  </view>
  <!-- 否则显示普通昵称 -->
  <text wx:else class="username">{{userInfo.nickname || '用户昵称'}}</text>
  
  <text class="phone">手机号: {{userInfo.phone || '138****8888'}}</text>
  
  <!-- 未登录提示 -->
  <view wx:if="{{!isLoggedIn}}" class="login-tip">
    <text class="login-text">点击登录获取完整功能</text>
  </view>
</view>
```

### 5. 新增样式 (`pages/profile/profile.wxss`)

```css
/* ✅ 完善信息相关样式 */
.username-container {
  margin-bottom: 8rpx;
}

.complete-info {
  color: #fbbf24;
  text-decoration: underline;
  cursor: pointer;
}

.complete-tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  display: block;
  margin-top: 4rpx;
}
```

## 🔄 用户体验流程

### 1. 首次登录流程
```
用户点击【我的】页面
↓
检测未登录，弹出登录提示
↓
用户点击"立即登录"
↓
调用 AuthManager.wechatLogin()（不获取用户信息）
↓
登录成功，显示：
- 头像：默认头像
- 昵称：完善信息（黄色，带下划线）
- 提示：点击完善头像和昵称
```

### 2. 完善信息流程
```
用户点击"完善信息"
↓
调用 completeUserInfo() 方法
↓
在用户点击事件中调用 getUserProfile()
↓
弹出微信授权弹窗
↓
用户确认授权
↓
获取真实头像和昵称
↓
更新页面显示：
- 头像：用户真实头像
- 昵称：用户真实昵称
```

### 3. 状态管理
```
用户状态分为三种：
1. 未登录：显示默认信息 + 登录提示
2. 已登录未完善：显示"完善信息" + 默认头像
3. 已登录已完善：显示真实昵称 + 真实头像
```

## 🎨 界面效果

### 未登录状态
```
[默认头像]  用户昵称
           手机号: 138****8888
           点击登录获取完整功能
```

### 已登录未完善状态
```
[默认头像]  完善信息 ← 黄色，带下划线，可点击
           点击完善头像和昵称
           手机号: 138****8888
```

### 已登录已完善状态
```
[真实头像]  张三
           手机号: 138****8888
```

## 🔧 技术要点

### 1. 解决getUserProfile调用限制
- **问题**：`getUserProfile` 必须在用户直接点击事件中调用
- **解决**：将获取用户信息独立为单独的方法，在用户主动点击时调用

### 2. 状态标记
- 使用 `hasRealInfo` 字段标记是否已获取真实用户信息
- 根据此字段决定显示内容和交互方式

### 3. 用户体验优化
- 登录流程更快（不需要等待用户信息授权）
- 给用户选择权（可以选择是否完善信息）
- 视觉提示清晰（黄色文字 + 下划线 + 提示文字）

## 🧪 测试验证

### 测试场景1：首次登录
1. 清除本地存储
2. 进入【我的】页面
3. 点击"立即登录"
4. 验证：只弹出一次登录提示，不弹出用户信息授权
5. 验证：登录成功后显示"完善信息"

### 测试场景2：完善信息
1. 在已登录未完善状态
2. 点击"完善信息"
3. 验证：弹出微信用户信息授权弹窗
4. 确认授权
5. 验证：页面显示真实头像和昵称

### 测试场景3：状态持久化
1. 完善信息后退出小程序
2. 重新进入【我的】页面
3. 验证：显示真实头像和昵称（不是"完善信息"）

## 🎯 核心优势

1. **符合微信政策**：`getUserProfile` 在用户主动点击时调用
2. **用户体验好**：登录快速，信息完善可选
3. **权限最小化**：只在需要时获取用户信息
4. **状态清晰**：用户明确知道当前状态和可执行操作
5. **向后兼容**：已完善信息的用户不受影响
