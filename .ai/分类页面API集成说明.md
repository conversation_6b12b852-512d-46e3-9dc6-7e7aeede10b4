# 分类页面API集成说明

## 概述

分类页面已经集成了 `/api/categories/tree/active` 接口，可以动态获取服务器端的分类数据并展示一级和二级分类。

## 主要功能

### 1. 动态数据加载
- 页面加载时自动调用 `/api/categories/tree/active` 接口
- 显示加载状态，提供良好的用户体验
- 支持数据刷新功能

### 2. 分类数据展示
- **一级分类**：显示在左侧边栏，支持点击切换
- **二级分类**：显示在右侧网格中，每个分类显示图标和名称
- **空状态处理**：当某个一级分类下没有子分类时显示空状态提示

### 3. 错误处理和降级
- API调用失败时自动使用本地模拟数据
- 显示友好的错误提示信息
- 确保页面在任何情况下都能正常显示

## 技术实现

### 文件结构
```
miniprogram/
├── types/category.ts           # 分类相关类型定义
├── services/categoryService.ts # 分类API服务封装
└── pages/category/
    ├── category.ts            # 页面逻辑（已更新）
    ├── category.wxml          # 页面模板（已更新）
    └── category.wxss          # 页面样式（已更新）
```

### API接口
- **接口地址**: `/api/public/categories/tree`
- **请求方法**: GET
- **接口类型**: 公开接口（无需登录认证）
- **返回格式**: 树形结构的分类数据，包含一级和二级分类

### 数据转换
页面会自动将API返回的树形数据转换为适合展示的格式：
- 提取一级分类作为侧边栏选项
- 将二级分类按父分类分组
- 为每个分类分配合适的图标

## 使用说明

### 1. 配置API地址
确保 `miniprogram/utils/config.ts` 中的 `API_BASE_URL` 配置正确：
```typescript
API_BASE_URL: 'http://your-backend-domain.com'
```

### 2. 后端接口要求
后端需要实现 `/api/public/categories/tree` 接口，返回格式如下：
```json
{
  "code": "200",
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "品牌镜片",
      "level": 1,
      "isActive": true,
      "imageUrl": "https://example.com/category1.png",
      "children": [
        {
          "id": 11,
          "name": "蔡司",
          "level": 2,
          "parentId": 1,
          "isActive": true,
          "imageUrl": "https://example.com/brand1.png"
        }
      ]
    }
  ],
  "success": true,
  "timestamp": 1640995200000
}
```

### 3. 图标配置
- 如果API返回的分类包含 `imageUrl` 字段，将优先使用该图片
- 否则会根据分类名称匹配本地图标
- 本地图标存放在 `miniprogram/images/` 目录下

## 调试功能

### 刷新按钮
页面右下角有一个蓝色的"刷新数据"按钮，用于开发调试：
- 点击可重新加载分类数据
- 生产环境建议移除此按钮

### 控制台日志
页面会在控制台输出详细的调试信息：
- API请求和响应数据
- 数据转换过程
- 错误信息

## 注意事项

1. **网络权限**：确保小程序配置中允许访问后端API域名
2. **公开接口**：`/api/public/categories/tree` 是公开接口，无需登录认证即可访问
3. **图片资源**：确保本地图标文件存在，避免显示异常
4. **性能优化**：分类数据会在页面加载时一次性获取，避免频繁请求

## 扩展功能

### 添加新的分类图标
在 `category.ts` 的 `getDefaultIcon` 方法中添加新的映射关系：
```typescript
const iconMap: { [key: string]: string } = {
  '新分类名称': '/images/new-category-icon.png',
  // ...其他映射
}
```

### 自定义数据处理
可以在 `transformCategoryData` 方法中添加自定义的数据处理逻辑，比如：
- 过滤特定分类
- 添加额外的显示属性
- 自定义排序规则
