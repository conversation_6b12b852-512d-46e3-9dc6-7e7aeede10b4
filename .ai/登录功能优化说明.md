# 登录功能优化说明

## 优化内容

### 1. 移除单独的登录界面
- ❌ 删除了 `pages/login/` 目录及相关文件
- ❌ 从 `app.json` 中移除了登录页面路由
- ✅ 登录功能直接在【我的】页面中触发

### 2. 退出登录优化
- ❌ 退出登录不再跳转页面
- ✅ 停留在当前页面，显示退出成功提示
- ✅ 清理所有本地存储的登录信息
- ✅ 刷新当前页面的登录状态

### 3. 请求头优化
- ✅ 所有API请求自动携带 `sessionId`
- ✅ 所有API请求自动携带 `appId`
- ✅ 移除了之前的 `Authorization` 和 `X-Session-Id` 请求头

## 修改的文件

### 1. `utils/auth.ts`
```typescript
// 添加appId配置
private appId: string = CONFIG.WECHAT.APP_ID

// 添加获取appId方法
getAppId(): string {
  return this.appId
}

// 修改退出登录逻辑
logout(): void {
  this.clearToken()
  // 不跳转页面，停留在当前页面
  wx.showToast({
    title: '已退出登录',
    icon: 'success',
    duration: 1500
  })
}
```

### 2. `utils/request.ts`
```typescript
// 修改请求头设置
const sessionId = AuthManager.getToken()
const appId = AuthManager.getAppId()

if (sessionId) {
  options.header['sessionId'] = sessionId
}

if (appId) {
  options.header['appId'] = appId
}
```

### 3. `utils/config.ts`
```typescript
// 添加appId配置
WECHAT: {
  APP_ID: 'your_miniprogram_appid', // 请替换为实际的小程序AppId
},
```

### 4. `pages/profile/profile.ts`
```typescript
// 修改退出登录逻辑
logout() {
  wx.showModal({
    title: '退出登录',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        AuthManager.logout()
        // 退出登录后刷新页面状态
        this.setData({ 
          isLoggedIn: false,
          userInfo: {
            nickname: '用户昵称',
            phone: '138****8888',
            avatar: '/images/default-avatar.png'
          }
        })
      }
    }
  })
}
```

### 5. `utils/loginHelper.ts`
```typescript
// 简化登录检查逻辑
static checkLogin(showModal: boolean = true): boolean {
  if (AuthManager.isLoggedIn()) {
    return true
  }

  if (showModal) {
    wx.showToast({
      title: '请先登录',
      icon: 'none',
      duration: 2000
    })
  }

  return false
}
```

## 使用方法

### 1. 配置AppId
修改 `utils/config.ts` 文件：
```typescript
WECHAT: {
  APP_ID: 'wx1234567890abcdef', // 替换为实际的小程序AppId
},
```

### 2. 登录流程
1. 用户进入【我的】页面
2. 系统检查登录状态
3. 未登录时弹出登录提示
4. 用户点击"立即登录"触发微信授权
5. 登录成功后页面自动刷新显示用户信息

### 3. 退出登录流程
1. 用户在【我的】页面点击"退出"按钮
2. 弹出确认对话框
3. 确认后清理本地存储
4. 页面状态刷新为未登录状态
5. 停留在当前页面

### 4. API请求
所有通过 `RequestManager` 发送的请求都会自动携带：
```
headers: {
  'Content-Type': 'application/json',
  'sessionId': 'user_session_id',
  'appId': 'your_miniprogram_appid'
}
```

## 请求头说明

### 新的请求头格式
```
sessionId: 2b76e99b855c43748eb8935cee2146a4
appId: wx1234567890abcdef
Content-Type: application/json
```

### 与后端对接
后端需要从请求头中获取：
- `sessionId`：用于验证用户登录状态
- `appId`：用于验证请求来源

## 优势

### 1. 用户体验优化
- ✅ 无需单独的登录页面，减少页面跳转
- ✅ 退出登录后停留在当前页面
- ✅ 登录状态变化时页面自动刷新

### 2. 代码简化
- ✅ 移除了不必要的登录页面代码
- ✅ 简化了页面跳转逻辑
- ✅ 统一了请求头格式

### 3. 维护性提升
- ✅ 登录逻辑集中在【我的】页面
- ✅ 请求头配置统一管理
- ✅ 减少了代码重复

## 注意事项

1. **AppId配置**：必须在 `utils/config.ts` 中配置正确的小程序AppId

2. **后端适配**：后端需要适配新的请求头格式（`sessionId` 和 `appId`）

3. **登录状态**：退出登录后用户停留在当前页面，需要手动刷新页面状态

4. **错误处理**：登录过期时只显示提示，不跳转页面

## 测试建议

1. **登录测试**
   - 在【我的】页面测试登录流程
   - 验证登录成功后的页面状态

2. **退出登录测试**
   - 测试退出登录后的页面状态
   - 验证本地存储是否清理完成

3. **API请求测试**
   - 验证所有API请求是否携带正确的请求头
   - 测试登录过期时的处理逻辑

4. **页面状态测试**
   - 测试登录状态变化时页面的自动刷新
   - 验证用户信息显示是否正确
