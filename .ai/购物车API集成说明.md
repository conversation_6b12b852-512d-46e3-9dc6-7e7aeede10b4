# 购物车API集成说明

## 概述

已成功将购物车功能从使用模拟数据改为调用真实的服务端API。本次更新包括创建完整的购物车服务层、类型定义，以及更新相关页面以使用真实API。

## 主要变更

### 1. 新增文件

#### `miniprogram/types/cart.ts`
- **功能**: 购物车相关的TypeScript类型定义
- **包含内容**:
  - `CartItemDTO`: 服务端返回的购物车项数据结构
  - `CartSummaryDTO`: 购物车汇总信息数据结构
  - `AddToCartRequest`: 添加到购物车的请求数据结构
  - `UpdateCartItemRequest`: 更新购物车项的请求数据结构
  - `CartItem`: 页面使用的购物车项数据结构（兼容现有页面）
  - `CartPageData`: 购物车页面数据结构
  - 各种API响应类型定义

#### `miniprogram/services/cartService.ts`
- **功能**: 购物车相关的API服务层
- **主要方法**:
  - `getCartItems()`: 获取购物车商品列表
  - `getCartSummary()`: 获取购物车汇总信息
  - `getCartItemCount()`: 获取购物车商品数量
  - `addToCart(request)`: 添加商品到购物车
  - `updateCartItem(cartItemId, request)`: 更新购物车项数量
  - `removeCartItem(cartItemId)`: 删除购物车项
  - `batchRemoveCartItems(cartItemIds)`: 批量删除购物车项
  - `clearCart()`: 清空购物车
  - `convertDTOToCartItem(dto)`: 数据转换工具方法
  - `convertDTOsToCartItems(dtos)`: 批量数据转换工具方法

### 2. 修改文件

#### `miniprogram/pages/cart/cart.ts`
- **主要变更**:
  - 移除硬编码的模拟数据
  - 添加导入购物车服务和类型定义
  - 新增 `loadCartData()` 方法从API加载购物车数据
  - 新增 `onRefresh()` 方法支持下拉刷新
  - 更新 `updateQuantity()` 方法调用API更新数量
  - 更新 `removeItem()` 方法调用API删除商品
  - 新增 `batchRemoveSelected()` 方法批量删除选中商品
  - 新增 `clearAllItems()` 方法清空购物车
  - 添加加载状态和错误处理

#### `miniprogram/pages/product-detail/product-detail.ts`
- **主要变更**:
  - 更新 `onAddToCart()` 方法调用真实API添加商品到购物车
  - 添加库存检查逻辑
  - 添加错误处理和用户反馈

## API接口映射

根据 `.ai/restful-api/modules/cart.json` 中的API规范，实现了以下接口调用：

| 功能 | HTTP方法 | API路径 | 对应服务方法 |
|------|----------|---------|-------------|
| 获取购物车列表 | GET | `/api/user/cart/items` | `getCartItems()` |
| 获取购物车汇总 | GET | `/api/user/cart/summary` | `getCartSummary()` |
| 获取购物车数量 | GET | `/api/user/cart/count` | `getCartItemCount()` |
| 添加到购物车 | POST | `/api/user/cart/add` | `addToCart()` |
| 更新购物车项 | PUT | `/api/user/cart/items/{cartItemId}` | `updateCartItem()` |
| 删除购物车项 | DELETE | `/api/user/cart/items/{cartItemId}` | `removeCartItem()` |
| 批量删除购物车项 | DELETE | `/api/user/cart/items/batch` | `batchRemoveCartItems()` |
| 清空购物车 | DELETE | `/api/user/cart/clear` | `clearCart()` |

## 数据转换

为了保持与现有页面的兼容性，实现了数据转换功能：

- **服务端数据** (`CartItemDTO`) → **页面数据** (`CartItem`)
- 自动映射字段名称（如 `productName` → `name`）
- 添加页面特有字段（如 `selected` 状态）
- 根据商品名称智能判断是否需要处方

## 错误处理

- **网络错误**: 显示用户友好的错误提示
- **API错误**: 根据服务端返回的错误信息显示相应提示
- **业务逻辑错误**: 如库存不足、商品不存在等
- **加载状态**: 防止重复请求，提供加载反馈

## 用户体验优化

1. **加载状态**: 在数据加载时显示loading状态
2. **下拉刷新**: 支持下拉刷新购物车数据
3. **乐观更新**: 部分操作先更新UI，再调用API
4. **确认对话框**: 删除和清空操作需要用户确认
5. **错误恢复**: API调用失败时提供重试选项

## 使用说明

### 1. 购物车页面
- 页面加载时自动从API获取购物车数据
- 支持选择/取消选择商品
- 支持修改商品数量（调用API同步）
- 支持删除单个商品（调用API同步）
- 支持批量删除选中商品
- 支持清空整个购物车

### 2. 商品详情页面
- 点击"加入购物车"按钮调用API添加商品
- 自动检查库存状态
- 提供操作结果反馈

### 3. 服务层使用
```typescript
import CartService from '../../services/cartService'

// 获取购物车列表
const items = await CartService.getCartItems()

// 添加到购物车
await CartService.addToCart({ skuId: 1, quantity: 2 })

// 更新数量
await CartService.updateCartItem(cartItemId, { quantity: 3 })

// 删除商品
await CartService.removeCartItem(cartItemId)
```

## 技术特点

1. **类型安全**: 完整的TypeScript类型定义
2. **服务分层**: 清晰的服务层架构
3. **错误处理**: 完善的错误处理机制
4. **数据转换**: 自动的数据格式转换
5. **兼容性**: 保持与现有页面的兼容性
6. **可扩展**: 易于添加新的购物车功能

## 注意事项

1. **认证要求**: 购物车API需要用户登录，会自动携带认证信息
2. **错误处理**: 所有API调用都包含完整的错误处理
3. **数据同步**: 页面数据与服务端数据保持同步
4. **性能优化**: 避免不必要的API调用
5. **用户体验**: 提供清晰的操作反馈

## 后续扩展

可以基于当前架构继续扩展以下功能：
- 购物车商品收藏
- 购物车商品推荐
- 购物车数据缓存
- 离线购物车支持
- 购物车分享功能
