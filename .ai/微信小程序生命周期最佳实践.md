# 微信小程序页面生命周期最佳实践

## 📋 生命周期详解

### 1. 完整的生命周期流程

```
用户操作          →  生命周期方法      →  执行时机
─────────────────────────────────────────────────
首次进入页面      →  onLoad          →  页面加载（仅一次）
                 →  onShow          →  页面显示
                 →  onReady         →  页面渲染完成（仅一次）

从其他页面返回    →  onShow          →  页面显示

切换到其他页面    →  onHide          →  页面隐藏

页面被销毁        →  onUnload        →  页面卸载（仅一次）
```

### 2. 各生命周期的详细说明

#### onLoad(options)
- **触发时机**：页面加载时触发，一个页面只会调用一次
- **参数**：可以获取页面跳转时传递的参数
- **适用场景**：
  - 初始化页面数据
  - 获取页面参数
  - 设置页面标题
  - 一次性的配置操作

```typescript
onLoad(options) {
  // ✅ 适合的操作
  console.log('页面参数:', options)
  this.setData({ pageId: options.id })
  wx.setNavigationBarTitle({ title: '页面标题' })
  
  // ✅ 初始化登录检查（首次）
  this.checkLoginAndGetUserInfo()
}
```

#### onShow()
- **触发时机**：页面显示/切入前台时触发，每次都会执行
- **适用场景**：
  - 刷新页面数据
  - 检查状态变化
  - 重新获取最新信息
  - 恢复定时器

```typescript
onShow() {
  // ✅ 适合的操作
  this.refreshData()           // 刷新数据
  this.checkStatusChange()     // 检查状态变化
  this.startTimer()           // 启动定时器
  
  // ✅ 静默检查登录状态变化
  this.refreshLoginStatus()
}
```

#### onReady()
- **触发时机**：页面初次渲染完成时触发，一个页面只会调用一次
- **适用场景**：
  - DOM操作
  - 获取组件实例
  - 设置画布大小

```typescript
onReady() {
  // ✅ 适合的操作
  this.selectComponent('#my-component')
  wx.createSelectorQuery().select('#canvas').boundingClientRect()
}
```

#### onHide()
- **触发时机**：页面隐藏/切入后台时触发
- **适用场景**：
  - 暂停操作
  - 清除定时器
  - 保存临时数据

```typescript
onHide() {
  // ✅ 适合的操作
  this.pauseVideo()           // 暂停视频
  clearInterval(this.timer)   // 清除定时器
  this.saveTemporaryData()    // 保存临时数据
}
```

#### onUnload()
- **触发时机**：页面卸载时触发
- **适用场景**：
  - 清理资源
  - 取消网络请求
  - 移除事件监听

```typescript
onUnload() {
  // ✅ 适合的操作
  wx.request.abort()          // 取消请求
  wx.offNetworkStatusChange() // 移除监听
  this.cleanup()              // 清理资源
}
```

## 🎯 登录检查的最佳实践

### 问题分析
原代码在 `onLoad` 和 `onShow` 都调用登录检查：
```typescript
// ❌ 问题代码
onLoad() {
  this.checkLoginAndGetUserInfo() // 第一次弹框
},
onShow() {
  this.checkLoginAndGetUserInfo() // 第二次弹框（首次进入时）
}
```

### 解决方案
```typescript
// ✅ 优化后的代码
onLoad() {
  // 页面加载时进行初始化登录检查
  this.checkLoginAndGetUserInfo()
},

onShow() {
  // 页面显示时静默检查登录状态变化
  this.refreshLoginStatus()
},

checkLoginAndGetUserInfo() {
  // 完整的登录检查，包括弹框提示
  if (!AuthManager.isLoggedIn()) {
    this.showLoginModal() // 会弹出登录提示
    return
  }
  this.setData({ isLoggedIn: true })
  this.getUserInfo()
},

refreshLoginStatus() {
  // 静默检查，不弹框
  const isLoggedIn = AuthManager.isLoggedIn()
  const currentLoginStatus = this.data.isLoggedIn
  
  // 只有状态变化时才更新
  if (isLoggedIn !== currentLoginStatus) {
    this.setData({ isLoggedIn })
    if (isLoggedIn) {
      this.getUserInfo() // 用户在其他地方登录了
    } else {
      this.resetUserInfo() // 用户在其他地方退出了
    }
  }
}
```

## 🔄 不同场景的执行流程

### 场景1：首次进入【我的】页面
```
onLoad → checkLoginAndGetUserInfo() → 弹出登录提示（如果未登录）
onShow → refreshLoginStatus() → 静默检查，不弹框
onReady → 页面渲染完成
```

### 场景2：从其他页面返回【我的】页面
```
onShow → refreshLoginStatus() → 检查登录状态是否变化
```

### 场景3：用户在其他页面登录后返回
```
onShow → refreshLoginStatus() → 检测到登录状态变化 → 更新用户信息
```

### 场景4：用户在其他页面退出后返回
```
onShow → refreshLoginStatus() → 检测到登录状态变化 → 重置用户信息
```

## 📱 其他页面的最佳实践

### 需要登录的功能页面
```typescript
// pages/orders/orders.ts
Page({
  onLoad() {
    // 不弹框检查，静默处理
    if (!AuthManager.isLoggedIn()) {
      this.showEmptyState('请先登录查看订单')
      return
    }
    this.loadOrders()
  },

  onShow() {
    // 检查登录状态变化
    if (AuthManager.isLoggedIn() && !this.data.orders.length) {
      this.loadOrders() // 用户可能刚登录，重新加载数据
    }
  }
})
```

### 购物车页面
```typescript
// pages/cart/cart.ts
Page({
  onLoad() {
    this.loadCartItems() // 无论是否登录都显示购物车
  },

  onShow() {
    this.refreshCartItems() // 刷新购物车状态
  },

  checkout() {
    // 结算时才检查登录
    if (!AuthManager.isLoggedIn()) {
      this.showLoginPrompt()
      return
    }
    this.proceedToCheckout()
  }
})
```

## 🎯 总结

### 登录检查的最佳时机

| 页面类型 | onLoad | onShow | 说明 |
|---------|--------|--------|------|
| 【我的】页面 | 完整检查+弹框 | 静默检查状态变化 | 主要登录入口 |
| 需要登录的页面 | 静默检查 | 检查状态变化 | 避免频繁弹框 |
| 公共页面 | 不检查 | 不检查 | 按需检查 |

### 核心原则

1. **避免重复弹框**：`onLoad` 负责初始检查，`onShow` 负责状态同步
2. **静默检查**：在 `onShow` 中使用静默检查，避免用户体验中断
3. **状态同步**：确保页面状态与实际登录状态保持一致
4. **按需检查**：不是所有页面都需要在生命周期中检查登录状态

### 推荐模式

```typescript
// 标准的登录检查模式
Page({
  onLoad() {
    // 初始化检查（可能弹框）
    this.initializeLoginCheck()
  },

  onShow() {
    // 状态同步检查（静默）
    this.syncLoginStatus()
  },

  initializeLoginCheck() {
    // 根据页面需求决定是否弹框
  },

  syncLoginStatus() {
    // 静默检查，只更新状态
  }
})
```
