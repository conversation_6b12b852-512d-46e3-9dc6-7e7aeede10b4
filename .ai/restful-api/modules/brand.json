{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 品牌管理", "description": "品牌管理相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "品牌管理", "description": "品牌相关接口"}], "paths": {"/api/brands/{id}": {"get": {"tags": ["品牌管理"], "summary": "根据ID查询品牌", "description": "根据品牌ID查询品牌详情", "operationId": "getById_3", "parameters": [{"name": "id", "in": "path", "description": "品牌ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBrandDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "put": {"tags": ["品牌管理"], "summary": "更新品牌", "description": "更新品牌信息", "operationId": "update_3", "parameters": [{"name": "id", "in": "path", "description": "品牌ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBrandRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBrandDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "delete": {"tags": ["品牌管理"], "summary": "删除品牌", "description": "删除品牌（逻辑删除）", "operationId": "delete_3", "parameters": [{"name": "id", "in": "path", "description": "品牌ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/brands/{id}/status": {"put": {"tags": ["品牌管理"], "summary": "更新品牌状态", "description": "更新品牌启用/禁用状态", "operationId": "updateStatus_3", "parameters": [{"name": "id", "in": "path", "description": "品牌ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "isActive", "in": "query", "description": "状态：true-启用，false-禁用", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/brands/batch/status": {"put": {"tags": ["品牌管理"], "summary": "批量更新品牌状态", "description": "批量更新品牌启用/禁用状态", "operationId": "batchUpdateStatus_3", "parameters": [{"name": "ids", "in": "query", "description": "品牌ID列表", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "isActive", "in": "query", "description": "状态：true-启用，false-禁用", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/brands": {"post": {"tags": ["品牌管理"], "summary": "创建品牌", "description": "创建新品牌", "operationId": "create_3", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBrandRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBrandDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/brands/{id}/product-count": {"get": {"tags": ["品牌管理"], "summary": "统计品牌下的商品数量", "description": "统计指定品牌下的商品数量", "operationId": "countProductsByBrandId", "parameters": [{"name": "id", "in": "path", "description": "品牌ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/brands/page": {"get": {"tags": ["品牌管理"], "summary": "分页查询品牌列表", "description": "分页查询品牌列表，返回分页信息", "operationId": "getPageList_1", "parameters": [{"name": "name", "in": "query", "description": "品牌名称", "required": false, "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "description": "状态：true-启用，false-禁用", "required": false, "schema": {"type": "boolean"}}, {"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}, "example": 1}, {"name": "size", "in": "query", "description": "每页大小", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}, "example": 10}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultPageResultBrandDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/brands/name/{name}": {"get": {"tags": ["品牌管理"], "summary": "根据名称查询品牌", "description": "根据品牌名称查询品牌详情", "operationId": "getByName_1", "parameters": [{"name": "name", "in": "path", "description": "品牌名称", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBrandDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/brands/exists/name": {"get": {"tags": ["品牌管理"], "summary": "检查品牌名称是否存在", "description": "检查品牌名称是否已存在", "operationId": "existsByName_1", "parameters": [{"name": "name", "in": "query", "description": "品牌名称", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/brands/all": {"get": {"tags": ["品牌管理"], "summary": "查询所有品牌", "description": "查询所有品牌列表", "operationId": "getAll_2", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListBrandDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/brands/active": {"get": {"tags": ["品牌管理"], "summary": "查询活跃品牌列表", "description": "查询所有启用的品牌", "operationId": "getActiveList_3", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListBrandDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultBrandDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/BrandDTO"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "PageResultBrandDTO": {"type": "object", "properties": {"records": {"type": "array", "description": "数据列表", "items": {"$ref": "#/components/schemas/BrandDTO"}}, "total": {"type": "integer", "description": "总记录数", "format": "int64", "example": 100}, "pageNum": {"type": "integer", "description": "当前页码", "format": "int32", "example": 1}, "pageSize": {"type": "integer", "description": "每页大小", "format": "int32", "example": 10}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32", "example": 10}, "hasNext": {"type": "boolean", "description": "是否有下一页", "example": true}, "hasPrevious": {"type": "boolean", "description": "是否有上一页", "example": false}, "empty": {"type": "boolean"}, "size": {"type": "integer", "format": "int32"}}, "description": "分页结果"}, "BrandDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "品牌ID", "format": "int32", "example": 1}, "name": {"type": "string", "description": "品牌名称", "example": "雷朋"}, "logoUrl": {"type": "string", "description": "品牌Logo URL", "example": "https://example.com/logo.png"}, "description": {"type": "string", "description": "品牌描述", "example": "全球知名太阳镜品牌"}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}}, "description": "品牌信息"}, "ResultListBrandDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/BrandDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultPageResultBrandDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageResultBrandDTO"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "UpdateBrandRequest": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 0, "type": "string", "description": "品牌名称", "example": "雷朋"}, "logoUrl": {"maxLength": 1024, "minLength": 0, "type": "string", "description": "品牌Logo URL", "example": "https://example.com/logo.png"}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "description": "品牌描述", "example": "全球知名太阳镜品牌"}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}}, "description": "更新品牌请求"}, "CreateBrandRequest": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 0, "type": "string", "description": "品牌名称", "example": "雷朋"}, "logoUrl": {"maxLength": 1024, "minLength": 0, "type": "string", "description": "品牌Logo URL", "example": "https://example.com/logo.png"}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "description": "品牌描述", "example": "全球知名太阳镜品牌"}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}}, "description": "创建品牌请求"}, "ResultInteger": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "integer", "format": "int32"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultBoolean": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "boolean"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}}}}