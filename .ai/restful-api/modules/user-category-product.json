{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 用户端分类商品", "description": "用户端分类商品相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "用户端分类商品", "description": "用户端分类商品相关接口（无需登录）"}], "paths": {"/api/public/categories/{id}/with-skus": {"get": {"tags": ["用户端分类商品"], "summary": "获取商品及其SKU信息", "description": "获取商品详情及其所有SKU信息", "operationId": "getProductWithSkus", "parameters": [{"name": "id", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultProductWithSkusDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/public/categories/{categoryId}/products": {"get": {"tags": ["用户端分类商品"], "summary": "根据分类ID查询商品列表", "description": "根据分类ID查询该分类下的所有活跃商品", "operationId": "getProductsByCategoryId", "parameters": [{"name": "categoryId", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/public/categories/tree": {"get": {"tags": ["用户端分类商品"], "summary": "查询活跃分类树", "description": "查询活跃分类的树形结构（不包含商品信息）", "operationId": "getActiveCategoryTree", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultProductWithSkusDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/ProductWithSkusDTO"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "CategoryDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "分类ID", "format": "int32", "example": 1}, "parentId": {"type": "integer", "description": "父分类ID", "format": "int32", "example": 1}, "name": {"type": "string", "description": "分类名称", "example": "眼镜框架"}, "slug": {"type": "string", "description": "URL友好标识", "example": "frames"}, "description": {"type": "string", "description": "分类描述", "example": "各种款式的眼镜框架"}, "imageUrl": {"type": "string", "description": "分类图片URL", "example": "https://example.com/category.png"}, "level": {"type": "integer", "description": "层级", "format": "int32", "example": 1}, "path": {"type": "string", "description": "层级路径", "example": "/1"}, "sortOrder": {"type": "integer", "description": "排序权重", "format": "int32", "example": 100}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}, "children": {"type": "array", "description": "子分类列表", "items": {"$ref": "#/components/schemas/CategoryDTO"}}, "productCount": {"type": "integer", "description": "商品数量", "format": "int32", "example": 10}, "topLevel": {"type": "boolean"}}, "description": "分类信息"}, "ProductStatistics": {"type": "object", "properties": {"totalSkuCount": {"type": "integer", "description": "SKU总数", "format": "int32", "example": 5}, "activeSkuCount": {"type": "integer", "description": "活跃SKU数量", "format": "int32", "example": 4}, "totalStockQuantity": {"type": "integer", "description": "总库存数量", "format": "int32", "example": 150}, "lowStockSkuCount": {"type": "integer", "description": "低库存SKU数量", "format": "int32", "example": 2}, "minPrice": {"type": "number", "description": "最低价格", "example": 299.0}, "maxPrice": {"type": "number", "description": "最高价格", "example": 599.0}}, "description": "商品统计信息"}, "ResultListCategoryDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ProductSkuDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "SKU ID", "format": "int64", "example": 10000}, "productId": {"type": "integer", "description": "商品ID", "format": "int64", "example": 1000}, "skuCode": {"type": "string", "description": "SKU编码", "example": "RB-CLASSIC-BLACK-M"}, "nameExtension": {"type": "string", "description": "SKU名称后缀", "example": "黑色 中号"}, "price": {"type": "number", "description": "销售价", "example": 399.0}, "originalPrice": {"type": "number", "description": "原价", "example": 499.0}, "stockQuantity": {"type": "integer", "description": "库存数量", "format": "int32", "example": 50}, "lowStockThreshold": {"type": "integer", "description": "低库存预警值", "format": "int32", "example": 10}, "weightKg": {"type": "number", "description": "重量", "example": 0.15}, "dimensionsCm": {"type": "string", "description": "尺寸", "example": "14x5x3"}, "imageUrl": {"type": "string", "description": "SKU专属图片", "example": "https://example.com/sku.jpg"}, "attributes": {"type": "object", "additionalProperties": {"type": "string", "description": "规格属性"}, "description": "规格属性"}, "attributesJson": {"type": "string", "description": "规格属性JSON", "example": "{\"color\":\"黑色\",\"size\":\"中号\"}"}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}, "productName": {"type": "string", "description": "商品名称", "example": "雷朋经典款太阳镜"}, "discountRate": {"type": "number"}, "lowStock": {"type": "boolean"}, "fullName": {"type": "string"}}, "description": "商品SKU信息"}, "ResultListProductDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ProductWithSkusDTO": {"type": "object", "properties": {"product": {"$ref": "#/components/schemas/ProductDTO"}, "skus": {"type": "array", "description": "SKU列表", "items": {"$ref": "#/components/schemas/ProductSkuDTO"}}, "statistics": {"$ref": "#/components/schemas/ProductStatistics"}}, "description": "商品及其SKU信息"}, "ProductDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "商品ID", "format": "int64", "example": 1000}, "name": {"type": "string", "description": "商品名称", "example": "雷朋经典款太阳镜"}, "slug": {"type": "string", "description": "URL友好标识", "example": "rayban-classic-sunglasses"}, "brandId": {"type": "integer", "description": "品牌ID", "format": "int32", "example": 1}, "brandName": {"type": "string", "description": "品牌名称", "example": "雷朋"}, "shortDescription": {"type": "string", "description": "简短描述", "example": "经典款太阳镜，时尚百搭"}, "fullDescription": {"type": "string", "description": "详细描述"}, "mainImageUrl": {"type": "string", "description": "主图URL", "example": "https://example.com/product.jpg"}, "isActive": {"type": "boolean", "description": "上架状态", "example": true}, "isFeatured": {"type": "boolean", "description": "推荐状态", "example": false}, "publishedAt": {"type": "string", "description": "首次发布时间", "format": "date-time"}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}, "categories": {"type": "array", "description": "商品分类列表", "items": {"$ref": "#/components/schemas/CategoryDTO"}}, "skus": {"type": "array", "description": "商品SKU列表", "items": {"$ref": "#/components/schemas/ProductSkuDTO"}}, "minPrice": {"type": "string", "description": "最低价格", "example": "299.0"}, "maxPrice": {"type": "string", "description": "最高价格", "example": "599.0"}, "totalStock": {"type": "integer", "description": "总库存", "format": "int32", "example": 100}, "priceRange": {"type": "string"}, "published": {"type": "boolean"}}, "description": "商品信息"}}}}