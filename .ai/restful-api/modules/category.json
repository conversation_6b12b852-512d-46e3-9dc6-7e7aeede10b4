{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 分类管理", "description": "分类管理相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "分类管理", "description": "分类相关接口"}], "paths": {"/api/categories/{id}": {"get": {"tags": ["分类管理"], "summary": "根据ID查询分类", "description": "根据分类ID查询分类详情", "operationId": "getById_2", "parameters": [{"name": "id", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "put": {"tags": ["分类管理"], "summary": "更新分类", "description": "更新分类信息", "operationId": "update_2", "parameters": [{"name": "id", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "delete": {"tags": ["分类管理"], "summary": "删除分类", "description": "删除分类（逻辑删除）", "operationId": "delete_2", "parameters": [{"name": "id", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/{id}/status": {"put": {"tags": ["分类管理"], "summary": "更新分类状态", "description": "更新分类启用/禁用状态", "operationId": "updateStatus_2", "parameters": [{"name": "id", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "isActive", "in": "query", "description": "状态：true-启用，false-禁用", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/batch/status": {"put": {"tags": ["分类管理"], "summary": "批量更新分类状态", "description": "批量更新分类启用/禁用状态", "operationId": "batchUpdateStatus_2", "parameters": [{"name": "ids", "in": "query", "description": "分类ID列表", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "isActive", "in": "query", "description": "状态：true-启用，false-禁用", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/batch/sort": {"put": {"tags": ["分类管理"], "summary": "批量更新排序", "description": "批量更新分类排序权重", "operationId": "batchUpdateSortOrder", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "排序更新列表", "items": {"$ref": "#/components/schemas/CategorySortUpdate"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories": {"post": {"tags": ["分类管理"], "summary": "创建分类", "description": "创建新分类", "operationId": "create_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/batch/assign-products": {"post": {"tags": ["分类管理"], "summary": "批量为分类分配商品", "description": "批量为分类分配多个商品", "operationId": "batchAssignProductsToCategory_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchAssignProductsRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/{id}/products": {"get": {"tags": ["分类管理"], "summary": "获取分类下的商品列表", "description": "获取指定分类下的所有商品", "operationId": "getProductsByCategoryId_2", "parameters": [{"name": "id", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "delete": {"tags": ["分类管理"], "summary": "批量移除分类下的商品", "description": "批量移除分类下的多个商品", "operationId": "batchRemoveProductsFromCategory_1", "parameters": [{"name": "id", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "商品ID列表", "items": {"type": "integer", "format": "int64"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/{id}/product-count": {"get": {"tags": ["分类管理"], "summary": "统计分类下的商品数量", "description": "统计指定分类下的商品数量", "operationId": "countProductsByCategoryId_1", "parameters": [{"name": "id", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/{id}/children-count": {"get": {"tags": ["分类管理"], "summary": "统计分类下的子分类数量", "description": "统计指定分类下的子分类数量", "operationId": "countChildrenByCategoryId", "parameters": [{"name": "id", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/tree": {"get": {"tags": ["分类管理"], "summary": "查询分类树", "description": "查询分类树结构（包含子分类）", "operationId": "getCategoryTree", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/tree/with-products": {"get": {"tags": ["分类管理"], "summary": "查询分类及关联商品的树形结构", "description": "查询所有分类及其关联商品的树形结构", "operationId": "getCategoryWithProductsTree", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCategoryWithProductsTreeDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/tree/active": {"get": {"tags": ["分类管理"], "summary": "查询活跃分类树", "description": "查询活跃分类树结构", "operationId": "getActiveCategoryTree_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/tree/active/with-products": {"get": {"tags": ["分类管理"], "summary": "查询活跃分类及关联商品的树形结构", "description": "查询活跃分类及其关联商品的树形结构", "operationId": "getActiveCategoryWithProductsTree", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCategoryWithProductsTreeDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/top-level": {"get": {"tags": ["分类管理"], "summary": "查询一级分类列表", "description": "查询所有一级分类", "operationId": "getTopLevelList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/top-level/active": {"get": {"tags": ["分类管理"], "summary": "查询活跃的一级分类列表", "description": "查询所有启用的一级分类", "operationId": "getActiveTopLevelList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/slug/{slug}": {"get": {"tags": ["分类管理"], "summary": "根据slug查询分类", "description": "根据分类slug查询分类详情", "operationId": "getBySlug_1", "parameters": [{"name": "slug", "in": "path", "description": "分类slug", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/parent/{parentId}": {"get": {"tags": ["分类管理"], "summary": "根据父分类ID查询子分类", "description": "根据父分类ID查询子分类列表", "operationId": "getByParentId", "parameters": [{"name": "parentId", "in": "path", "description": "父分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/parent/{parentId}/active": {"get": {"tags": ["分类管理"], "summary": "根据父分类ID查询活跃子分类", "description": "根据父分类ID查询活跃的子分类列表", "operationId": "getActiveByParentId", "parameters": [{"name": "parentId", "in": "path", "description": "父分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/name/{name}": {"get": {"tags": ["分类管理"], "summary": "根据名称查询分类", "description": "根据分类名称查询分类详情", "operationId": "getByName", "parameters": [{"name": "name", "in": "path", "description": "分类名称", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/exists/slug": {"get": {"tags": ["分类管理"], "summary": "检查分类slug是否存在", "description": "检查分类slug是否已存在", "operationId": "existsBySlug_1", "parameters": [{"name": "slug", "in": "query", "description": "分类slug", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/exists/name": {"get": {"tags": ["分类管理"], "summary": "检查分类名称是否存在", "description": "检查同级别下分类名称是否已存在", "operationId": "existsByNameAndParentId", "parameters": [{"name": "name", "in": "query", "description": "分类名称", "required": true, "schema": {"type": "string"}}, {"name": "parentId", "in": "query", "description": "父分类ID", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/all": {"get": {"tags": ["分类管理"], "summary": "查询所有分类", "description": "查询所有分类列表", "operationId": "getAll_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/categories/active": {"get": {"tags": ["分类管理"], "summary": "查询活跃分类列表", "description": "查询所有启用的分类", "operationId": "getActiveList_2", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "CategorySortUpdate": {"required": ["id", "sortOrder"], "type": "object", "properties": {"id": {"type": "integer", "description": "分类ID", "format": "int32", "example": 1}, "sortOrder": {"maximum": 9999, "minimum": 0, "type": "integer", "description": "排序权重", "format": "int32", "example": 100}}, "description": "分类排序更新"}, "CategoryDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "分类ID", "format": "int32", "example": 1}, "parentId": {"type": "integer", "description": "父分类ID", "format": "int32", "example": 1}, "name": {"type": "string", "description": "分类名称", "example": "眼镜框架"}, "slug": {"type": "string", "description": "URL友好标识", "example": "frames"}, "description": {"type": "string", "description": "分类描述", "example": "各种款式的眼镜框架"}, "imageUrl": {"type": "string", "description": "分类图片URL", "example": "https://example.com/category.png"}, "level": {"type": "integer", "description": "层级", "format": "int32", "example": 1}, "path": {"type": "string", "description": "层级路径", "example": "/1"}, "sortOrder": {"type": "integer", "description": "排序权重", "format": "int32", "example": 100}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}, "children": {"type": "array", "description": "子分类列表", "items": {"$ref": "#/components/schemas/CategoryDTO"}}, "productCount": {"type": "integer", "description": "商品数量", "format": "int32", "example": 10}, "topLevel": {"type": "boolean"}}, "description": "分类信息"}, "ResultBoolean": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "boolean"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultListCategoryDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ProductSkuDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "SKU ID", "format": "int64", "example": 10000}, "productId": {"type": "integer", "description": "商品ID", "format": "int64", "example": 1000}, "skuCode": {"type": "string", "description": "SKU编码", "example": "RB-CLASSIC-BLACK-M"}, "nameExtension": {"type": "string", "description": "SKU名称后缀", "example": "黑色 中号"}, "price": {"type": "number", "description": "销售价", "example": 399.0}, "originalPrice": {"type": "number", "description": "原价", "example": 499.0}, "stockQuantity": {"type": "integer", "description": "库存数量", "format": "int32", "example": 50}, "lowStockThreshold": {"type": "integer", "description": "低库存预警值", "format": "int32", "example": 10}, "weightKg": {"type": "number", "description": "重量", "example": 0.15}, "dimensionsCm": {"type": "string", "description": "尺寸", "example": "14x5x3"}, "imageUrl": {"type": "string", "description": "SKU专属图片", "example": "https://example.com/sku.jpg"}, "attributes": {"type": "object", "additionalProperties": {"type": "string", "description": "规格属性"}, "description": "规格属性"}, "attributesJson": {"type": "string", "description": "规格属性JSON", "example": "{\"color\":\"黑色\",\"size\":\"中号\"}"}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}, "productName": {"type": "string", "description": "商品名称", "example": "雷朋经典款太阳镜"}, "discountRate": {"type": "number"}, "lowStock": {"type": "boolean"}, "fullName": {"type": "string"}}, "description": "商品SKU信息"}, "CreateCategoryRequest": {"required": ["name"], "type": "object", "properties": {"parentId": {"type": "integer", "description": "父分类ID", "format": "int32", "example": 1}, "name": {"maxLength": 100, "minLength": 0, "type": "string", "description": "分类名称", "example": "眼镜框架"}, "slug": {"maxLength": 100, "minLength": 0, "type": "string", "description": "URL友好标识", "example": "frames"}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "description": "分类描述", "example": "各种款式的眼镜框架"}, "imageUrl": {"maxLength": 1024, "minLength": 0, "type": "string", "description": "分类图片URL", "example": "https://example.com/category.png"}, "sortOrder": {"maximum": 9999, "minimum": 0, "type": "integer", "description": "排序权重", "format": "int32", "example": 100}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}, "topLevel": {"type": "boolean"}}, "description": "创建分类请求"}, "BatchAssignProductsRequest": {"required": ["categoryId", "mode", "productIds"], "type": "object", "properties": {"categoryId": {"type": "integer", "description": "分类ID", "format": "int32", "example": 1}, "productIds": {"type": "array", "description": "商品ID列表", "example": [1, 2, 3], "items": {"type": "integer", "description": "商品ID列表", "format": "int64"}}, "isPrimary": {"type": "boolean", "description": "是否设为主分类", "example": false}, "mode": {"type": "string", "description": "操作模式：REPLACE-替换分类下现有商品，ADD-追加商品到分类", "example": "ADD", "enum": ["REPLACE", "ADD"]}}, "description": "批量分配商品请求"}, "ResultInteger": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "integer", "format": "int32"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultListCategoryWithProductsTreeDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryWithProductsTreeDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultCategoryDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/CategoryDTO"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultListProductDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "UpdateCategoryRequest": {"required": ["name"], "type": "object", "properties": {"parentId": {"type": "integer", "description": "父分类ID", "format": "int32", "example": 1}, "name": {"maxLength": 100, "minLength": 0, "type": "string", "description": "分类名称", "example": "眼镜框架"}, "slug": {"maxLength": 100, "minLength": 0, "type": "string", "description": "URL友好标识", "example": "frames"}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "description": "分类描述", "example": "各种款式的眼镜框架"}, "imageUrl": {"maxLength": 1024, "minLength": 0, "type": "string", "description": "分类图片URL", "example": "https://example.com/category.png"}, "sortOrder": {"maximum": 9999, "minimum": 0, "type": "integer", "description": "排序权重", "format": "int32", "example": 100}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}, "topLevel": {"type": "boolean"}}, "description": "更新分类请求"}, "CategoryWithProductsTreeDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "分类ID", "format": "int32", "example": 1}, "parentId": {"type": "integer", "description": "父分类ID", "format": "int32", "example": 1}, "name": {"type": "string", "description": "分类名称", "example": "眼镜框架"}, "slug": {"type": "string", "description": "URL友好标识", "example": "frames"}, "description": {"type": "string", "description": "分类描述", "example": "各种款式的眼镜框架"}, "imageUrl": {"type": "string", "description": "分类图片URL", "example": "https://example.com/category.png"}, "level": {"type": "integer", "description": "层级", "format": "int32", "example": 1}, "path": {"type": "string", "description": "层级路径", "example": "/1"}, "sortOrder": {"type": "integer", "description": "排序权重", "format": "int32", "example": 100}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}, "productCount": {"type": "integer", "description": "商品数量", "format": "int32", "example": 10}, "products": {"type": "array", "description": "分类下的商品列表", "items": {"$ref": "#/components/schemas/ProductDTO"}}, "children": {"type": "array", "description": "子分类列表", "items": {"$ref": "#/components/schemas/CategoryWithProductsTreeDTO"}}, "productsCount": {"type": "integer", "format": "int32"}, "totalProductCount": {"type": "integer", "format": "int32"}, "childrenCount": {"type": "integer", "format": "int32"}, "topLevel": {"type": "boolean"}}, "description": "分类及关联商品树形结构"}, "ProductDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "商品ID", "format": "int64", "example": 1000}, "name": {"type": "string", "description": "商品名称", "example": "雷朋经典款太阳镜"}, "slug": {"type": "string", "description": "URL友好标识", "example": "rayban-classic-sunglasses"}, "brandId": {"type": "integer", "description": "品牌ID", "format": "int32", "example": 1}, "brandName": {"type": "string", "description": "品牌名称", "example": "雷朋"}, "shortDescription": {"type": "string", "description": "简短描述", "example": "经典款太阳镜，时尚百搭"}, "fullDescription": {"type": "string", "description": "详细描述"}, "mainImageUrl": {"type": "string", "description": "主图URL", "example": "https://example.com/product.jpg"}, "isActive": {"type": "boolean", "description": "上架状态", "example": true}, "isFeatured": {"type": "boolean", "description": "推荐状态", "example": false}, "publishedAt": {"type": "string", "description": "首次发布时间", "format": "date-time"}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}, "categories": {"type": "array", "description": "商品分类列表", "items": {"$ref": "#/components/schemas/CategoryDTO"}}, "skus": {"type": "array", "description": "商品SKU列表", "items": {"$ref": "#/components/schemas/ProductSkuDTO"}}, "minPrice": {"type": "string", "description": "最低价格", "example": "299.0"}, "maxPrice": {"type": "string", "description": "最高价格", "example": "599.0"}, "totalStock": {"type": "integer", "description": "总库存", "format": "int32", "example": 100}, "priceRange": {"type": "string"}, "published": {"type": "boolean"}}, "description": "商品信息"}}}}