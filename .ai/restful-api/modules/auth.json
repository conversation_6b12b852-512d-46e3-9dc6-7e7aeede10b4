{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 管理员登录", "description": "管理员登录相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "管理员登录", "description": "管理员登录认证相关接口"}], "paths": {"/api/admin/refresh": {"post": {"tags": ["管理员登录"], "summary": "刷新会话", "description": "刷新当前会话的过期时间", "operationId": "refreshSession", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/logout": {"post": {"tags": ["管理员登录"], "summary": "管理员登出", "description": "管理员登出，清除会话信息", "operationId": "logout_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/login": {"post": {"tags": ["管理员登录"], "summary": "管理员登录", "description": "使用邮箱和密码进行管理员登录", "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultLoginResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/force-logout/{adminId}": {"post": {"tags": ["管理员登录"], "summary": "强制登出指定管理员", "description": "强制登出指定管理员的所有会话", "operationId": "forceLogout", "parameters": [{"name": "adminId", "in": "path", "description": "管理员ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/sessions/{adminId}": {"get": {"tags": ["管理员登录"], "summary": "获取管理员活跃会话数", "description": "获取指定管理员的活跃会话数量", "operationId": "getActiveSessionCount", "parameters": [{"name": "adminId", "in": "path", "description": "管理员ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/current": {"get": {"tags": ["管理员登录"], "summary": "获取当前登录管理员信息", "description": "获取当前登录的管理员详细信息", "operationId": "getCurrentAdmin", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultAdminSession"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/check": {"get": {"tags": ["管理员登录"], "summary": "检查登录状态", "description": "检查当前登录状态是否有效", "operationId": "checkLoginStatus_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultAdminSession": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminSession"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultLoginResponse": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/LoginResponse"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultInteger": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "integer", "format": "int32"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "LoginRequest": {"required": ["email", "password"], "type": "object", "properties": {"email": {"type": "string", "description": "邮箱地址", "example": "<EMAIL>"}, "password": {"maxLength": 20, "minLength": 6, "type": "string", "description": "登录密码", "example": "123456"}, "rememberMe": {"type": "boolean", "description": "是否记住登录状态", "example": false}}, "description": "管理员登录请求"}, "AdminSession": {"type": "object", "properties": {"sessionId": {"type": "string"}, "adminId": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "realName": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "roleId": {"type": "integer", "format": "int64"}, "roleName": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string"}}, "loginTime": {"type": "string", "format": "date-time"}, "lastAccessTime": {"type": "string", "format": "date-time"}, "loginIp": {"type": "string"}, "userAgent": {"type": "string"}, "rememberMe": {"type": "boolean"}, "status": {"type": "integer", "format": "int32"}}}, "LoginResponse": {"type": "object", "properties": {"username": {"type": "string", "description": "用户名", "example": "admin"}, "realName": {"type": "string", "description": "真实姓名", "example": "系统管理员"}, "email": {"type": "string", "description": "邮箱地址", "example": "<EMAIL>"}}, "description": "管理员登录响应"}, "ResultBoolean": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "boolean"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}}}}