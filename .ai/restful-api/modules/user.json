{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 用户管理", "description": "用户管理相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "用户管理", "description": "用户相关接口"}], "paths": {"/api/auth/profile": {"put": {"tags": ["用户管理"], "summary": "更新用户信息", "description": "更新用户个人信息，包括昵称、头像等", "operationId": "updateUserInfo", "parameters": [{"name": "X-Session-Id", "in": "header", "description": "用户SessionId", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserInfoRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUpdateUserInfoResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/auth/wechat/login": {"post": {"tags": ["用户管理"], "summary": "微信小程序登录", "description": "通过微信授权登录获取用户信息和SessionId", "operationId": "wechatLogin", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserLoginResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/auth/validate-session": {"post": {"tags": ["用户管理"], "summary": "验证用户会话", "description": "验证用户SessionId是否有效", "operationId": "validateSession", "parameters": [{"name": "X-Session-Id", "in": "header", "description": "用户SessionId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserSession"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/auth/phone/get": {"post": {"tags": ["用户管理"], "summary": "获取用户手机号", "description": "通过微信授权获取用户手机号", "operationId": "getPhoneNumber", "parameters": [{"name": "X-Session-Id", "in": "header", "description": "用户SessionId", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPhoneNumberRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultGetPhoneNumberResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/auth/logout": {"post": {"tags": ["用户管理"], "summary": "用户登出", "description": "用户登出，清除会话", "operationId": "logout", "parameters": [{"name": "X-Session-Id", "in": "header", "description": "用户SessionId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/auth/info/{userId}": {"get": {"tags": ["用户管理"], "summary": "获取用户信息", "description": "根据用户ID获取用户详细信息", "operationId": "getUserInfo", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUser"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "UpdateUserInfoRequest": {"type": "object", "properties": {"nickname": {"maxLength": 50, "minLength": 0, "type": "string", "description": "用户昵称", "example": "张三"}, "avatarUrl": {"maxLength": 500, "minLength": 0, "type": "string", "description": "用户头像URL", "example": "https://example.com/avatar.jpg"}, "gender": {"type": "integer", "description": "性别：0-未知, 1-男, 2-女", "format": "int32", "example": 1}, "country": {"maxLength": 50, "minLength": 0, "type": "string", "description": "所在国家", "example": "中国"}, "province": {"maxLength": 50, "minLength": 0, "type": "string", "description": "所在省份", "example": "广东省"}, "city": {"maxLength": 50, "minLength": 0, "type": "string", "description": "所在城市", "example": "深圳市"}}, "description": "更新用户信息请求"}, "ResultUserLoginResponse": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/UserLoginResponse"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultGetPhoneNumberResponse": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/GetPhoneNumberResponse"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "UserLoginResponse": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户ID", "format": "int64", "example": 100001}, "sessionId": {"type": "string", "description": "用户会话ID", "example": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"}, "sessionExpireTime": {"type": "string", "description": "会话过期时间", "format": "date-time"}, "isNewUser": {"type": "boolean", "description": "是否为新用户", "example": false}, "userInfo": {"$ref": "#/components/schemas/UserBasicInfo"}}, "description": "用户登录响应"}, "UserInfo": {"type": "object", "properties": {"nickName": {"type": "string", "description": "用户昵称", "example": "微信用户"}, "avatarUrl": {"type": "string", "description": "用户头像URL", "example": "https://thirdwx.qlogo.cn/mmopen/..."}, "gender": {"type": "integer", "description": "用户性别：0-未知, 1-男, 2-女", "format": "int32", "example": 1}, "country": {"type": "string", "description": "用户所在国家", "example": "China"}, "province": {"type": "string", "description": "用户所在省份", "example": "Guangdong"}, "city": {"type": "string", "description": "用户所在城市", "example": "Guangzhou"}}, "description": "用户信息"}, "ResultUserSession": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/UserSession"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "UpdateUserInfoResponse": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户ID", "format": "int64", "example": 1}, "nickname": {"type": "string", "description": "用户昵称", "example": "张三"}, "avatarUrl": {"type": "string", "description": "用户头像URL", "example": "https://example.com/avatar.jpg"}, "gender": {"type": "integer", "description": "性别：0-未知, 1-男, 2-女", "format": "int32", "example": 1}, "country": {"type": "string", "description": "所在国家", "example": "中国"}, "province": {"type": "string", "description": "所在省份", "example": "广东省"}, "city": {"type": "string", "description": "所在城市", "example": "深圳市"}, "updatedAt": {"type": "string", "description": "更新时间", "format": "date-time"}, "success": {"type": "boolean", "description": "操作是否成功", "example": true}, "message": {"type": "string", "description": "操作结果消息", "example": "用户信息更新成功"}}, "description": "更新用户信息响应"}, "UserBasicInfo": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户ID", "format": "int64", "example": 100001}, "nickname": {"type": "string", "description": "用户昵称", "example": "微信用户"}, "avatarUrl": {"type": "string", "description": "用户头像URL", "example": "https://thirdwx.qlogo.cn/mmopen/..."}, "gender": {"type": "integer", "description": "用户性别：0-未知, 1-男, 2-女", "format": "int32", "example": 1}, "phoneNumber": {"type": "string", "description": "手机号", "example": "13800138000"}, "hasPhoneNumber": {"type": "boolean", "description": "是否绑定手机号", "example": true}, "createdAt": {"type": "string", "description": "注册时间", "format": "date-time"}}, "description": "用户基本信息"}, "GetPhoneNumberResponse": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户ID", "format": "int64", "example": 100001}, "phoneNumber": {"type": "string", "description": "手机号（带区号）", "example": "+8613800138000"}, "purePhoneNumber": {"type": "string", "description": "纯手机号（不带区号）", "example": "13800138000"}, "countryCode": {"type": "string", "description": "区号", "example": "86"}, "success": {"type": "boolean", "description": "是否成功获取", "example": true}, "message": {"type": "string", "description": "消息", "example": "手机号获取成功"}}, "description": "获取手机号响应"}, "User": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "wechatOpenid": {"type": "string"}, "wechatUnionid": {"type": "string"}, "wechatSessionKey": {"type": "string"}, "nickname": {"type": "string"}, "avatarUrl": {"type": "string"}, "gender": {"type": "integer", "format": "int32"}, "country": {"type": "string"}, "province": {"type": "string"}, "city": {"type": "string"}, "phoneNumber": {"type": "string"}, "lastLoginAt": {"type": "string", "format": "date-time"}, "isDeleted": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "UserLoginRequest": {"required": ["code"], "type": "object", "properties": {"code": {"type": "string", "description": "微信登录凭证code", "example": "081Kq4Ga1MSox22OLHGa1Q3w5r2Kq4Gd"}, "userInfo": {"$ref": "#/components/schemas/UserInfo"}}, "description": "登录请求"}, "ResultUser": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/User"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "UserSession": {"type": "object", "properties": {"sessionId": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}, "wechatOpenid": {"type": "string"}, "wechatUnionid": {"type": "string"}, "nickname": {"type": "string"}, "avatarUrl": {"type": "string"}, "gender": {"type": "integer", "format": "int32"}, "phoneNumber": {"type": "string"}, "country": {"type": "string"}, "province": {"type": "string"}, "city": {"type": "string"}, "loginTime": {"type": "string", "format": "date-time"}, "lastAccessTime": {"type": "string", "format": "date-time"}, "loginIp": {"type": "string"}, "userAgent": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "isNewUser": {"type": "boolean"}}}, "ResultUpdateUserInfoResponse": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/UpdateUserInfoResponse"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "GetPhoneNumberRequest": {"required": ["code"], "type": "object", "properties": {"code": {"type": "string", "description": "手机号获取凭证code", "example": "081Kq4Ga1MSox22OLHGa1Q3w5r2Kq4Gd"}}, "description": "获取手机号请求"}}}}