{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 用户支付", "description": "用户支付相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "用户支付", "description": "用户支付相关接口"}], "paths": {"/api/user/payment/create": {"post": {"tags": ["用户支付"], "summary": "创建支付订单", "description": "为订单创建微信支付", "operationId": "createPayment", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultMiniProgramPayRequest"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/payment/status/{orderNo}": {"get": {"tags": ["用户支付"], "summary": "查询支付状态", "description": "查询订单支付状态", "operationId": "queryPaymentStatus", "parameters": [{"name": "orderNo", "in": "path", "description": "订单号", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "CreatePaymentRequest": {"required": ["description", "openId", "orderNo", "totalAmount"], "type": "object", "properties": {"orderNo": {"type": "string"}, "openId": {"type": "string"}, "totalAmount": {"type": "integer", "format": "int32"}, "description": {"type": "string"}, "attach": {"type": "string"}, "clientIp": {"type": "string"}}, "description": "创建支付请求"}, "ResultMiniProgramPayRequest": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/MiniProgramPayRequest"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultString": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "string"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "MiniProgramPayRequest": {"type": "object", "properties": {"timeStamp": {"type": "string"}, "nonceStr": {"type": "string"}, "packageValue": {"type": "string"}, "signType": {"type": "string"}, "paySign": {"type": "string"}}}}}}