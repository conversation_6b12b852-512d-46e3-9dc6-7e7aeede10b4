{"openapi": "3.0.1", "info": {"title": "管理员订单管理API", "description": "眼镜商城管理员订单管理相关接口，包括订单查看、状态更新、发货、取消、统计和导出等功能", "version": "1.0.0", "contact": {"name": "眼镜商城开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "http://localhost:8080", "description": "本地开发环境"}, {"url": "https://api.glasses-merchant.com", "description": "生产环境"}], "tags": [{"name": "管理员订单管理", "description": "管理员订单管理相关接口，包括查询、状态更新、发货、取消、统计和导出功能"}], "paths": {"/api/admin/orders": {"get": {"tags": ["管理员订单管理"], "summary": "分页查询订单列表", "description": "管理员分页查询订单列表，支持多条件筛选", "operationId": "getOrderList", "security": [{"AdminAuth": []}], "parameters": [{"name": "orderNumber", "in": "query", "description": "订单号（模糊查询）", "required": false, "schema": {"type": "string", "example": "ORD202501051234"}}, {"name": "status", "in": "query", "description": "订单状态", "required": false, "schema": {"type": "string", "enum": ["pending_payment", "paid", "processing", "shipped", "delivered", "completed", "cancelled", "refunded"], "example": "paid"}}, {"name": "userId", "in": "query", "description": "用户ID", "required": false, "schema": {"type": "integer", "format": "int64", "example": 1001}}, {"name": "paymentMethod", "in": "query", "description": "支付方式", "required": false, "schema": {"type": "string", "example": "wechat_pay"}}, {"name": "startDate", "in": "query", "description": "开始日期（下单时间）", "required": false, "schema": {"type": "string", "format": "date", "example": "2025-01-01"}}, {"name": "endDate", "in": "query", "description": "结束日期（下单时间）", "required": false, "schema": {"type": "string", "format": "date", "example": "2025-01-31"}}, {"name": "minAmount", "in": "query", "description": "最小订单金额", "required": false, "schema": {"type": "number", "format": "decimal", "example": 100.0}}, {"name": "maxAmount", "in": "query", "description": "最大订单金额", "required": false, "schema": {"type": "number", "format": "decimal", "example": 1000.0}}, {"name": "page", "in": "query", "description": "页码（从1开始）", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "pageSize", "in": "query", "description": "每页大小", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "sortBy", "in": "query", "description": "排序字段", "required": false, "schema": {"type": "string", "enum": ["placedAt", "totalAmount", "updatedAt"], "default": "placedAt", "example": "placedAt"}}, {"name": "sortOrder", "in": "query", "description": "排序方向", "required": false, "schema": {"type": "string", "enum": ["asc", "desc"], "default": "desc", "example": "desc"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResultAdminOrderListDTO"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}, "401": {"description": "未授权", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}}}}, "/api/admin/orders/{orderId}": {"get": {"tags": ["管理员订单管理"], "summary": "获取订单详情", "description": "根据订单ID获取订单详情", "operationId": "getOrderDetail", "security": [{"AdminAuth": []}], "parameters": [{"name": "orderId", "in": "path", "description": "订单ID", "required": true, "schema": {"type": "integer", "format": "int64", "example": 1001}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultAdminOrderDetailDTO"}}}}, "404": {"description": "订单不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}}}}, "/api/admin/orders/by-number/{orderNumber}": {"get": {"tags": ["管理员订单管理"], "summary": "根据订单号获取订单详情", "description": "根据订单号获取订单详情", "operationId": "getOrderDetailByNumber", "security": [{"AdminAuth": []}], "parameters": [{"name": "orderNumber", "in": "path", "description": "订单号", "required": true, "schema": {"type": "string", "example": "ORD202501051234567890"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultAdminOrderDetailDTO"}}}}, "404": {"description": "订单不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}}}}, "/api/admin/orders/{orderId}/status": {"put": {"tags": ["管理员订单管理"], "summary": "更新订单状态", "description": "管理员更新订单状态", "operationId": "updateOrderStatus", "security": [{"AdminAuth": []}], "parameters": [{"name": "orderId", "in": "path", "description": "订单ID", "required": true, "schema": {"type": "integer", "format": "int64", "example": 1001}}], "requestBody": {"description": "状态更新请求", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderStatusRequest"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}, "404": {"description": "订单不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}}}}, "/api/admin/orders/{orderId}/ship": {"post": {"tags": ["管理员订单管理"], "summary": "订单发货", "description": "管理员为订单安排发货", "operationId": "shipOrder", "security": [{"AdminAuth": []}], "parameters": [{"name": "orderId", "in": "path", "description": "订单ID", "required": true, "schema": {"type": "integer", "format": "int64", "example": 1001}}], "requestBody": {"description": "发货请求", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipOrderRequest"}}}}, "responses": {"200": {"description": "发货成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "请求参数错误或订单状态不允许发货", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}, "404": {"description": "订单不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}}}}, "/api/admin/orders/{orderId}/cancel": {"post": {"tags": ["管理员订单管理"], "summary": "取消订单", "description": "管理员取消订单", "operationId": "cancelOrder", "security": [{"AdminAuth": []}], "parameters": [{"name": "orderId", "in": "path", "description": "订单ID", "required": true, "schema": {"type": "integer", "format": "int64", "example": 1001}}], "requestBody": {"description": "取消请求", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelOrderRequest"}}}}, "responses": {"200": {"description": "取消成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "请求参数错误或订单状态不允许取消", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}, "404": {"description": "订单不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}}}}, "/api/admin/orders/{orderId}/notes": {"put": {"tags": ["管理员订单管理"], "summary": "更新订单备注", "description": "管理员更新订单备注", "operationId": "updateOrderNotes", "security": [{"AdminAuth": []}], "parameters": [{"name": "orderId", "in": "path", "description": "订单ID", "required": true, "schema": {"type": "integer", "format": "int64", "example": 1001}}], "requestBody": {"description": "备注更新请求", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderNotesRequest"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}, "404": {"description": "订单不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}}}}, "/api/admin/orders/statistics": {"get": {"tags": ["管理员订单管理"], "summary": "获取订单统计", "description": "获取指定时间范围内的订单统计信息", "operationId": "getOrderStatistics", "security": [{"AdminAuth": []}], "parameters": [{"name": "startDate", "in": "query", "description": "开始日期", "required": false, "schema": {"type": "string", "format": "date", "example": "2025-01-01"}}, {"name": "endDate", "in": "query", "description": "结束日期", "required": false, "schema": {"type": "string", "format": "date", "example": "2025-01-31"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOrderStatisticsDTO"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}}}}, "/api/admin/orders/export": {"post": {"tags": ["管理员订单管理"], "summary": "导出订单数据", "description": "根据查询条件导出订单数据", "operationId": "exportOrders", "security": [{"AdminAuth": []}], "requestBody": {"description": "导出查询条件", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminOrderPageQuery"}}}}, "responses": {"200": {"description": "导出成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResult"}}}}}}}}, "components": {"schemas": {"AdminOrderPageQuery": {"type": "object", "description": "管理员订单分页查询参数", "properties": {"orderNumber": {"type": "string", "description": "订单号（模糊查询）", "example": "ORD202501051234"}, "status": {"type": "string", "description": "订单状态", "enum": ["pending_payment", "paid", "processing", "shipped", "delivered", "completed", "cancelled", "refunded"], "example": "paid"}, "userId": {"type": "integer", "format": "int64", "description": "用户ID", "example": 1001}, "paymentMethod": {"type": "string", "description": "支付方式", "example": "wechat_pay"}, "startDate": {"type": "string", "format": "date", "description": "开始日期（下单时间）", "example": "2025-01-01"}, "endDate": {"type": "string", "format": "date", "description": "结束日期（下单时间）", "example": "2025-01-31"}, "minAmount": {"type": "number", "format": "decimal", "description": "最小订单金额", "example": 100.0}, "maxAmount": {"type": "number", "format": "decimal", "description": "最大订单金额", "example": 1000.0}, "page": {"type": "integer", "minimum": 1, "default": 1, "description": "页码（从1开始）", "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "description": "每页大小", "example": 10}, "sortBy": {"type": "string", "enum": ["placedAt", "totalAmount", "updatedAt"], "default": "placedAt", "description": "排序字段", "example": "placedAt"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"], "default": "desc", "description": "排序方向", "example": "desc"}}}, "AdminOrderListDTO": {"type": "object", "description": "管理员订单列表DTO", "properties": {"id": {"type": "integer", "format": "int64", "description": "订单ID", "example": 1001}, "orderNumber": {"type": "string", "description": "订单号", "example": "ORD202501051234567890"}, "userId": {"type": "integer", "format": "int64", "description": "用户ID", "example": 2001}, "username": {"type": "string", "description": "用户名", "example": "user123"}, "userPhone": {"type": "string", "description": "用户手机号", "example": "13800138000"}, "status": {"type": "string", "description": "订单状态", "example": "paid"}, "statusDescription": {"type": "string", "description": "订单状态描述", "example": "已支付"}, "totalAmount": {"type": "number", "format": "decimal", "description": "订单总额", "example": 299.0}, "itemsTotalAmount": {"type": "number", "format": "decimal", "description": "商品总价", "example": 279.0}, "discountAmount": {"type": "number", "format": "decimal", "description": "优惠金额", "example": 0.0}, "shippingFee": {"type": "number", "format": "decimal", "description": "运费", "example": 20.0}, "paymentMethod": {"type": "string", "description": "支付方式", "example": "wechat_pay"}, "itemCount": {"type": "integer", "description": "订单项数量", "example": 2}, "firstProductName": {"type": "string", "description": "第一个商品名称", "example": "时尚近视眼镜框"}, "recipientName": {"type": "string", "description": "收货人姓名", "example": "张三"}, "recipientPhone": {"type": "string", "description": "收货人电话", "example": "13900139000"}, "placedAt": {"type": "string", "format": "date-time", "description": "下单时间", "example": "2025-01-05T10:30:00"}, "paidAt": {"type": "string", "format": "date-time", "description": "支付时间", "example": "2025-01-05T10:35:00", "nullable": true}, "shippedAt": {"type": "string", "format": "date-time", "description": "发货时间", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "description": "最后更新时间", "example": "2025-01-05T10:35:00"}}}, "AdminOrderDetailDTO": {"type": "object", "description": "管理员订单详情DTO", "properties": {"id": {"type": "integer", "format": "int64", "description": "订单ID", "example": 1001}, "userId": {"type": "integer", "format": "int64", "description": "用户ID", "example": 2001}, "username": {"type": "string", "description": "用户名", "example": "user123"}, "userPhone": {"type": "string", "description": "用户手机号", "example": "13800138000"}, "userEmail": {"type": "string", "description": "用户邮箱", "example": "<EMAIL>"}, "orderNumber": {"type": "string", "description": "订单号", "example": "ORD202501051234567890"}, "status": {"type": "string", "description": "订单状态", "example": "paid"}, "statusDescription": {"type": "string", "description": "订单状态描述", "example": "已支付"}, "totalAmount": {"type": "number", "format": "decimal", "description": "订单总额", "example": 299.0}, "itemsTotalAmount": {"type": "number", "format": "decimal", "description": "商品总价", "example": 279.0}, "discountAmount": {"type": "number", "format": "decimal", "description": "优惠金额", "example": 0.0}, "shippingFee": {"type": "number", "format": "decimal", "description": "运费", "example": 20.0}, "paymentMethod": {"type": "string", "description": "支付方式", "example": "wechat_pay"}, "notesToSeller": {"type": "string", "description": "用户留言", "example": "请尽快发货"}, "adminNotes": {"type": "string", "description": "管理员备注", "example": "客户要求加急处理"}, "shippingAddress": {"$ref": "#/components/schemas/AddressSnapshotDTO"}, "billingAddress": {"$ref": "#/components/schemas/AddressSnapshotDTO"}, "items": {"type": "array", "description": "订单项列表", "items": {"$ref": "#/components/schemas/OrderItemDetailDTO"}}, "placedAt": {"type": "string", "format": "date-time", "description": "下单时间", "example": "2025-01-05T10:30:00"}, "paidAt": {"type": "string", "format": "date-time", "description": "支付时间", "example": "2025-01-05T10:35:00", "nullable": true}, "shippedAt": {"type": "string", "format": "date-time", "description": "发货时间", "nullable": true}, "deliveredAt": {"type": "string", "format": "date-time", "description": "签收时间", "nullable": true}}}, "UpdateOrderStatusRequest": {"type": "object", "description": "更新订单状态请求", "required": ["status"], "properties": {"status": {"type": "string", "description": "新的订单状态", "enum": ["paid", "processing", "shipped", "delivered", "completed", "cancelled", "refunded"], "example": "processing"}, "reason": {"type": "string", "description": "状态变更原因", "maxLength": 500, "example": "客户已付款，开始处理订单"}}}, "ShipOrderRequest": {"type": "object", "description": "订单发货请求", "required": ["shippingCompany", "trackingNumber"], "properties": {"shippingCompany": {"type": "string", "description": "物流公司", "maxLength": 100, "example": "顺丰速运"}, "trackingNumber": {"type": "string", "description": "物流单号", "maxLength": 100, "example": "SF1234567890123"}, "shippingNotes": {"type": "string", "description": "发货备注", "maxLength": 500, "example": "已包装完毕，预计2-3天到达"}}}, "CancelOrderRequest": {"type": "object", "description": "取消订单请求", "required": ["reason"], "properties": {"reason": {"type": "string", "description": "取消原因", "maxLength": 500, "example": "库存不足，无法发货"}, "refundAmount": {"type": "number", "format": "decimal", "description": "退款金额（如果已支付）", "example": 299.0}, "notifyUser": {"type": "boolean", "description": "是否通知用户", "default": true, "example": true}}}, "UpdateOrderNotesRequest": {"type": "object", "description": "更新订单备注请求", "required": ["adminNotes"], "properties": {"adminNotes": {"type": "string", "description": "管理员备注", "maxLength": 1000, "example": "客户要求加急处理，已安排优先发货"}}}, "OrderStatisticsDTO": {"type": "object", "description": "订单统计DTO", "properties": {"totalOrders": {"type": "integer", "description": "总订单数", "example": 1250}, "totalAmount": {"type": "number", "format": "decimal", "description": "总订单金额", "example": 125000.0}, "statusCounts": {"type": "object", "description": "各状态订单数量", "additionalProperties": {"type": "integer"}, "example": {"pending_payment": 50, "paid": 200, "processing": 100, "shipped": 300, "delivered": 400, "completed": 150, "cancelled": 30, "refunded": 20}}, "statusAmounts": {"type": "object", "description": "各状态订单金额", "additionalProperties": {"type": "number", "format": "decimal"}, "example": {"pending_payment": 5000.0, "paid": 20000.0, "processing": 10000.0, "shipped": 30000.0, "delivered": 40000.0, "completed": 15000.0, "cancelled": 3000.0, "refunded": 2000.0}}, "dailyStats": {"type": "array", "description": "每日统计数据", "items": {"$ref": "#/components/schemas/DailyStatistics"}}}}, "DailyStatistics": {"type": "object", "description": "每日统计数据", "properties": {"date": {"type": "string", "format": "date", "description": "日期", "example": "2025-01-05"}, "orderCount": {"type": "integer", "description": "当日订单数", "example": 25}, "orderAmount": {"type": "number", "format": "decimal", "description": "当日订单金额", "example": 2500.0}}}, "AddressSnapshotDTO": {"type": "object", "description": "地址快照DTO", "properties": {"recipientName": {"type": "string", "description": "收货人姓名", "example": "张三"}, "recipientPhone": {"type": "string", "description": "收货人电话", "example": "13900139000"}, "province": {"type": "string", "description": "省份", "example": "广东省"}, "city": {"type": "string", "description": "城市", "example": "深圳市"}, "district": {"type": "string", "description": "区县", "example": "南山区"}, "detailAddress": {"type": "string", "description": "详细地址", "example": "科技园南区深圳湾科技生态园"}, "postalCode": {"type": "string", "description": "邮政编码", "example": "518000"}}}, "OrderItemDetailDTO": {"type": "object", "description": "订单项详情DTO", "properties": {"id": {"type": "integer", "format": "int64", "description": "订单项ID", "example": 3001}, "orderId": {"type": "integer", "format": "int64", "description": "订单ID", "example": 1001}, "skuId": {"type": "integer", "format": "int64", "description": "SKU ID", "example": 10001}, "productId": {"type": "integer", "format": "int64", "description": "商品ID", "example": 5001}, "productNameSnapshot": {"type": "string", "description": "下单时商品名称（快照）", "example": "时尚近视眼镜框"}, "quantity": {"type": "integer", "description": "购买数量", "example": 2}, "unitPriceAtPurchase": {"type": "number", "format": "decimal", "description": "下单时单价", "example": 149.5}, "totalPrice": {"type": "number", "format": "decimal", "description": "商品总价（单价×数量）", "example": 299.0}, "customizationType": {"type": "string", "description": "定制类型（如：glasses_prescription）", "example": "glasses_prescription"}, "customizationId": {"type": "integer", "format": "int64", "description": "关联定制信息ID（如处方ID）", "example": 2001}}}, "PageResultAdminOrderListDTO": {"type": "object", "description": "分页结果包装器", "properties": {"code": {"type": "string", "description": "响应码", "example": "SUCCESS"}, "message": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"$ref": "#/components/schemas/PageResult"}, "timestamp": {"type": "integer", "format": "int64", "description": "时间戳", "example": 1704441600000}, "success": {"type": "boolean", "description": "是否成功", "example": true}}}, "PageResult": {"type": "object", "description": "分页结果", "properties": {"records": {"type": "array", "description": "数据列表", "items": {"$ref": "#/components/schemas/AdminOrderListDTO"}}, "total": {"type": "integer", "format": "int64", "description": "总记录数", "example": 100}, "pageNum": {"type": "integer", "description": "当前页码", "example": 1}, "pageSize": {"type": "integer", "description": "每页大小", "example": 10}, "totalPages": {"type": "integer", "description": "总页数", "example": 10}, "hasNext": {"type": "boolean", "description": "是否有下一页", "example": true}, "hasPrevious": {"type": "boolean", "description": "是否有上一页", "example": false}, "empty": {"type": "boolean", "description": "是否为空", "example": false}, "size": {"type": "integer", "description": "当前页记录数", "example": 10}}}, "ResultAdminOrderDetailDTO": {"type": "object", "description": "订单详情响应", "properties": {"code": {"type": "string", "description": "响应码", "example": "SUCCESS"}, "message": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"$ref": "#/components/schemas/AdminOrderDetailDTO"}, "timestamp": {"type": "integer", "format": "int64", "description": "时间戳", "example": 1704441600000}, "success": {"type": "boolean", "description": "是否成功", "example": true}}}, "ResultOrderStatisticsDTO": {"type": "object", "description": "订单统计响应", "properties": {"code": {"type": "string", "description": "响应码", "example": "SUCCESS"}, "message": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"$ref": "#/components/schemas/OrderStatisticsDTO"}, "timestamp": {"type": "integer", "format": "int64", "description": "时间戳", "example": 1704441600000}, "success": {"type": "boolean", "description": "是否成功", "example": true}}}, "ResultString": {"type": "object", "description": "字符串响应", "properties": {"code": {"type": "string", "description": "响应码", "example": "SUCCESS"}, "message": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"type": "string", "description": "导出文件路径或下载链接", "example": "/exports/orders_20250105_103000.xlsx"}, "timestamp": {"type": "integer", "format": "int64", "description": "时间戳", "example": 1704441600000}, "success": {"type": "boolean", "description": "是否成功", "example": true}}}, "ResultVoid": {"type": "object", "description": "无数据响应", "properties": {"code": {"type": "string", "description": "响应码", "example": "SUCCESS"}, "message": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"type": "object", "nullable": true, "description": "数据（为空）"}, "timestamp": {"type": "integer", "format": "int64", "description": "时间戳", "example": 1704441600000}, "success": {"type": "boolean", "description": "是否成功", "example": true}}}, "ErrorResult": {"type": "object", "description": "错误响应", "properties": {"code": {"type": "string", "description": "错误码", "example": "VALIDATION_ERROR"}, "message": {"type": "string", "description": "错误消息", "example": "请求参数验证失败"}, "data": {"type": "object", "nullable": true, "description": "错误详情"}, "timestamp": {"type": "integer", "format": "int64", "description": "时间戳", "example": 1704441600000}, "success": {"type": "boolean", "description": "是否成功", "example": false}}}}, "securitySchemes": {"AdminAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "管理员JWT认证，需要在请求头中添加 Authorization: Bearer <token>"}}}}