{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 商品分类关联管理", "description": "商品分类关联管理相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "商品分类关联管理", "description": "商品分类关联相关接口"}], "paths": {"/api/product-categories/primary": {"put": {"tags": ["商品分类关联管理"], "summary": "设置主分类", "description": "设置商品的主分类", "operationId": "setPrimaryCategory", "parameters": [{"name": "productId", "in": "query", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "categoryId", "in": "query", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/batch/assign-products": {"post": {"tags": ["商品分类关联管理"], "summary": "批量为分类分配商品", "description": "批量为分类分配多个商品", "operationId": "batchAssignProductsToCategory", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchAssignProductsRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/batch/assign-categories": {"post": {"tags": ["商品分类关联管理"], "summary": "批量为商品分配分类", "description": "批量为多个商品分配分类", "operationId": "batchAssignCategoriesToProducts_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchAssignCategoriesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/assign": {"post": {"tags": ["商品分类关联管理"], "summary": "为商品分配分类", "description": "为单个商品分配一个或多个分类", "operationId": "assignCategoriesToProduct", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCategoryAssignRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/product/{productId}/primary-category": {"get": {"tags": ["商品分类关联管理"], "summary": "获取商品主分类", "description": "获取商品的主分类信息", "operationId": "getPrimaryCategoryByProductId_1", "parameters": [{"name": "productId", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/product/{productId}/mappings": {"get": {"tags": ["商品分类关联管理"], "summary": "获取商品分类映射详情", "description": "获取商品的分类映射详细信息", "operationId": "getProductCategoryMappings", "parameters": [{"name": "productId", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductCategoryMappingDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/product/{productId}/count": {"get": {"tags": ["商品分类关联管理"], "summary": "统计商品分类数量", "description": "统计指定商品的分类数量", "operationId": "countCategoriesByProductId", "parameters": [{"name": "productId", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/product/{productId}/categories": {"get": {"tags": ["商品分类关联管理"], "summary": "获取商品的分类列表", "description": "获取指定商品关联的所有分类", "operationId": "getCategoriesByProductId_1", "parameters": [{"name": "productId", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/exists": {"get": {"tags": ["商品分类关联管理"], "summary": "检查商品分类关联", "description": "检查商品分类关联是否存在", "operationId": "existsProductCategoryMapping", "parameters": [{"name": "productId", "in": "query", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "categoryId", "in": "query", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/category/{categoryId}/products": {"get": {"tags": ["商品分类关联管理"], "summary": "获取分类下的商品列表", "description": "获取指定分类下的所有商品", "operationId": "getProductsByCategoryId_1", "parameters": [{"name": "categoryId", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/category/{categoryId}/mappings": {"get": {"tags": ["商品分类关联管理"], "summary": "获取分类商品映射详情", "description": "获取分类的商品映射详细信息", "operationId": "getCategoryProductMappings", "parameters": [{"name": "categoryId", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductCategoryMappingDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/category/{categoryId}/count": {"get": {"tags": ["商品分类关联管理"], "summary": "统计分类商品数量", "description": "统计指定分类下的商品数量", "operationId": "countProductsByCategoryId", "parameters": [{"name": "categoryId", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/remove": {"delete": {"tags": ["商品分类关联管理"], "summary": "移除商品分类关联", "description": "移除商品的特定分类关联", "operationId": "removeProductFromCategory", "parameters": [{"name": "productId", "in": "query", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "categoryId", "in": "query", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/remove-all/{productId}": {"delete": {"tags": ["商品分类关联管理"], "summary": "移除商品所有分类", "description": "移除商品的所有分类关联", "operationId": "removeAllCategoriesFromProduct", "parameters": [{"name": "productId", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/batch/remove-products/{categoryId}": {"delete": {"tags": ["商品分类关联管理"], "summary": "批量移除分类下的商品", "description": "批量移除分类下的多个商品", "operationId": "batchRemoveProductsFromCategory", "parameters": [{"name": "categoryId", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "商品ID列表", "items": {"type": "integer", "format": "int64"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-categories/batch/remove-products-categories": {"delete": {"tags": ["商品分类关联管理"], "summary": "批量移除商品分类关联", "description": "批量移除多个商品的多个分类关联", "operationId": "batchRemoveProductsFromCategories", "parameters": [{"name": "productIds", "in": "query", "description": "商品ID列表", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "categoryIds", "in": "query", "description": "分类ID列表", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ProductCategoryAssignRequest": {"required": ["categoryIds", "productId"], "type": "object", "properties": {"productId": {"type": "integer", "description": "商品ID", "format": "int64", "example": 1}, "categoryIds": {"type": "array", "description": "分类ID列表", "example": [1, 2], "items": {"type": "integer", "description": "分类ID列表", "format": "int32"}}, "primaryCategoryId": {"type": "integer", "description": "主分类ID（必须在分类ID列表中）", "format": "int32", "example": 1}, "validPrimaryCategory": {"type": "boolean"}}, "description": "商品分类关联请求"}, "BatchAssignCategoriesRequest": {"required": ["categoryIds", "mode", "productIds"], "type": "object", "properties": {"productIds": {"type": "array", "description": "商品ID列表", "example": [1, 2, 3], "items": {"type": "integer", "description": "商品ID列表", "format": "int64"}}, "categoryIds": {"type": "array", "description": "分类ID列表", "example": [1, 2], "items": {"type": "integer", "description": "分类ID列表", "format": "int32"}}, "primaryCategoryId": {"type": "integer", "description": "主分类ID（必须在分类ID列表中）", "format": "int32", "example": 1}, "mode": {"type": "string", "description": "操作模式：REPLACE-替换现有分类，ADD-追加分类", "example": "REPLACE", "enum": ["REPLACE", "ADD"]}, "validPrimaryCategory": {"type": "boolean"}}, "description": "批量分配分类请求"}, "CategoryDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "分类ID", "format": "int32", "example": 1}, "parentId": {"type": "integer", "description": "父分类ID", "format": "int32", "example": 1}, "name": {"type": "string", "description": "分类名称", "example": "眼镜框架"}, "slug": {"type": "string", "description": "URL友好标识", "example": "frames"}, "description": {"type": "string", "description": "分类描述", "example": "各种款式的眼镜框架"}, "imageUrl": {"type": "string", "description": "分类图片URL", "example": "https://example.com/category.png"}, "level": {"type": "integer", "description": "层级", "format": "int32", "example": 1}, "path": {"type": "string", "description": "层级路径", "example": "/1"}, "sortOrder": {"type": "integer", "description": "排序权重", "format": "int32", "example": 100}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}, "children": {"type": "array", "description": "子分类列表", "items": {"$ref": "#/components/schemas/CategoryDTO"}}, "productCount": {"type": "integer", "description": "商品数量", "format": "int32", "example": 10}, "topLevel": {"type": "boolean"}}, "description": "分类信息"}, "ResultListProductCategoryMappingDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductCategoryMappingDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultListCategoryDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ProductSkuDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "SKU ID", "format": "int64", "example": 10000}, "productId": {"type": "integer", "description": "商品ID", "format": "int64", "example": 1000}, "skuCode": {"type": "string", "description": "SKU编码", "example": "RB-CLASSIC-BLACK-M"}, "nameExtension": {"type": "string", "description": "SKU名称后缀", "example": "黑色 中号"}, "price": {"type": "number", "description": "销售价", "example": 399.0}, "originalPrice": {"type": "number", "description": "原价", "example": 499.0}, "stockQuantity": {"type": "integer", "description": "库存数量", "format": "int32", "example": 50}, "lowStockThreshold": {"type": "integer", "description": "低库存预警值", "format": "int32", "example": 10}, "weightKg": {"type": "number", "description": "重量", "example": 0.15}, "dimensionsCm": {"type": "string", "description": "尺寸", "example": "14x5x3"}, "imageUrl": {"type": "string", "description": "SKU专属图片", "example": "https://example.com/sku.jpg"}, "attributes": {"type": "object", "additionalProperties": {"type": "string", "description": "规格属性"}, "description": "规格属性"}, "attributesJson": {"type": "string", "description": "规格属性JSON", "example": "{\"color\":\"黑色\",\"size\":\"中号\"}"}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}, "productName": {"type": "string", "description": "商品名称", "example": "雷朋经典款太阳镜"}, "discountRate": {"type": "number"}, "lowStock": {"type": "boolean"}, "fullName": {"type": "string"}}, "description": "商品SKU信息"}, "ProductCategoryMappingDTO": {"type": "object", "properties": {"productId": {"type": "integer", "description": "商品ID", "format": "int64", "example": 1}, "productName": {"type": "string", "description": "商品名称", "example": "时尚眼镜"}, "categoryId": {"type": "integer", "description": "分类ID", "format": "int32", "example": 1}, "categoryName": {"type": "string", "description": "分类名称", "example": "太阳镜"}, "isPrimary": {"type": "boolean", "description": "是否主分类", "example": true}, "createdAt": {"type": "string", "description": "关联创建时间", "format": "date-time"}}, "description": "商品分类映射信息"}, "BatchAssignProductsRequest": {"required": ["categoryId", "mode", "productIds"], "type": "object", "properties": {"categoryId": {"type": "integer", "description": "分类ID", "format": "int32", "example": 1}, "productIds": {"type": "array", "description": "商品ID列表", "example": [1, 2, 3], "items": {"type": "integer", "description": "商品ID列表", "format": "int64"}}, "isPrimary": {"type": "boolean", "description": "是否设为主分类", "example": false}, "mode": {"type": "string", "description": "操作模式：REPLACE-替换分类下现有商品，ADD-追加商品到分类", "example": "ADD", "enum": ["REPLACE", "ADD"]}}, "description": "批量分配商品请求"}, "ResultInteger": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "integer", "format": "int32"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultCategoryDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/CategoryDTO"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultListProductDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultBoolean": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "boolean"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ProductDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "商品ID", "format": "int64", "example": 1000}, "name": {"type": "string", "description": "商品名称", "example": "雷朋经典款太阳镜"}, "slug": {"type": "string", "description": "URL友好标识", "example": "rayban-classic-sunglasses"}, "brandId": {"type": "integer", "description": "品牌ID", "format": "int32", "example": 1}, "brandName": {"type": "string", "description": "品牌名称", "example": "雷朋"}, "shortDescription": {"type": "string", "description": "简短描述", "example": "经典款太阳镜，时尚百搭"}, "fullDescription": {"type": "string", "description": "详细描述"}, "mainImageUrl": {"type": "string", "description": "主图URL", "example": "https://example.com/product.jpg"}, "isActive": {"type": "boolean", "description": "上架状态", "example": true}, "isFeatured": {"type": "boolean", "description": "推荐状态", "example": false}, "publishedAt": {"type": "string", "description": "首次发布时间", "format": "date-time"}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}, "categories": {"type": "array", "description": "商品分类列表", "items": {"$ref": "#/components/schemas/CategoryDTO"}}, "skus": {"type": "array", "description": "商品SKU列表", "items": {"$ref": "#/components/schemas/ProductSkuDTO"}}, "minPrice": {"type": "string", "description": "最低价格", "example": "299.0"}, "maxPrice": {"type": "string", "description": "最高价格", "example": "599.0"}, "totalStock": {"type": "integer", "description": "总库存", "format": "int32", "example": 100}, "priceRange": {"type": "string"}, "published": {"type": "boolean"}}, "description": "商品信息"}}}}