{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 用户订单", "description": "用户订单相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "用户订单", "description": "用户订单相关接口"}], "paths": {"/api/user/orders": {"get": {"tags": ["用户订单"], "summary": "查询用户订单列表", "description": "查询当前用户的所有订单", "operationId": "getUserOrders", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListOrderListDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "post": {"tags": ["用户订单"], "summary": "创建订单", "description": "用户下单，创建新订单", "operationId": "createOrder", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrderRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultOrderDetailDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/orders/{orderId}/confirm-receipt": {"post": {"tags": ["用户订单"], "summary": "确认收货", "description": "确认收货，完成订单", "operationId": "confirmReceipt", "parameters": [{"name": "orderId", "in": "path", "description": "订单ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/orders/{orderId}/cancel": {"post": {"tags": ["用户订单"], "summary": "取消订单", "description": "取消指定订单", "operationId": "cancelOrder", "parameters": [{"name": "orderId", "in": "path", "description": "订单ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/orders/{orderId}": {"get": {"tags": ["用户订单"], "summary": "查询订单详情", "description": "根据订单ID查询订单详情", "operationId": "getOrderDetail", "parameters": [{"name": "orderId", "in": "path", "description": "订单ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultOrderDetailDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "delete": {"tags": ["用户订单"], "summary": "删除订单", "description": "删除指定订单（逻辑删除）", "operationId": "deleteOrder", "parameters": [{"name": "orderId", "in": "path", "description": "订单ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/orders/status/{status}": {"get": {"tags": ["用户订单"], "summary": "根据状态查询订单列表", "description": "根据订单状态查询用户订单列表", "operationId": "getUserOrdersByStatus", "parameters": [{"name": "status", "in": "path", "description": "订单状态", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListOrderListDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/orders/number/{orderNumber}": {"get": {"tags": ["用户订单"], "summary": "根据订单号查询订单详情", "description": "根据订单号查询订单详情", "operationId": "getOrderDetailByOrderNumber", "parameters": [{"name": "orderNumber", "in": "path", "description": "订单号", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultOrderDetailDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/orders/count": {"get": {"tags": ["用户订单"], "summary": "统计用户订单数量", "description": "统计当前用户的订单总数", "operationId": "countUserOrders", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/orders/count/status/{status}": {"get": {"tags": ["用户订单"], "summary": "统计指定状态的订单数量", "description": "统计当前用户指定状态的订单数量", "operationId": "countUserOrdersByStatus", "parameters": [{"name": "status", "in": "path", "description": "订单状态", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultOrderDetailDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/OrderDetailDTO"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "OrderItemRequest": {"required": ["quantity", "skuId"], "type": "object", "properties": {"skuId": {"type": "integer", "format": "int64"}, "quantity": {"type": "integer", "format": "int32"}, "customizationType": {"type": "string"}, "customizationId": {"type": "integer", "format": "int64"}}}, "OrderListDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "orderNumber": {"type": "string"}, "status": {"type": "string"}, "statusDescription": {"type": "string"}, "totalAmount": {"type": "number"}, "itemsTotalAmount": {"type": "number"}, "discountAmount": {"type": "number"}, "shippingFee": {"type": "number"}, "itemCount": {"type": "integer", "format": "int32"}, "firstProductName": {"type": "string"}, "firstProductImageUrl": {"type": "string"}, "recipientName": {"type": "string"}, "recipientPhone": {"type": "string"}, "placedAt": {"type": "string", "format": "date-time"}, "paidAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "ResultListOrderListDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/OrderListDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "OrderDetailDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "orderNumber": {"type": "string"}, "status": {"type": "string"}, "statusDescription": {"type": "string"}, "totalAmount": {"type": "number"}, "itemsTotalAmount": {"type": "number"}, "discountAmount": {"type": "number"}, "shippingFee": {"type": "number"}, "shippingAddress": {"$ref": "#/components/schemas/AddressSnapshotDTO"}, "billingAddress": {"$ref": "#/components/schemas/AddressSnapshotDTO"}, "paymentMethod": {"type": "string"}, "notesToSeller": {"type": "string"}, "adminNotes": {"type": "string"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/OrderItemDetailDTO"}}, "placedAt": {"type": "string", "format": "date-time"}, "paidAt": {"type": "string", "format": "date-time"}, "shippedAt": {"type": "string", "format": "date-time"}, "deliveredAt": {"type": "string", "format": "date-time"}, "cancelledAt": {"type": "string", "format": "date-time"}, "completedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "ResultInteger": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "integer", "format": "int32"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "SkuAttributesSnapshotDTO": {"type": "object", "properties": {"skuCode": {"type": "string"}, "skuName": {"type": "string"}, "price": {"type": "number"}, "marketPrice": {"type": "number"}, "costPrice": {"type": "number"}, "stockQuantity": {"type": "integer", "format": "int32"}, "weight": {"type": "number"}, "imageUrl": {"type": "string"}, "attributes": {"type": "object", "additionalProperties": {"type": "string"}}, "brandName": {"type": "string"}, "categoryName": {"type": "string"}}}, "CreateOrderRequest": {"required": ["addressId", "items"], "type": "object", "properties": {"addressId": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/OrderItemRequest"}}, "paymentMethod": {"type": "string"}, "notesToSeller": {"maxLength": 500, "minLength": 0, "type": "string"}}, "description": "创建订单请求"}, "OrderItemDetailDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "orderId": {"type": "integer", "format": "int64"}, "skuId": {"type": "integer", "format": "int64"}, "productId": {"type": "integer", "format": "int64"}, "productNameSnapshot": {"type": "string"}, "skuAttributesSnapshot": {"$ref": "#/components/schemas/SkuAttributesSnapshotDTO"}, "quantity": {"type": "integer", "format": "int32"}, "unitPriceAtPurchase": {"type": "number"}, "totalPrice": {"type": "number"}, "customizationType": {"type": "string"}, "customizationId": {"type": "integer", "format": "int64"}}}, "AddressSnapshotDTO": {"type": "object", "properties": {"recipientName": {"type": "string"}, "phoneNumber": {"type": "string"}, "regionProvince": {"type": "string"}, "regionCity": {"type": "string"}, "regionDistrict": {"type": "string"}, "streetAddress": {"type": "string"}, "postalCode": {"type": "string"}, "fullAddress": {"type": "string"}}}}}}