{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 用户地址", "description": "用户地址相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "用户地址", "description": "用户地址管理相关接口"}], "paths": {"/api/user/addresses/{addressId}": {"get": {"tags": ["用户地址"], "summary": "查询地址详情", "description": "根据地址ID查询地址详情", "operationId": "getAddressDetail", "parameters": [{"name": "addressId", "in": "path", "description": "地址ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserAddress"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "put": {"tags": ["用户地址"], "summary": "更新地址", "description": "更新指定地址信息", "operationId": "updateAddress", "parameters": [{"name": "addressId", "in": "path", "description": "地址ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAddressRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserAddress"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "delete": {"tags": ["用户地址"], "summary": "删除地址", "description": "删除指定地址（逻辑删除）", "operationId": "deleteAddress", "parameters": [{"name": "addressId", "in": "path", "description": "地址ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/addresses": {"get": {"tags": ["用户地址"], "summary": "查询用户地址列表", "description": "查询当前用户的所有地址", "operationId": "getUserAddresses", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListUserAddress"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "post": {"tags": ["用户地址"], "summary": "创建地址", "description": "创建新的收货地址", "operationId": "createAddress", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAddressRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserAddress"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/addresses/{addressId}/set-default": {"post": {"tags": ["用户地址"], "summary": "设置默认地址", "description": "将指定地址设置为默认地址", "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "addressId", "in": "path", "description": "地址ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/addresses/default": {"get": {"tags": ["用户地址"], "summary": "获取默认地址", "description": "获取当前用户的默认地址", "operationId": "getDefaultAddress", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserAddress"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "CreateAddressRequest": {"required": ["phoneNumber", "<PERSON><PERSON><PERSON>", "regionCity", "regionDistrict", "regionProvince", "streetAddress"], "type": "object", "properties": {"recipientName": {"maxLength": 50, "minLength": 0, "type": "string", "description": "收件人姓名", "example": "张三"}, "phoneNumber": {"pattern": "^1[3-9]\\d{9}$", "type": "string", "description": "收件人电话", "example": "13800138000"}, "regionProvince": {"maxLength": 50, "minLength": 0, "type": "string", "description": "省份", "example": "广东省"}, "regionCity": {"maxLength": 50, "minLength": 0, "type": "string", "description": "城市", "example": "深圳市"}, "regionDistrict": {"maxLength": 50, "minLength": 0, "type": "string", "description": "区县", "example": "南山区"}, "streetAddress": {"maxLength": 200, "minLength": 0, "type": "string", "description": "详细街道地址", "example": "科技园南区深南大道10000号"}, "postalCode": {"pattern": "^$|^\\d{6}$", "type": "string", "description": "邮政编码", "example": "518000"}, "isDefault": {"type": "boolean", "description": "是否设置为默认地址", "example": false}}, "description": "创建地址请求"}, "UpdateAddressRequest": {"type": "object", "properties": {"recipientName": {"maxLength": 50, "minLength": 0, "type": "string", "description": "收件人姓名", "example": "张三"}, "phoneNumber": {"pattern": "^1[3-9]\\d{9}$", "type": "string", "description": "收件人电话", "example": "13800138000"}, "regionProvince": {"maxLength": 50, "minLength": 0, "type": "string", "description": "省份", "example": "广东省"}, "regionCity": {"maxLength": 50, "minLength": 0, "type": "string", "description": "城市", "example": "深圳市"}, "regionDistrict": {"maxLength": 50, "minLength": 0, "type": "string", "description": "区县", "example": "南山区"}, "streetAddress": {"maxLength": 200, "minLength": 0, "type": "string", "description": "详细街道地址", "example": "科技园南区深南大道10000号"}, "postalCode": {"pattern": "^$|^\\d{6}$", "type": "string", "description": "邮政编码", "example": "518000"}, "isDefault": {"type": "boolean", "description": "是否设置为默认地址", "example": false}}, "description": "更新地址请求"}, "ResultListUserAddress": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserAddress"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultUserAddress": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/UserAddress"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "UserAddress": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "recipientName": {"type": "string"}, "phoneNumber": {"type": "string"}, "regionProvince": {"type": "string"}, "regionCity": {"type": "string"}, "regionDistrict": {"type": "string"}, "streetAddress": {"type": "string"}, "postalCode": {"type": "string"}, "isDefault": {"type": "boolean"}, "isDeleted": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}}}