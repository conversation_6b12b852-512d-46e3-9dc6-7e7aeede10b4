{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - OSS图片上传与访问", "description": "阿里云OSS图片上传与访问相关接口文档 - 支持PostObject签名上传、完整图片元数据管理和预签名URL安全访问。\n\n## 重要说明\n\n### 图片访问方式\n系统使用OSS预签名URL方式提供图片访问，前端可以直接访问预签名URL下载图片：\n\n**推荐方式（预签名URL直接访问）：**\n```\nhttps://kk-oss-bucket-01.oss-cn-hangzhou.aliyuncs.com/images/1751966179530_0gxp1rv9y.png?Expires=1752038560&OSSAccessKeyId=STS.xxx&Signature=xxx&security-token=xxx\n```\n\n### 前端集成要点\n1. **预签名URL获取**：通过 `/api/oss/signed-url` 接口获取图片的预签名URL\n2. **直接访问**：前端可以直接使用预签名URL访问图片，无需经过业务服务端\n3. **缓存机制**：预签名URL有1小时有效期，系统会自动缓存避免重复生成\n4. **安全性**：使用STS临时凭证生成预签名URL，确保访问安全", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "OSS文件上传", "description": "阿里云OSS文件上传相关接口"}], "paths": {"/api/oss/upload-token": {"get": {"tags": ["OSS文件上传"], "summary": "获取OSS上传临时凭证", "description": "获取用于前端直传OSS的临时访问凭证，包含STS临时密钥和上传策略", "operationId": "getUploadToken", "parameters": [{"name": "X-Session-Id", "in": "header", "description": "用户SessionId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOssUploadTokenResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "401": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/oss/save-image": {"post": {"tags": ["OSS文件上传"], "summary": "保存图片信息", "description": "将上传到OSS的图片信息保存到数据库，建立图片记录", "operationId": "saveImage", "parameters": [{"name": "X-Session-Id", "in": "header", "description": "用户SessionId", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveImageRequest"}}}, "required": true}, "responses": {"200": {"description": "保存成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultImage"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "401": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/oss/image/{imageId}": {"get": {"tags": ["OSS文件上传"], "summary": "获取图片信息", "description": "根据图片ID获取图片详细信息", "operationId": "getImageById", "parameters": [{"name": "imageId", "in": "path", "description": "图片ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Session-Id", "in": "header", "description": "用户SessionId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultImage"}}}}, "404": {"description": "图片不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "delete": {"tags": ["OSS文件上传"], "summary": "删除图片", "description": "逻辑删除指定的图片记录", "operationId": "deleteImage", "parameters": [{"name": "imageId", "in": "path", "description": "图片ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Session-Id", "in": "header", "description": "用户SessionId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "404": {"description": "图片不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/oss/validate-url": {"post": {"tags": ["OSS文件上传"], "summary": "验证图片URL", "description": "验证图片URL是否有效，检查域名、文件类型等", "operationId": "validateImageUrl", "parameters": [{"name": "imageUrl", "in": "query", "description": "图片URL", "required": true, "schema": {"type": "string"}}, {"name": "X-Session-Id", "in": "header", "description": "用户SessionId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "验证完成", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/oss/images/business/{businessId}": {"get": {"tags": ["OSS文件上传"], "summary": "获取业务关联图片", "description": "根据业务ID和业务类型获取关联的图片列表，支持商品多图管理", "operationId": "getImagesByBusinessId", "parameters": [{"name": "businessId", "in": "path", "description": "业务ID（如商品ID）", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "businessType", "in": "query", "description": "业务类型", "required": true, "schema": {"type": "string", "enum": ["product", "user", "category", "brand", "banner", "other"]}}, {"name": "X-Session-Id", "in": "header", "description": "用户SessionId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultListImage"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/oss/images/batch": {"post": {"tags": ["OSS文件上传"], "summary": "批量保存图片信息", "description": "批量将上传到OSS的图片信息保存到数据库，支持商品多图上传", "operationId": "batchSaveImages", "parameters": [{"name": "X-Session-Id", "in": "header", "description": "用户SessionId", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchSaveImagesRequest"}}}, "required": true}, "responses": {"200": {"description": "批量保存成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultListImage"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/oss/images/sort": {"put": {"tags": ["OSS文件上传"], "summary": "更新图片排序", "description": "更新图片的排序顺序，支持商品图片排序管理", "operationId": "updateImageSort", "parameters": [{"name": "X-Session-Id", "in": "header", "description": "用户SessionId", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateImageSortRequest"}}}, "required": true}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/oss/signed-url": {"get": {"tags": ["OSS文件上传"], "summary": "获取OSS预签名URL", "description": "获取图片的OSS预签名URL，前端可直接访问，有效期1小时", "operationId": "getSignedUrl", "parameters": [{"name": "image<PERSON>ey", "in": "query", "description": "图片在OSS中的完整key，例如：images/1751968241424_ljkx4e4is.png", "required": true, "schema": {"type": "string"}}, {"name": "X-Session-Id", "in": "header", "description": "用户SessionId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOssSignedUrlResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/oss/transfer-wechat-avatar": {"post": {"tags": ["OSS文件上传"], "summary": "转存微信头像到OSS", "description": "下载微信头像并上传到OSS，返回OSS地址，用于解决微信头像URL过期问题", "operationId": "transferWechatAvatar", "parameters": [{"name": "X-Session-Id", "in": "header", "description": "用户SessionId", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferWechatAvatarRequest"}}}, "required": true}, "responses": {"200": {"description": "转存成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultTransferAvatarResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultVoid": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"type": "object", "description": "响应数据"}, "timestamp": {"type": "integer", "format": "int64", "description": "响应时间戳"}, "success": {"type": "boolean", "description": "操作是否成功"}}}, "ResultOssUploadTokenResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "message": {"type": "string", "description": "响应消息", "example": "获取上传凭证成功"}, "data": {"$ref": "#/components/schemas/OssUploadTokenResponse"}, "timestamp": {"type": "integer", "format": "int64", "description": "响应时间戳"}, "success": {"type": "boolean", "description": "操作是否成功", "example": true}}}, "OssUploadTokenResponse": {"type": "object", "properties": {"accessKeyId": {"type": "string", "description": "临时访问密钥ID", "example": "STS.NUCJMhD1n5rK6mGSEN3g41234"}, "accessKeySecret": {"type": "string", "description": "临时访问密钥Secret", "example": "9drEXAMPLEKEY-AfvRrcc2S8Avi6Q2GFrCTD+fBQ"}, "securityToken": {"type": "string", "description": "安全令牌", "example": "CAIS8AF1q6Ft5B2yfSjIr5bSEcTgo..."}, "expiration": {"type": "string", "format": "date-time", "description": "凭证过期时间", "example": "2024-01-15T10:30:00Z"}, "endpoint": {"type": "string", "description": "OSS上传端点", "example": "https://your-bucket.oss-cn-hangzhou.aliyuncs.com"}, "bucketName": {"type": "string", "description": "存储桶名称", "example": "glasses-merchant-images"}, "pathPrefix": {"type": "string", "description": "文件路径前缀", "example": "uploads/2024/01/15/"}, "maxFileSize": {"type": "integer", "format": "int64", "description": "最大文件大小（字节）", "example": 10485760}, "allowedFileTypes": {"type": "array", "items": {"type": "string"}, "description": "允许的文件类型", "example": ["jpg", "jpeg", "png", "gif", "webp"]}, "policy": {"type": "object", "description": "上传策略信息", "properties": {"maxSize": {"type": "integer", "format": "int64", "description": "最大文件大小（字节）", "example": 10485760}, "allowedTypes": {"type": "array", "items": {"type": "string"}, "description": "允许的文件类型", "example": ["jpg", "jpeg", "png", "gif", "webp"]}, "pathPrefix": {"type": "string", "description": "文件路径前缀", "example": "images/"}}}, "postPolicy": {"type": "string", "description": "PostObject上传策略（Base64编码）", "example": "ewogICJleHBpcmF0aW9uIjogIjIwMjUtMDctMDhUMDk6MjE6MzlaIiwKICAiY29uZGl0aW9ucyI6IFsKICAgIHsiYnVja2V0IjogImtrLW9zcy1idWNrZXQtMDEifSwKICAgIFsic3RhcnRzLXdpdGgiLCAiJGtleSIsICJpbWFnZXMvIl0sCiAgICBbImNvbnRlbnQtbGVuZ3RoLXJhbmdlIiwgMCwgMTA0ODU3NjBdLAogICAgeyJzdWNjZXNzX2FjdGlvbl9zdGF0dXMiOiAiMjAwIn0KICBdCn0="}, "signature": {"type": "string", "description": "PostObject上传签名", "example": "RZfiGV6XX/WuLqFH3irtShwhCiU="}}, "description": "OSS上传凭证响应"}, "SaveImageRequest": {"required": ["imageUrl"], "type": "object", "properties": {"imageUrl": {"type": "string", "description": "图片URL地址", "example": "https://your-bucket.oss-cn-hangzhou.aliyuncs.com/uploads/2024/01/15/1705312345678_abc123.jpg"}, "imageName": {"type": "string", "description": "图片名称", "example": "用户头像.jpg"}, "description": {"type": "string", "description": "图片描述", "example": "用户上传的头像图片"}, "category": {"type": "string", "description": "图片分类", "example": "avatar", "enum": ["avatar", "product", "banner", "category", "brand", "other"]}, "businessType": {"type": "string", "description": "业务类型", "example": "user", "enum": ["user", "product", "order", "category", "brand", "banner", "other"]}, "businessId": {"type": "integer", "format": "int64", "description": "关联的业务ID（如用户ID、商品ID等）", "example": 100001}, "tags": {"type": "string", "description": "图片标签，多个标签用逗号分隔", "example": "头像,用户,个人资料"}, "sortOrder": {"type": "integer", "format": "int32", "description": "排序序号", "example": 1}, "fileSize": {"type": "integer", "format": "int64", "description": "文件大小（字节）", "example": 1048576}, "width": {"type": "integer", "format": "int32", "description": "图片宽度（像素）", "example": 1920}, "height": {"type": "integer", "format": "int32", "description": "图片高度（像素）", "example": 1080}, "fileType": {"type": "string", "description": "文件类型（如jpg、png等）", "example": "jpg"}, "uploadedBy": {"type": "integer", "format": "int64", "description": "上传者用户ID", "example": 100001}}, "description": "保存图片信息请求"}, "ResultImage": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "message": {"type": "string", "description": "响应消息", "example": "图片信息保存成功"}, "data": {"$ref": "#/components/schemas/Image"}, "timestamp": {"type": "integer", "format": "int64", "description": "响应时间戳"}, "success": {"type": "boolean", "description": "操作是否成功", "example": true}}}, "Image": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "图片ID", "example": 1001}, "imageUrl": {"type": "string", "description": "图片URL地址", "example": "https://your-bucket.oss-cn-hangzhou.aliyuncs.com/uploads/2024/01/15/1705312345678_abc123.jpg"}, "imageName": {"type": "string", "description": "图片名称", "example": "用户头像.jpg"}, "description": {"type": "string", "description": "图片描述", "example": "用户上传的头像图片"}, "category": {"type": "string", "description": "图片分类", "example": "avatar"}, "businessType": {"type": "string", "description": "业务类型", "example": "user"}, "businessId": {"type": "integer", "format": "int64", "description": "关联的业务ID", "example": 100001}, "tags": {"type": "string", "description": "图片标签，多个标签用逗号分隔", "example": "头像,用户,个人资料"}, "fileSize": {"type": "integer", "format": "int64", "description": "文件大小（字节）", "example": 1048576}, "width": {"type": "integer", "format": "int32", "description": "图片宽度（像素）", "example": 1920}, "height": {"type": "integer", "format": "int32", "description": "图片高度（像素）", "example": 1080}, "fileType": {"type": "string", "description": "文件类型", "example": "jpg"}, "sortOrder": {"type": "integer", "format": "int32", "description": "排序序号", "example": 1}, "uploadedBy": {"type": "integer", "format": "int64", "description": "上传用户ID", "example": 100001}, "status": {"type": "integer", "format": "int32", "description": "状态：1-正常，0-删除", "example": 1}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间", "example": "2024-01-15T10:30:00Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "更新时间", "example": "2024-01-15T10:30:00Z"}}, "description": "图片信息"}, "ResultBoolean": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "message": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"type": "boolean", "description": "操作结果", "example": true}, "timestamp": {"type": "integer", "format": "int64", "description": "响应时间戳"}, "success": {"type": "boolean", "description": "操作是否成功", "example": true}}}, "ResultListImage": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "message": {"type": "string", "description": "响应消息", "example": "获取图片列表成功"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Image"}, "description": "图片列表"}, "timestamp": {"type": "integer", "format": "int64", "description": "响应时间戳"}, "success": {"type": "boolean", "description": "操作是否成功", "example": true}}}, "BatchSaveImagesRequest": {"required": ["images"], "type": "object", "properties": {"images": {"type": "array", "items": {"$ref": "#/components/schemas/SaveImageRequest"}, "description": "图片信息列表", "maxItems": 20, "minItems": 1}}, "description": "批量保存图片信息请求"}, "UpdateImageSortRequest": {"required": ["imageSorts"], "type": "object", "properties": {"imageSorts": {"type": "array", "items": {"$ref": "#/components/schemas/ImageSortInfo"}, "description": "图片排序信息列表", "minItems": 1}}, "description": "更新图片排序请求"}, "ImageSortInfo": {"required": ["imageId", "sortOrder"], "type": "object", "properties": {"imageId": {"type": "integer", "format": "int64", "description": "图片ID", "example": 1001}, "sortOrder": {"type": "integer", "format": "int32", "description": "排序值，数值越大排序越靠前", "example": 1}}, "description": "图片排序信息"}, "ResultOssSignedUrlResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "message": {"type": "string", "description": "响应消息", "example": "获取预签名URL成功"}, "data": {"$ref": "#/components/schemas/OssSignedUrlResponse"}, "timestamp": {"type": "integer", "format": "int64", "description": "响应时间戳"}, "success": {"type": "boolean", "description": "操作是否成功", "example": true}}}, "OssSignedUrlResponse": {"type": "object", "properties": {"signedUrl": {"type": "string", "description": "预签名URL", "example": "https://kk-bucket-01.oss-cn-hangzhou.aliyuncs.com/images/1751966179530_0gxp1rv9y.png?Expires=1752038560&OSSAccessKeyId=STS.xxx&Signature=xxx&security-token=xxx"}, "imageKey": {"type": "string", "description": "图片在OSS中的key", "example": "images/1751966179530_0gxp1rv9y.png"}, "expiresAt": {"type": "string", "format": "date-time", "description": "过期时间", "example": "2024-01-15T11:30:00Z"}, "expiresInSeconds": {"type": "integer", "format": "int64", "description": "有效期（秒）", "example": 3600}, "fromCache": {"type": "boolean", "description": "是否来自缓存", "example": false}, "cacheRemainingSeconds": {"type": "integer", "format": "int64", "description": "缓存剩余时间（秒）", "example": 3600}}, "description": "OSS预签名URL响应"}, "TransferWechatAvatarRequest": {"required": ["wechatAvatarUrl"], "type": "object", "properties": {"wechatAvatarUrl": {"type": "string", "description": "微信头像URL", "example": "https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKVUskibDnhMs4eeJN2OWMiaBIGgKicQn7jQFqTImqKcOwRmAoqt6nia9O6WRW0xts5BmCvdwK4RJOYcg/132"}, "userId": {"type": "integer", "format": "int64", "description": "用户ID（可选，用于关联用户）", "example": 100001}, "description": {"type": "string", "description": "图片描述", "example": "用户头像"}}, "description": "转存微信头像请求"}, "TransferAvatarResponse": {"type": "object", "properties": {"ossUrl": {"type": "string", "description": "OSS图片URL", "example": "https://kk-bucket-01.oss-cn-hangzhou.aliyuncs.com/images/avatar/1752571234567_abc123.jpg"}, "signedUrl": {"type": "string", "description": "OSS预签名URL（可直接访问）", "example": "https://kk-bucket-01.oss-cn-hangzhou.aliyuncs.com/images/avatar/1752571234567_abc123.jpg?Expires=xxx"}, "ossKey": {"type": "string", "description": "OSS文件Key", "example": "images/avatar/1752571234567_abc123.jpg"}, "originalWechatUrl": {"type": "string", "description": "原始微信头像URL", "example": "https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKVUskibDnhMs4eeJN2OWMiaBIGgKicQn7jQFqTImqKcOwRmAoqt6nia9O6WRW0xts5BmCvdwK4RJOYcg/132"}, "imageId": {"type": "integer", "format": "int64", "description": "图片ID（如果保存到images表）", "example": 1945051248220225537}, "fileSize": {"type": "integer", "format": "int64", "description": "文件大小（字节）", "example": 51200}, "width": {"type": "integer", "format": "int32", "description": "图片宽度（像素）", "example": 132}, "height": {"type": "integer", "format": "int32", "description": "图片高度（像素）", "example": 132}, "fileType": {"type": "string", "description": "文件类型", "example": "jpg"}, "transferTime": {"type": "string", "format": "date-time", "description": "转存时间", "example": "2025-07-15T17:30:00"}, "success": {"type": "boolean", "description": "是否转存成功", "example": true}, "message": {"type": "string", "description": "转存消息", "example": "微信头像转存成功"}}, "description": "转存头像响应"}, "ResultTransferAvatarResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "message": {"type": "string", "description": "响应消息", "example": "微信头像转存成功"}, "data": {"$ref": "#/components/schemas/TransferAvatarResponse"}, "timestamp": {"type": "integer", "format": "int64", "description": "响应时间戳"}, "success": {"type": "boolean", "description": "操作是否成功", "example": true}}}}}}