{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 管理员管理", "description": "管理员管理相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "管理员管理", "description": "管理员相关接口"}], "paths": {"/api/admin/users/{id}": {"get": {"tags": ["管理员管理"], "summary": "根据ID查询管理员", "description": "根据管理员ID查询管理员详细信息", "operationId": "getById_4", "parameters": [{"name": "id", "in": "path", "description": "管理员ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultAdminUser"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "put": {"tags": ["管理员管理"], "summary": "更新管理员信息", "description": "更新管理员的基本信息（不包括用户名和密码）", "operationId": "updateAdminUser", "parameters": [{"name": "id", "in": "path", "description": "管理员ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAdminUserRequest"}}}, "required": true}, "responses": {"200": {"description": "更新成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "请求参数错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "401": {"description": "未授权", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "403": {"description": "权限不足", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "404": {"description": "管理员不存在", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "409": {"description": "手机号已存在", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "delete": {"tags": ["管理员管理"], "summary": "删除管理员", "description": "删除指定的管理员", "operationId": "delete_4", "parameters": [{"name": "id", "in": "path", "description": "管理员ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/users/{id}/status": {"put": {"tags": ["管理员管理"], "summary": "更新状态", "description": "启用或禁用管理员", "operationId": "updateStatus_4", "parameters": [{"name": "id", "in": "path", "description": "管理员ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "isActive", "in": "query", "description": "状态：true-启用，false-禁用", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/users/{id}/reset-password": {"put": {"tags": ["管理员管理"], "summary": "重置管理员密码", "description": "管理员重置其他管理员的密码", "operationId": "resetPassword", "parameters": [{"name": "id", "in": "path", "description": "管理员ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "重置成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "请求参数错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "401": {"description": "未授权", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "403": {"description": "权限不足", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "404": {"description": "管理员不存在", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/users/{id}/change-password": {"put": {"tags": ["管理员管理"], "summary": "修改管理员密码", "description": "管理员修改自己的密码", "operationId": "changePassword", "parameters": [{"name": "id", "in": "path", "description": "管理员ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "修改成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "请求参数错误或原密码错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "401": {"description": "未授权", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "403": {"description": "权限不足", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "404": {"description": "管理员不存在", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/users": {"get": {"tags": ["管理员管理"], "summary": "分页查询管理员列表", "description": "分页查询管理员列表，支持多条件筛选", "operationId": "getPageList", "parameters": [{"name": "username", "in": "query", "description": "用户名（模糊查询）", "required": false, "schema": {"type": "string"}}, {"name": "realName", "in": "query", "description": "真实姓名（模糊查询）", "required": false, "schema": {"type": "string"}}, {"name": "email", "in": "query", "description": "邮箱（模糊查询）", "required": false, "schema": {"type": "string"}}, {"name": "phone", "in": "query", "description": "手机号（模糊查询）", "required": false, "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "description": "状态：true-启用，false-禁用，null-全部", "required": false, "schema": {"type": "boolean"}}, {"name": "roleId", "in": "query", "description": "角色ID", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "startTime", "in": "query", "description": "创建开始时间（格式：yyyy-MM-dd HH:mm:ss）", "required": false, "schema": {"type": "string"}}, {"name": "endTime", "in": "query", "description": "创建结束时间（格式：yyyy-MM-dd HH:mm:ss）", "required": false, "schema": {"type": "string"}}, {"name": "sortBy", "in": "query", "description": "排序字段（create_time, last_login_time, username）", "required": false, "schema": {"type": "string", "default": "create_time"}}, {"name": "sortOrder", "in": "query", "description": "排序方向（ASC, DESC）", "required": false, "schema": {"type": "string", "default": "DESC"}}, {"name": "page", "in": "query", "description": "页码（从1开始）", "required": false, "schema": {"type": "integer", "default": 1, "minimum": 1}}, {"name": "size", "in": "query", "description": "每页大小", "required": false, "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 100}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultPageResultAdminUserPageResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "post": {"tags": ["管理员管理"], "summary": "创建管理员", "description": "创建新的管理员用户", "operationId": "createAdminUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAdminUserRequest"}}}, "required": true}, "responses": {"200": {"description": "创建成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "请求参数错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "401": {"description": "未授权", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "403": {"description": "权限不足", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "409": {"description": "用户名或手机号已存在", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultAdminUser": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminUser"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultPageResultAdminUserPageResponse": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageResultAdminUserPageResponse"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "PageResultAdminUserPageResponse": {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/components/schemas/AdminUserPageResponse"}}, "total": {"type": "integer", "format": "int64", "description": "总记录数"}, "page": {"type": "integer", "description": "当前页码"}, "size": {"type": "integer", "description": "每页大小"}, "pages": {"type": "integer", "description": "总页数"}}}, "AdminUser": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "hashedPassword": {"type": "string"}, "email": {"type": "string"}, "realName": {"type": "string"}, "phone": {"type": "string"}, "roleId": {"type": "integer", "format": "int64"}, "isActive": {"type": "boolean"}, "isDeleted": {"type": "boolean"}, "lastLoginTime": {"type": "string", "format": "date-time"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}}}, "AdminUserPageResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "管理员ID"}, "username": {"type": "string", "description": "用户名"}, "email": {"type": "string", "description": "邮箱"}, "realName": {"type": "string", "description": "真实姓名"}, "phone": {"type": "string", "description": "手机号"}, "roleId": {"type": "integer", "format": "int64", "description": "角色ID"}, "roleName": {"type": "string", "description": "角色名称"}, "isActive": {"type": "boolean", "description": "是否启用"}, "lastLoginTime": {"type": "string", "format": "date-time", "description": "最后登录时间"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}}}, "CreateAdminUserRequest": {"type": "object", "required": ["username", "password", "email", "realName", "roleId"], "properties": {"username": {"type": "string", "description": "用户名", "minLength": 3, "maxLength": 20, "pattern": "^[a-zA-Z0-9_]+$"}, "password": {"type": "string", "description": "密码", "minLength": 6, "maxLength": 20}, "email": {"type": "string", "description": "邮箱", "format": "email"}, "realName": {"type": "string", "description": "真实姓名", "minLength": 1, "maxLength": 50}, "phone": {"type": "string", "description": "手机号", "pattern": "^1[3-9]\\d{9}$"}, "roleId": {"type": "integer", "format": "int64", "description": "角色ID"}}}, "UpdateAdminUserRequest": {"type": "object", "required": ["email", "realName", "roleId"], "properties": {"email": {"type": "string", "description": "邮箱", "format": "email"}, "realName": {"type": "string", "description": "真实姓名", "minLength": 1, "maxLength": 50}, "phone": {"type": "string", "description": "手机号", "pattern": "^1[3-9]\\d{9}$"}, "roleId": {"type": "integer", "format": "int64", "description": "角色ID"}}}, "ChangePasswordRequest": {"type": "object", "required": ["oldPassword", "newPassword"], "properties": {"oldPassword": {"type": "string", "description": "原密码", "minLength": 1}, "newPassword": {"type": "string", "description": "新密码", "minLength": 6, "maxLength": 20}}}, "ResetPasswordRequest": {"type": "object", "required": ["newPassword"], "properties": {"newPassword": {"type": "string", "description": "新密码", "minLength": 6, "maxLength": 20}}}, "ResultListAdminUser": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AdminUser"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}}}}