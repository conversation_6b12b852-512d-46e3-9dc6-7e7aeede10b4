{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 商品SKU管理", "description": "商品SKU管理相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "商品SKU管理", "description": "商品SKU相关接口"}], "paths": {"/api/product-skus/{id}": {"get": {"tags": ["商品SKU管理"], "summary": "根据ID查询SKU", "description": "根据SKU ID查询SKU详情", "operationId": "getById_1", "parameters": [{"name": "id", "in": "path", "description": "SKU ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultProductSkuDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "put": {"tags": ["商品SKU管理"], "summary": "更新SKU", "description": "更新SKU信息", "operationId": "update_1", "parameters": [{"name": "id", "in": "path", "description": "SKU ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductSkuRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultProductSkuDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "delete": {"tags": ["商品SKU管理"], "summary": "删除SKU", "description": "删除SKU（逻辑删除）", "operationId": "delete_1", "parameters": [{"name": "id", "in": "path", "description": "SKU ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/{id}/stock": {"put": {"tags": ["商品SKU管理"], "summary": "更新库存", "description": "更新SKU库存数量", "operationId": "updateStock", "parameters": [{"name": "id", "in": "path", "description": "SKU ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "stock", "in": "query", "description": "库存数量", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/{id}/stock/increase": {"put": {"tags": ["商品SKU管理"], "summary": "增加库存", "description": "增加SKU库存（用于退货）", "operationId": "increaseStock", "parameters": [{"name": "id", "in": "path", "description": "SKU ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "quantity", "in": "query", "description": "增加数量", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/{id}/stock/decrease": {"put": {"tags": ["商品SKU管理"], "summary": "减少库存", "description": "减少SKU库存（用于下单）", "operationId": "decreaseStock", "parameters": [{"name": "id", "in": "path", "description": "SKU ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "quantity", "in": "query", "description": "减少数量", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/{id}/status": {"put": {"tags": ["商品SKU管理"], "summary": "更新SKU状态", "description": "更新SKU启用/禁用状态", "operationId": "updateStatus_1", "parameters": [{"name": "id", "in": "path", "description": "SKU ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "isActive", "in": "query", "description": "状态：true-启用，false-禁用", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/batch": {"put": {"tags": ["商品SKU管理"], "summary": "批量更新SKU", "description": "批量更新多个SKU", "operationId": "batchUpdate", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "批量更新SKU操作列表", "items": {"$ref": "#/components/schemas/SkuUpdateOperation"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductSkuDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "post": {"tags": ["商品SKU管理"], "summary": "批量创建SKU", "description": "批量创建多个SKU", "operationId": "batchCreate", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "批量创建SKU请求列表", "items": {"$ref": "#/components/schemas/CreateSkuRequest"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductSkuDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "delete": {"tags": ["商品SKU管理"], "summary": "批量删除SKU", "description": "批量删除多个SKU", "operationId": "batchDelete_1", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "SKU ID列表", "items": {"type": "integer", "format": "int64"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/batch/stock": {"put": {"tags": ["商品SKU管理"], "summary": "批量更新库存", "description": "批量更新SKU库存", "operationId": "batchUpdateStock", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "库存更新列表", "items": {"$ref": "#/components/schemas/StockUpdate"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/batch/status": {"put": {"tags": ["商品SKU管理"], "summary": "批量更新SKU状态", "description": "批量更新SKU启用/禁用状态", "operationId": "batchUpdateStatus_1", "parameters": [{"name": "ids", "in": "query", "description": "SKU ID列表", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "isActive", "in": "query", "description": "状态：true-启用，false-禁用", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus": {"post": {"tags": ["商品SKU管理"], "summary": "创建SKU", "description": "创建新的商品SKU", "operationId": "create_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSkuRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultProductSkuDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/product/{productId}/batch": {"post": {"tags": ["商品SKU管理"], "summary": "为商品批量创建SKU", "description": "为指定商品批量创建SKU", "operationId": "batchCreateForProduct", "parameters": [{"name": "productId", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "批量创建SKU请求列表", "items": {"$ref": "#/components/schemas/CreateSkuRequest"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductSkuDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/sku-code/{skuCode}": {"get": {"tags": ["商品SKU管理"], "summary": "根据SKU编码查询SKU", "description": "根据SKU编码查询SKU详情", "operationId": "getBySkuCode", "parameters": [{"name": "skuCode", "in": "path", "description": "SKU编码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultProductSkuDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/product/{productId}": {"get": {"tags": ["商品SKU管理"], "summary": "根据商品ID查询SKU列表", "description": "根据商品ID查询所有SKU", "operationId": "getByProductId", "parameters": [{"name": "productId", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductSkuDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "delete": {"tags": ["商品SKU管理"], "summary": "删除商品的所有SKU", "description": "根据商品ID删除所有SKU（逻辑删除）", "operationId": "deleteByProductId", "parameters": [{"name": "productId", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/product/{productId}/active": {"get": {"tags": ["商品SKU管理"], "summary": "根据商品ID查询活跃SKU列表", "description": "根据商品ID查询活跃的SKU", "operationId": "getActiveByProductId", "parameters": [{"name": "productId", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductSkuDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/low-stock": {"get": {"tags": ["商品SKU管理"], "summary": "查询低库存SKU列表", "description": "查询库存不足的SKU", "operationId": "getLowStockList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductSkuDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/exists/sku-code": {"get": {"tags": ["商品SKU管理"], "summary": "检查SKU编码是否存在", "description": "检查SKU编码是否已存在", "operationId": "existsBySkuCode", "parameters": [{"name": "skuCode", "in": "query", "description": "SKU编码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/all": {"get": {"tags": ["商品SKU管理"], "summary": "查询所有SKU", "description": "查询所有SKU列表", "operationId": "getAll", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductSkuDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/product-skus/active": {"get": {"tags": ["商品SKU管理"], "summary": "查询活跃SKU列表", "description": "查询所有活跃的SKU", "operationId": "getActiveList_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductSkuDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "SkuUpdateOperation": {"required": ["operation"], "type": "object", "properties": {"operation": {"type": "string", "description": "操作类型", "example": "CREATE", "enum": ["CREATE", "UPDATE", "DELETE"]}, "skuId": {"type": "integer", "description": "SKU ID", "format": "int64", "example": 1}, "createData": {"$ref": "#/components/schemas/CreateSkuRequest"}, "updateData": {"$ref": "#/components/schemas/UpdateProductSkuRequest"}, "update": {"type": "boolean"}, "delete": {"type": "boolean"}, "create": {"type": "boolean"}, "valid": {"type": "boolean"}}, "description": "SKU更新操作"}, "CreateSkuRequest": {"required": ["price", "stockQuantity"], "type": "object", "properties": {"productId": {"type": "integer", "description": "关联商品ID", "format": "int64", "example": 1}, "skuCode": {"maxLength": 100, "minLength": 0, "type": "string", "description": "SKU编码", "example": "RB-CLASSIC-BLACK-M"}, "nameExtension": {"maxLength": 255, "minLength": 0, "type": "string", "description": "SKU名称后缀", "example": "黑色 中号"}, "price": {"minimum": 0.01, "exclusiveMinimum": false, "type": "number", "description": "销售价", "example": 399.0}, "originalPrice": {"minimum": 0.01, "exclusiveMinimum": false, "type": "number", "description": "原价", "example": 499.0}, "stockQuantity": {"minimum": 0, "type": "integer", "description": "库存数量", "format": "int32", "example": 50}, "lowStockThreshold": {"minimum": 0, "type": "integer", "description": "低库存预警值", "format": "int32", "example": 10}, "weightKg": {"minimum": 0.001, "exclusiveMinimum": false, "type": "number", "description": "重量", "example": 0.15}, "dimensionsCm": {"maxLength": 100, "minLength": 0, "type": "string", "description": "尺寸", "example": "14x5x3"}, "imageUrl": {"maxLength": 1024, "minLength": 0, "type": "string", "description": "SKU专属图片", "example": "https://example.com/sku.jpg"}, "attributes": {"type": "object", "additionalProperties": {"type": "string", "description": "规格属性", "example": "{\"color\":\"黑色\",\"size\":\"中号\"}"}, "description": "规格属性", "example": {"color": "黑色", "size": "中号"}}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}}, "description": "创建SKU请求"}, "StockUpdate": {"required": ["id", "stockQuantity"], "type": "object", "properties": {"id": {"type": "integer", "description": "SKU ID", "format": "int64", "example": 1}, "stockQuantity": {"minimum": 0, "type": "integer", "description": "库存数量", "format": "int32", "example": 50}}, "description": "库存更新"}, "ResultProductSkuDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/ProductSkuDTO"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "UpdateProductSkuRequest": {"type": "object", "properties": {"skuCode": {"maxLength": 100, "minLength": 0, "type": "string", "description": "SKU编码", "example": "RB-CLASSIC-BLACK-M"}, "nameExtension": {"maxLength": 255, "minLength": 0, "type": "string", "description": "SKU名称后缀", "example": "黑色 中号"}, "price": {"minimum": 0.01, "exclusiveMinimum": false, "type": "number", "description": "销售价", "example": 399.0}, "originalPrice": {"minimum": 0.01, "exclusiveMinimum": false, "type": "number", "description": "原价", "example": 499.0}, "stockQuantity": {"minimum": 0, "type": "integer", "description": "库存数量", "format": "int32", "example": 50}, "lowStockThreshold": {"minimum": 0, "type": "integer", "description": "低库存预警值", "format": "int32", "example": 10}, "weightKg": {"minimum": 0.001, "exclusiveMinimum": false, "type": "number", "description": "重量", "example": 0.15}, "dimensionsCm": {"maxLength": 100, "minLength": 0, "type": "string", "description": "尺寸", "example": "14x5x3"}, "imageUrl": {"maxLength": 1024, "minLength": 0, "type": "string", "description": "SKU专属图片", "example": "https://example.com/sku.jpg"}, "attributes": {"type": "object", "additionalProperties": {"type": "string", "description": "规格属性", "example": "{\"color\":\"黑色\",\"size\":\"中号\"}"}, "description": "规格属性", "example": {"color": "黑色", "size": "中号"}}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}}, "description": "更新SKU请求"}, "ProductSkuDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "SKU ID", "format": "int64", "example": 10000}, "productId": {"type": "integer", "description": "商品ID", "format": "int64", "example": 1000}, "skuCode": {"type": "string", "description": "SKU编码", "example": "RB-CLASSIC-BLACK-M"}, "nameExtension": {"type": "string", "description": "SKU名称后缀", "example": "黑色 中号"}, "price": {"type": "number", "description": "销售价", "example": 399.0}, "originalPrice": {"type": "number", "description": "原价", "example": 499.0}, "stockQuantity": {"type": "integer", "description": "库存数量", "format": "int32", "example": 50}, "lowStockThreshold": {"type": "integer", "description": "低库存预警值", "format": "int32", "example": 10}, "weightKg": {"type": "number", "description": "重量", "example": 0.15}, "dimensionsCm": {"type": "string", "description": "尺寸", "example": "14x5x3"}, "imageUrl": {"type": "string", "description": "SKU专属图片", "example": "https://example.com/sku.jpg"}, "attributes": {"type": "object", "additionalProperties": {"type": "string", "description": "规格属性"}, "description": "规格属性"}, "attributesJson": {"type": "string", "description": "规格属性JSON", "example": "{\"color\":\"黑色\",\"size\":\"中号\"}"}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}, "productName": {"type": "string", "description": "商品名称", "example": "雷朋经典款太阳镜"}, "discountRate": {"type": "number"}, "lowStock": {"type": "boolean"}, "fullName": {"type": "string"}}, "description": "商品SKU信息"}, "ResultBoolean": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "boolean"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultListProductSkuDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSkuDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}}}}