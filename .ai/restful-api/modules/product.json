{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 商品管理", "description": "商品管理相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "商品管理", "description": "商品相关接口"}], "paths": {"/api/products/{id}": {"get": {"tags": ["商品管理"], "summary": "根据ID查询商品", "description": "根据商品ID查询商品详情", "operationId": "getById", "parameters": [{"name": "id", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultProductDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "put": {"tags": ["商品管理"], "summary": "更新商品", "description": "更新商品信息", "operationId": "update", "parameters": [{"name": "id", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultProductDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "delete": {"tags": ["商品管理"], "summary": "删除商品", "description": "删除商品（逻辑删除）", "operationId": "delete", "parameters": [{"name": "id", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/{id}/with-skus": {"get": {"tags": ["商品管理"], "summary": "获取商品及其SKU信息", "description": "获取商品详情及其所有SKU信息", "operationId": "getProductWithSkus_1", "parameters": [{"name": "id", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultProductWithSkusDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "put": {"tags": ["商品管理"], "summary": "更新商品并管理SKU", "description": "更新商品信息的同时管理其SKU", "operationId": "updateProductWithSkus", "parameters": [{"name": "id", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductWithSkusRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultProductWithSkusDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/{id}/unpublish": {"put": {"tags": ["商品管理"], "summary": "下架商品", "description": "下架商品", "operationId": "unpublish", "parameters": [{"name": "id", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/{id}/status": {"put": {"tags": ["商品管理"], "summary": "更新商品状态", "description": "更新商品上架/下架状态", "operationId": "updateStatus", "parameters": [{"name": "id", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "isActive", "in": "query", "description": "状态：true-上架，false-下架", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/{id}/publish": {"put": {"tags": ["商品管理"], "summary": "发布商品", "description": "发布商品", "operationId": "publish", "parameters": [{"name": "id", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/{id}/featured": {"put": {"tags": ["商品管理"], "summary": "更新推荐状态", "description": "更新商品推荐状态", "operationId": "updateFeatured", "parameters": [{"name": "id", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "isFeatured", "in": "query", "description": "推荐状态：true-推荐，false-不推荐", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/batch/status": {"put": {"tags": ["商品管理"], "summary": "批量更新商品状态", "description": "批量更新商品上架/下架状态", "operationId": "batchUpdateStatus", "parameters": [{"name": "ids", "in": "query", "description": "商品ID列表", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "isActive", "in": "query", "description": "状态：true-上架，false-下架", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/batch/featured": {"put": {"tags": ["商品管理"], "summary": "批量更新推荐状态", "description": "批量更新商品推荐状态", "operationId": "batchUpdateFeatured", "parameters": [{"name": "ids", "in": "query", "description": "商品ID列表", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "isFeatured", "in": "query", "description": "推荐状态：true-推荐，false-不推荐", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products": {"post": {"tags": ["商品管理"], "summary": "创建商品", "description": "创建新商品", "operationId": "create", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultProductDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/with-skus": {"post": {"tags": ["商品管理"], "summary": "创建商品并设置SKU", "description": "创建商品的同时创建其SKU", "operationId": "createProductWithSkus", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductWithSkusRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultProductWithSkusDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/batch/assign-categories": {"post": {"tags": ["商品管理"], "summary": "批量为商品分配分类", "description": "批量为多个商品分配分类", "operationId": "batchAssignCategoriesToProducts", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchAssignCategoriesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/{id}/primary-category": {"get": {"tags": ["商品管理"], "summary": "获取商品主分类", "description": "获取商品的主分类信息", "operationId": "getPrimaryCategoryByProductId", "parameters": [{"name": "id", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/{id}/categories": {"get": {"tags": ["商品管理"], "summary": "获取商品的分类列表", "description": "获取指定商品关联的所有分类", "operationId": "getCategoriesByProductId", "parameters": [{"name": "id", "in": "path", "description": "商品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCategoryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/slug/{slug}": {"get": {"tags": ["商品管理"], "summary": "根据slug查询商品", "description": "根据商品slug查询商品详情", "operationId": "getBySlug", "parameters": [{"name": "slug", "in": "path", "description": "商品slug", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultProductDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/page": {"get": {"tags": ["商品管理"], "summary": "分页查询商品列表", "description": "分页查询商品列表，支持排序和完整分页信息，可选择是否包含SKU信息", "operationId": "getPageList", "parameters": [{"name": "name", "in": "query", "description": "商品名称", "required": false, "schema": {"type": "string"}}, {"name": "brandId", "in": "query", "description": "品牌ID", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "categoryId", "in": "query", "description": "分类ID", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "isActive", "in": "query", "description": "状态：true-上架，false-下架", "required": false, "schema": {"type": "boolean"}}, {"name": "isFeatured", "in": "query", "description": "推荐状态：true-推荐，false-不推荐", "required": false, "schema": {"type": "boolean"}}, {"name": "includeSku", "in": "query", "description": "是否包含SKU信息：true-包含，false-不包含", "required": false, "schema": {"type": "boolean", "default": false}, "example": false}, {"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}, "example": 1}, {"name": "size", "in": "query", "description": "每页大小", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}, "example": 10}, {"name": "sortBy", "in": "query", "description": "排序字段", "required": false, "schema": {"type": "string"}, "example": "createdAt"}, {"name": "sortDirection", "in": "query", "description": "排序方向", "required": false, "schema": {"type": "string", "default": "desc"}, "example": "desc"}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultPageResultProductDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/featured": {"get": {"tags": ["商品管理"], "summary": "查询推荐商品列表", "description": "查询推荐商品列表", "operationId": "getFeaturedList", "parameters": [{"name": "limit", "in": "query", "description": "限制数量", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/exists/slug": {"get": {"tags": ["商品管理"], "summary": "检查商品slug是否存在", "description": "检查商品slug是否已存在", "operationId": "existsBySlug", "parameters": [{"name": "slug", "in": "query", "description": "商品slug", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/exists/name": {"get": {"tags": ["商品管理"], "summary": "检查商品名称是否存在", "description": "检查商品名称是否已存在", "operationId": "existsByName", "parameters": [{"name": "name", "in": "query", "description": "商品名称", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/category/{categoryId}": {"get": {"tags": ["商品管理"], "summary": "根据分类ID查询商品", "description": "根据分类ID查询商品列表", "operationId": "getByCategoryId", "parameters": [{"name": "categoryId", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/brand/{brandId}": {"get": {"tags": ["商品管理"], "summary": "根据品牌ID查询商品", "description": "根据品牌ID查询商品列表", "operationId": "getByBrandId", "parameters": [{"name": "brandId", "in": "path", "description": "品牌ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/active": {"get": {"tags": ["商品管理"], "summary": "查询活跃商品列表", "description": "查询所有上架的商品", "operationId": "getActiveList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListProductDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/products/batch": {"delete": {"tags": ["商品管理"], "summary": "批量删除商品", "description": "批量删除多个商品（逻辑删除）", "operationId": "batchDelete", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "商品ID列表", "items": {"type": "integer", "format": "int64"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"Image": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "图片ID", "example": 1001}, "imageUrl": {"type": "string", "description": "图片URL地址", "example": "https://your-bucket.oss-cn-hangzhou.aliyuncs.com/uploads/2024/01/15/1705312345678_abc123.jpg"}, "imageName": {"type": "string", "description": "图片名称", "example": "商品图片.jpg"}, "description": {"type": "string", "description": "图片描述", "example": "商品额外图片"}, "category": {"type": "string", "description": "图片分类", "example": "product"}, "businessType": {"type": "string", "description": "业务类型", "example": "product"}, "businessId": {"type": "integer", "format": "int64", "description": "关联的业务ID", "example": 1000}, "tags": {"type": "string", "description": "图片标签，多个标签用逗号分隔", "example": "商品,图片,详情"}, "fileSize": {"type": "integer", "format": "int64", "description": "文件大小（字节）", "example": 1048576}, "width": {"type": "integer", "format": "int32", "description": "图片宽度（像素）", "example": 1920}, "height": {"type": "integer", "format": "int32", "description": "图片高度（像素）", "example": 1080}, "fileType": {"type": "string", "description": "文件类型", "example": "jpg"}, "sortOrder": {"type": "integer", "format": "int32", "description": "排序序号", "example": 1}, "uploadedBy": {"type": "integer", "format": "int64", "description": "上传用户ID", "example": 100001}, "status": {"type": "integer", "format": "int32", "description": "状态：1-正常，0-删除", "example": 1}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间", "example": "2024-01-15T10:30:00Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "更新时间", "example": "2024-01-15T10:30:00Z"}}, "description": "图片信息"}, "BatchAssignCategoriesRequest": {"required": ["categoryIds", "mode", "productIds"], "type": "object", "properties": {"productIds": {"type": "array", "description": "商品ID列表", "example": [1, 2, 3], "items": {"type": "integer", "description": "商品ID列表", "format": "int64"}}, "categoryIds": {"type": "array", "description": "分类ID列表", "example": [1, 2], "items": {"type": "integer", "description": "分类ID列表", "format": "int32"}}, "primaryCategoryId": {"type": "integer", "description": "主分类ID（必须在分类ID列表中）", "format": "int32", "example": 1}, "mode": {"type": "string", "description": "操作模式：REPLACE-替换现有分类，ADD-追加分类", "example": "REPLACE", "enum": ["REPLACE", "ADD"]}, "validPrimaryCategory": {"type": "boolean"}}, "description": "批量分配分类请求"}, "CategoryDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "分类ID", "format": "int32", "example": 1}, "parentId": {"type": "integer", "description": "父分类ID", "format": "int32", "example": 1}, "name": {"type": "string", "description": "分类名称", "example": "眼镜框架"}, "slug": {"type": "string", "description": "URL友好标识", "example": "frames"}, "description": {"type": "string", "description": "分类描述", "example": "各种款式的眼镜框架"}, "imageUrl": {"type": "string", "description": "分类图片URL", "example": "https://example.com/category.png"}, "level": {"type": "integer", "description": "层级", "format": "int32", "example": 1}, "path": {"type": "string", "description": "层级路径", "example": "/1"}, "sortOrder": {"type": "integer", "description": "排序权重", "format": "int32", "example": 100}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}, "children": {"type": "array", "description": "子分类列表", "items": {"$ref": "#/components/schemas/CategoryDTO"}}, "productCount": {"type": "integer", "description": "商品数量", "format": "int32", "example": 10}, "topLevel": {"type": "boolean"}}, "description": "分类信息"}, "SkuUpdateOperation": {"required": ["operation"], "type": "object", "properties": {"operation": {"type": "string", "description": "操作类型", "example": "CREATE", "enum": ["CREATE", "UPDATE", "DELETE"]}, "skuId": {"type": "integer", "description": "SKU ID", "format": "int64", "example": 1}, "createData": {"$ref": "#/components/schemas/CreateSkuRequest"}, "updateData": {"$ref": "#/components/schemas/UpdateProductSkuRequest"}, "update": {"type": "boolean"}, "delete": {"type": "boolean"}, "create": {"type": "boolean"}, "valid": {"type": "boolean"}}, "description": "SKU更新操作"}, "UpdateProductSkuRequest": {"type": "object", "properties": {"skuCode": {"maxLength": 100, "minLength": 0, "type": "string", "description": "SKU编码", "example": "RB-CLASSIC-BLACK-M"}, "nameExtension": {"maxLength": 255, "minLength": 0, "type": "string", "description": "SKU名称后缀", "example": "黑色 中号"}, "price": {"minimum": 0.01, "exclusiveMinimum": false, "type": "number", "description": "销售价", "example": 399.0}, "originalPrice": {"minimum": 0.01, "exclusiveMinimum": false, "type": "number", "description": "原价", "example": 499.0}, "stockQuantity": {"minimum": 0, "type": "integer", "description": "库存数量", "format": "int32", "example": 50}, "lowStockThreshold": {"minimum": 0, "type": "integer", "description": "低库存预警值", "format": "int32", "example": 10}, "weightKg": {"minimum": 0.001, "exclusiveMinimum": false, "type": "number", "description": "重量", "example": 0.15}, "dimensionsCm": {"maxLength": 100, "minLength": 0, "type": "string", "description": "尺寸", "example": "14x5x3"}, "imageUrl": {"maxLength": 1024, "minLength": 0, "type": "string", "description": "SKU专属图片", "example": "https://example.com/sku.jpg"}, "attributes": {"type": "object", "additionalProperties": {"type": "string", "description": "规格属性", "example": "{\"color\":\"黑色\",\"size\":\"中号\"}"}, "description": "规格属性", "example": {"color": "黑色", "size": "中号"}}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}}, "description": "更新SKU请求"}, "ResultCategoryDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/CategoryDTO"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ProductWithSkusDTO": {"type": "object", "properties": {"product": {"$ref": "#/components/schemas/ProductDTO"}, "skus": {"type": "array", "description": "SKU列表", "items": {"$ref": "#/components/schemas/ProductSkuDTO"}}, "statistics": {"$ref": "#/components/schemas/ProductStatistics"}}, "description": "商品及其SKU信息"}, "CreateProductWithSkusRequest": {"required": ["product"], "type": "object", "properties": {"product": {"$ref": "#/components/schemas/CreateProductRequest"}, "skus": {"type": "array", "description": "SKU列表", "items": {"$ref": "#/components/schemas/CreateSkuRequest"}}, "autoGenerateSkuCodes": {"type": "boolean", "description": "是否自动生成SKU编码", "example": false}}, "description": "创建商品及SKU请求"}, "ResultProductDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/ProductDTO"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultListCategoryDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ProductSkuDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "SKU ID", "format": "int64", "example": 10000}, "productId": {"type": "integer", "description": "商品ID", "format": "int64", "example": 1000}, "skuCode": {"type": "string", "description": "SKU编码", "example": "RB-CLASSIC-BLACK-M"}, "nameExtension": {"type": "string", "description": "SKU名称后缀", "example": "黑色 中号"}, "price": {"type": "number", "description": "销售价", "example": 399.0}, "originalPrice": {"type": "number", "description": "原价", "example": 499.0}, "stockQuantity": {"type": "integer", "description": "库存数量", "format": "int32", "example": 50}, "lowStockThreshold": {"type": "integer", "description": "低库存预警值", "format": "int32", "example": 10}, "weightKg": {"type": "number", "description": "重量", "example": 0.15}, "dimensionsCm": {"type": "string", "description": "尺寸", "example": "14x5x3"}, "imageUrl": {"type": "string", "description": "SKU专属图片", "example": "https://example.com/sku.jpg"}, "attributes": {"type": "object", "additionalProperties": {"type": "string", "description": "规格属性"}, "description": "规格属性"}, "attributesJson": {"type": "string", "description": "规格属性JSON", "example": "{\"color\":\"黑色\",\"size\":\"中号\"}"}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}, "productName": {"type": "string", "description": "商品名称", "example": "雷朋经典款太阳镜"}, "discountRate": {"type": "number"}, "lowStock": {"type": "boolean"}, "fullName": {"type": "string"}}, "description": "商品SKU信息"}, "ResultProductWithSkusDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/ProductWithSkusDTO"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "PageResultProductDTO": {"type": "object", "properties": {"records": {"type": "array", "description": "数据列表", "items": {"$ref": "#/components/schemas/ProductDTO"}}, "total": {"type": "integer", "description": "总记录数", "format": "int64", "example": 100}, "pageNum": {"type": "integer", "description": "当前页码", "format": "int32", "example": 1}, "pageSize": {"type": "integer", "description": "每页大小", "format": "int32", "example": 10}, "totalPages": {"type": "integer", "description": "总页数", "format": "int32", "example": 10}, "hasNext": {"type": "boolean", "description": "是否有下一页", "example": true}, "hasPrevious": {"type": "boolean", "description": "是否有上一页", "example": false}, "empty": {"type": "boolean"}, "size": {"type": "integer", "format": "int32"}}, "description": "分页结果"}, "ProductStatistics": {"type": "object", "properties": {"totalSkuCount": {"type": "integer", "description": "SKU总数", "format": "int32", "example": 5}, "activeSkuCount": {"type": "integer", "description": "活跃SKU数量", "format": "int32", "example": 4}, "totalStockQuantity": {"type": "integer", "description": "总库存数量", "format": "int32", "example": 150}, "lowStockSkuCount": {"type": "integer", "description": "低库存SKU数量", "format": "int32", "example": 2}, "minPrice": {"type": "number", "description": "最低价格", "example": 299.0}, "maxPrice": {"type": "number", "description": "最高价格", "example": 599.0}}, "description": "商品统计信息"}, "CreateSkuRequest": {"required": ["price", "stockQuantity"], "type": "object", "properties": {"productId": {"type": "integer", "description": "关联商品ID", "format": "int64", "example": 1}, "skuCode": {"maxLength": 100, "minLength": 0, "type": "string", "description": "SKU编码", "example": "RB-CLASSIC-BLACK-M"}, "nameExtension": {"maxLength": 255, "minLength": 0, "type": "string", "description": "SKU名称后缀", "example": "黑色 中号"}, "price": {"minimum": 0.01, "exclusiveMinimum": false, "type": "number", "description": "销售价", "example": 399.0}, "originalPrice": {"minimum": 0.01, "exclusiveMinimum": false, "type": "number", "description": "原价", "example": 499.0}, "stockQuantity": {"minimum": 0, "type": "integer", "description": "库存数量", "format": "int32", "example": 50}, "lowStockThreshold": {"minimum": 0, "type": "integer", "description": "低库存预警值", "format": "int32", "example": 10}, "weightKg": {"minimum": 0.001, "exclusiveMinimum": false, "type": "number", "description": "重量", "example": 0.15}, "dimensionsCm": {"maxLength": 100, "minLength": 0, "type": "string", "description": "尺寸", "example": "14x5x3"}, "imageUrl": {"maxLength": 1024, "minLength": 0, "type": "string", "description": "SKU专属图片", "example": "https://example.com/sku.jpg"}, "attributes": {"type": "object", "additionalProperties": {"type": "string", "description": "规格属性", "example": "{\"color\":\"黑色\",\"size\":\"中号\"}"}, "description": "规格属性", "example": {"color": "黑色", "size": "中号"}}, "isActive": {"type": "boolean", "description": "启用状态", "example": true}}, "description": "创建SKU请求"}, "ResultListProductDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultBoolean": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "boolean"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultPageResultProductDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageResultProductDTO"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ProductDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "商品ID", "format": "int64", "example": 1000}, "name": {"type": "string", "description": "商品名称", "example": "雷朋经典款太阳镜"}, "slug": {"type": "string", "description": "URL友好标识", "example": "rayban-classic-sunglasses"}, "brandId": {"type": "integer", "description": "品牌ID", "format": "int32", "example": 1}, "brandName": {"type": "string", "description": "品牌名称", "example": "雷朋"}, "shortDescription": {"type": "string", "description": "简短描述", "example": "经典款太阳镜，时尚百搭"}, "fullDescription": {"type": "string", "description": "详细描述"}, "mainImageUrl": {"type": "string", "description": "主图URL", "example": "https://example.com/product.jpg"}, "additionalImages": {"type": "array", "items": {"$ref": "#/components/schemas/Image"}, "description": "额外图片列表（除主图外的其他图片）"}, "isActive": {"type": "boolean", "description": "上架状态", "example": true}, "isFeatured": {"type": "boolean", "description": "推荐状态", "example": false}, "publishedAt": {"type": "string", "description": "首次发布时间", "format": "date-time"}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}, "categories": {"type": "array", "description": "商品分类列表", "items": {"$ref": "#/components/schemas/CategoryDTO"}}, "skus": {"type": "array", "description": "商品SKU列表", "items": {"$ref": "#/components/schemas/ProductSkuDTO"}}, "minPrice": {"type": "string", "description": "最低价格", "example": "299.0"}, "maxPrice": {"type": "string", "description": "最高价格", "example": "599.0"}, "totalStock": {"type": "integer", "description": "总库存", "format": "int32", "example": 100}, "priceRange": {"type": "string"}, "published": {"type": "boolean"}}, "description": "商品信息"}, "ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "CreateProductRequest": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 255, "minLength": 0, "type": "string", "description": "商品名称", "example": "雷朋经典款太阳镜"}, "slug": {"maxLength": 255, "minLength": 0, "type": "string", "description": "URL友好标识（可选，为空时自动生成）", "example": "rayban-classic-sunglasses"}, "brandId": {"type": "integer", "description": "品牌ID", "format": "int32", "example": 1}, "shortDescription": {"maxLength": 500, "minLength": 0, "type": "string", "description": "简短描述", "example": "经典款太阳镜，时尚百搭"}, "fullDescription": {"maxLength": 10000, "minLength": 0, "type": "string", "description": "详细描述"}, "mainImageUrl": {"maxLength": 1024, "minLength": 0, "type": "string", "description": "主图URL", "example": "https://example.com/product.jpg"}, "additionalImageUrls": {"type": "array", "items": {"type": "string"}, "description": "额外图片URL列表（除主图外的其他图片）", "example": ["https://example.com/product2.jpg", "https://example.com/product3.jpg"]}, "isActive": {"type": "boolean", "description": "上架状态", "example": true}, "isFeatured": {"type": "boolean", "description": "推荐状态", "example": false}, "categoryIds": {"type": "array", "description": "分类ID列表", "example": [1, 2], "items": {"type": "integer", "description": "分类ID列表", "format": "int32"}}, "primaryCategoryId": {"type": "integer", "description": "主分类ID", "format": "int32", "example": 1}}, "description": "创建商品请求"}, "UpdateProductWithSkusRequest": {"required": ["product"], "type": "object", "properties": {"product": {"$ref": "#/components/schemas/CreateProductRequest"}, "skuOperations": {"type": "array", "description": "SKU操作列表", "items": {"$ref": "#/components/schemas/SkuUpdateOperation"}}, "autoGenerateSkuCodes": {"type": "boolean", "description": "是否自动生成SKU编码", "example": false}}, "description": "更新商品及SKU请求"}}}}