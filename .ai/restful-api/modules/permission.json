{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 权限管理", "description": "权限管理相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "权限管理", "description": "权限相关接口"}], "paths": {"/api/admin/permissions/{id}": {"get": {"tags": ["权限管理"], "summary": "根据ID查询权限", "description": "根据权限ID查询权限详细信息", "operationId": "getById_6", "parameters": [{"name": "id", "in": "path", "description": "权限ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultAdminRolePermissionDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "put": {"tags": ["权限管理"], "summary": "更新权限", "description": "更新权限信息", "operationId": "update_6", "parameters": [{"name": "id", "in": "path", "description": "权限ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminRolePermissionDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "delete": {"tags": ["权限管理"], "summary": "删除权限", "description": "删除指定的权限", "operationId": "delete_6", "parameters": [{"name": "id", "in": "path", "description": "权限ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/permissions": {"get": {"tags": ["权限管理"], "summary": "查询权限列表", "description": "分页查询权限列表", "operationId": "getList_2", "parameters": [{"name": "roleId", "in": "query", "description": "角色ID", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "description": "权限键", "required": false, "schema": {"type": "string"}}, {"name": "permissionName", "in": "query", "description": "权限名称", "required": false, "schema": {"type": "string"}}, {"name": "permissionType", "in": "query", "description": "权限类型：1-菜单，2-按钮，3-接口", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "size", "in": "query", "description": "每页大小", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListAdminRolePermissionDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "post": {"tags": ["权限管理"], "summary": "创建权限", "description": "创建新的权限", "operationId": "create_6", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminRolePermissionDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/permissions/role/{roleId}": {"get": {"tags": ["权限管理"], "summary": "根据角色ID查询权限", "description": "根据角色ID查询该角色拥有的权限", "operationId": "getByRoleId", "parameters": [{"name": "roleId", "in": "path", "description": "角色ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListAdminRolePermission"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/permissions/all": {"get": {"tags": ["权限管理"], "summary": "查询所有权限", "description": "查询所有权限，用于分配权限", "operationId": "getAllPermissions", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListAdminRolePermission"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/permissions/key/{permissionKey}": {"delete": {"tags": ["权限管理"], "summary": "根据权限键删除权限", "description": "根据权限键删除权限", "operationId": "deleteByPermissionKey", "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "description": "权限键", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "AdminRolePermission": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "roleId": {"type": "integer", "format": "int64"}, "permissionKey": {"type": "string"}, "permissionName": {"type": "string"}, "description": {"type": "string"}, "permissionType": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "createBy": {"type": "string"}}}, "ResultListAdminRolePermission": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AdminRolePermission"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "AdminRolePermissionDTO": {"required": ["<PERSON><PERSON><PERSON>", "permissionName", "permissionType", "roleId"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "roleId": {"type": "integer", "format": "int64"}, "permissionKey": {"type": "string"}, "permissionName": {"type": "string"}, "description": {"type": "string"}, "permissionType": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}}}, "ResultAdminRolePermissionDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminRolePermissionDTO"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultListAdminRolePermissionDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AdminRolePermissionDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}}}}