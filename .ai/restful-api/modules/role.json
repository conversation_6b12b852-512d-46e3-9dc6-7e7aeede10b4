{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 角色管理", "description": "角色管理相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "角色管理", "description": "角色相关接口"}], "paths": {"/api/admin/roles/{id}": {"get": {"tags": ["角色管理"], "summary": "根据ID查询角色", "description": "根据角色ID查询角色详细信息", "operationId": "getById_5", "parameters": [{"name": "id", "in": "path", "description": "角色ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultAdminRoleDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "put": {"tags": ["角色管理"], "summary": "更新角色", "description": "更新角色信息", "operationId": "update_5", "parameters": [{"name": "id", "in": "path", "description": "角色ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminRoleDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "delete": {"tags": ["角色管理"], "summary": "删除角色", "description": "删除指定的角色", "operationId": "delete_5", "parameters": [{"name": "id", "in": "path", "description": "角色ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/roles/{id}/status": {"put": {"tags": ["角色管理"], "summary": "更新状态", "description": "启用或禁用角色", "operationId": "updateStatus_5", "parameters": [{"name": "id", "in": "path", "description": "角色ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "status", "in": "query", "description": "状态：1-启用，0-禁用", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/roles/{id}/permissions": {"put": {"tags": ["角色管理"], "summary": "分配权限", "description": "为角色分配权限", "operationId": "assignPermissions", "parameters": [{"name": "id", "in": "path", "description": "角色ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "权限键列表", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/roles": {"get": {"tags": ["角色管理"], "summary": "查询角色列表", "description": "分页查询角色列表", "operationId": "getList_1", "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "query", "description": "角色名称", "required": false, "schema": {"type": "string"}}, {"name": "roleCode", "in": "query", "description": "角色编码", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "状态：1-启用，0-禁用", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "size", "in": "query", "description": "每页大小", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListAdminRoleDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "post": {"tags": ["角色管理"], "summary": "创建角色", "description": "创建新的角色", "operationId": "create_5", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminRoleDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/admin/roles/enabled": {"get": {"tags": ["角色管理"], "summary": "查询启用的角色", "description": "查询所有启用的角色，用于下拉选择", "operationId": "getEnabledRoles", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListAdminRole"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "AdminRoleDTO": {"required": ["roleCode", "<PERSON><PERSON><PERSON>", "status"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "roleName": {"type": "string"}, "roleCode": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "sort": {"type": "integer", "format": "int32"}, "permissionKeys": {"type": "array", "items": {"type": "string"}}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/AdminRolePermissionDTO"}}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}}}, "AdminRole": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "roleName": {"type": "string"}, "roleCode": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "isDeleted": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}}}, "ResultListAdminRole": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AdminRole"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "AdminRolePermissionDTO": {"required": ["<PERSON><PERSON><PERSON>", "permissionName", "permissionType", "roleId"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "roleId": {"type": "integer", "format": "int64"}, "permissionKey": {"type": "string"}, "permissionName": {"type": "string"}, "description": {"type": "string"}, "permissionType": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}}}, "ResultAdminRoleDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminRoleDTO"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultListAdminRoleDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AdminRoleDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}}}}