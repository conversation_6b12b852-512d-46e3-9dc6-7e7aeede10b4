{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 用户购物车", "description": "用户购物车相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "用户购物车", "description": "用户购物车相关接口"}], "paths": {"/api/user/cart/items/{cartItemId}": {"put": {"tags": ["用户购物车"], "summary": "更新购物车项数量", "description": "更新指定购物车项的商品数量", "operationId": "updateCartItem", "parameters": [{"name": "cartItemId", "in": "path", "description": "购物车项ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCartItemRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}, "delete": {"tags": ["用户购物车"], "summary": "删除购物车项", "description": "从购物车中删除指定商品", "operationId": "removeCartItem", "parameters": [{"name": "cartItemId", "in": "path", "description": "购物车项ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/cart/add": {"post": {"tags": ["用户购物车"], "summary": "添加商品到购物车", "description": "将指定商品添加到用户购物车，如果商品已存在则增加数量", "operationId": "addToCart", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddToCartRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/cart/summary": {"get": {"tags": ["用户购物车"], "summary": "获取购物车汇总", "description": "获取购物车汇总信息，包括商品列表、总数量、总金额等", "operationId": "getCartSummary", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultCartSummaryDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/cart/items": {"get": {"tags": ["用户购物车"], "summary": "获取购物车列表", "description": "获取当前用户的购物车商品列表", "operationId": "getCartItems", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCartItemDTO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/cart/count": {"get": {"tags": ["用户购物车"], "summary": "获取购物车商品数量", "description": "获取当前用户购物车中的商品总数量", "operationId": "getCartItemCount", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/cart/items/batch": {"delete": {"tags": ["用户购物车"], "summary": "批量删除购物车项", "description": "批量删除多个购物车项", "operationId": "batchRemoveCartItems", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "购物车项ID列表", "items": {"type": "integer", "format": "int64"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/cart/clear": {"delete": {"tags": ["用户购物车"], "summary": "清空购物车", "description": "清空当前用户的所有购物车商品", "operationId": "clearCart", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "AddToCartRequest": {"required": ["quantity", "skuId"], "type": "object", "properties": {"skuId": {"type": "integer", "description": "SKU ID", "format": "int64", "example": 1}, "quantity": {"minimum": 1, "type": "integer", "description": "购买数量", "format": "int32", "example": 2}}, "description": "添加到购物车请求"}, "UpdateCartItemRequest": {"required": ["quantity"], "type": "object", "properties": {"quantity": {"minimum": 1, "type": "integer", "description": "购买数量", "format": "int32", "example": 3}}, "description": "更新购物车项请求"}, "CartSummaryDTO": {"type": "object", "properties": {"items": {"type": "array", "description": "购物车项列表", "items": {"$ref": "#/components/schemas/CartItemDTO"}}, "totalQuantity": {"type": "integer", "description": "商品总数量", "format": "int32", "example": 5}, "totalAmount": {"type": "number", "description": "商品总金额", "example": 1299.0}, "validQuantity": {"type": "integer", "description": "有效商品数量", "format": "int32", "example": 4}, "validAmount": {"type": "number", "description": "有效商品金额", "example": 999.0}, "allInStock": {"type": "boolean", "description": "是否全部有库存", "example": false}}, "description": "购物车汇总信息"}, "CartItemDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "购物车项ID", "format": "int64", "example": 1}, "userId": {"type": "integer", "description": "用户ID", "format": "int64", "example": 1}, "skuId": {"type": "integer", "description": "SKU ID", "format": "int64", "example": 1}, "skuCode": {"type": "string", "description": "SKU编码", "example": "GLASSES-001-BLACK-M"}, "productId": {"type": "integer", "description": "商品ID", "format": "int64", "example": 1}, "productName": {"type": "string", "description": "商品名称", "example": "时尚眼镜框"}, "productImageUrl": {"type": "string", "description": "商品图片URL", "example": "https://example.com/image.jpg"}, "skuAttributes": {"type": "string", "description": "SKU属性描述", "example": "颜色:黑色,尺寸:中号"}, "unitPrice": {"type": "number", "description": "单价", "example": 299.0}, "quantity": {"type": "integer", "description": "购买数量", "format": "int32", "example": 2}, "subtotal": {"type": "number", "description": "小计金额", "example": 598.0}, "stockQuantity": {"type": "integer", "description": "库存数量", "format": "int32", "example": 100}, "inStock": {"type": "boolean", "description": "是否有库存", "example": true}, "addedAt": {"type": "string", "description": "加入购物车时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "最后更新时间", "format": "date-time"}}, "description": "购物车项信息"}, "ResultCartSummaryDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/CartSummaryDTO"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultInteger": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "integer", "format": "int32"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultListCartItemDTO": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CartItemDTO"}}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}}}}