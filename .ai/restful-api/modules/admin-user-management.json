{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 管理员端用户管理", "description": "管理员端用户管理相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "管理员端用户管理", "description": "管理员管理用户相关接口"}], "paths": {"/admin/users": {"get": {"tags": ["管理员端用户管理"], "summary": "分页查询用户列表", "description": "管理员分页查询用户列表，支持多条件筛选和排序", "operationId": "getUserPageList", "parameters": [{"name": "userId", "in": "query", "description": "用户ID", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "wechatOpenid", "in": "query", "description": "微信OpenID（模糊查询）", "required": false, "schema": {"type": "string"}}, {"name": "nickname", "in": "query", "description": "昵称（模糊查询）", "required": false, "schema": {"type": "string"}}, {"name": "phoneNumber", "in": "query", "description": "手机号（模糊查询）", "required": false, "schema": {"type": "string"}}, {"name": "gender", "in": "query", "description": "性别（0-未知，1-男，2-女）", "required": false, "schema": {"type": "integer", "minimum": 0, "maximum": 2}}, {"name": "country", "in": "query", "description": "国家（模糊查询）", "required": false, "schema": {"type": "string"}}, {"name": "province", "in": "query", "description": "省份（模糊查询）", "required": false, "schema": {"type": "string"}}, {"name": "city", "in": "query", "description": "城市（模糊查询）", "required": false, "schema": {"type": "string"}}, {"name": "isDeleted", "in": "query", "description": "是否删除（true-已删除，false-正常，null-全部）", "required": false, "schema": {"type": "boolean"}}, {"name": "startTime", "in": "query", "description": "创建开始时间（格式：yyyy-MM-dd HH:mm:ss）", "required": false, "schema": {"type": "string"}}, {"name": "endTime", "in": "query", "description": "创建结束时间（格式：yyyy-MM-dd HH:mm:ss）", "required": false, "schema": {"type": "string"}}, {"name": "sortBy", "in": "query", "description": "排序字段（create_time, update_time, last_login_time）", "required": false, "schema": {"type": "string", "default": "create_time"}}, {"name": "sortOrder", "in": "query", "description": "排序方向（ASC, DESC）", "required": false, "schema": {"type": "string", "default": "DESC"}}, {"name": "page", "in": "query", "description": "页码（从1开始）", "required": false, "schema": {"type": "integer", "default": 1, "minimum": 1}}, {"name": "size", "in": "query", "description": "每页大小", "required": false, "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 100}}], "responses": {"200": {"description": "查询成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultPageResultUserManagementResponse"}}}}, "400": {"description": "请求参数错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "401": {"description": "未授权", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "403": {"description": "权限不足", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/admin/users/{id}": {"get": {"tags": ["管理员端用户管理"], "summary": "根据ID查询用户详情", "description": "管理员根据用户ID查询用户详细信息（包括已删除用户）", "operationId": "getUserById", "parameters": [{"name": "id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "查询成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserManagementDetailResponse"}}}}, "400": {"description": "请求参数错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "401": {"description": "未授权", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "403": {"description": "权限不足", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "404": {"description": "用户不存在", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/admin/users/{id}/status": {"put": {"tags": ["管理员端用户管理"], "summary": "更新用户状态", "description": "管理员删除或恢复用户（逻辑删除）", "operationId": "updateUserStatus", "parameters": [{"name": "id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "isDeleted", "in": "query", "description": "删除状态：true-删除，false-恢复", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "更新成功", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "400": {"description": "请求参数错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "401": {"description": "未授权", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "403": {"description": "权限不足", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "404": {"description": "用户不存在", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "服务器内部错误", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultPageResultUserManagementResponse": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageResultUserManagementResponse"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultUserManagementDetailResponse": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/UserManagementDetailResponse"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "PageResultUserManagementResponse": {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/components/schemas/UserManagementResponse"}}, "total": {"type": "integer", "format": "int64", "description": "总记录数"}, "page": {"type": "integer", "description": "当前页码"}, "size": {"type": "integer", "description": "每页大小"}, "pages": {"type": "integer", "description": "总页数"}}}, "UserManagementResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "用户ID"}, "wechatOpenid": {"type": "string", "description": "微信OpenID"}, "nickname": {"type": "string", "description": "昵称"}, "avatarUrl": {"type": "string", "description": "头像URL"}, "phoneNumber": {"type": "string", "description": "手机号"}, "gender": {"type": "integer", "description": "性别（0-未知，1-男，2-女）"}, "country": {"type": "string", "description": "国家"}, "province": {"type": "string", "description": "省份"}, "city": {"type": "string", "description": "城市"}, "isDeleted": {"type": "boolean", "description": "是否删除"}, "lastLoginTime": {"type": "string", "format": "date-time", "description": "最后登录时间"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "UserManagementDetailResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "用户ID"}, "wechatOpenid": {"type": "string", "description": "微信OpenID"}, "nickname": {"type": "string", "description": "昵称"}, "avatarUrl": {"type": "string", "description": "头像URL"}, "phoneNumber": {"type": "string", "description": "手机号"}, "gender": {"type": "integer", "description": "性别（0-未知，1-男，2-女）"}, "country": {"type": "string", "description": "国家"}, "province": {"type": "string", "description": "省份"}, "city": {"type": "string", "description": "城市"}, "isDeleted": {"type": "boolean", "description": "是否删除"}, "lastLoginTime": {"type": "string", "format": "date-time", "description": "最后登录时间"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}}}}}}