{"openapi": "3.0.1", "info": {"title": "眼镜商城系统 API - 用户个人信息", "description": "用户个人信息相关接口文档", "contact": {"name": "开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "用户个人信息", "description": "用户个人信息相关接口"}], "paths": {"/api/user/profile/current": {"get": {"tags": ["用户个人信息"], "summary": "获取当前用户信息", "description": "获取当前登录用户的详细信息", "operationId": "getCurrentUserProfile", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserSession"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/api/user/profile/check": {"get": {"tags": ["用户个人信息"], "summary": "检查用户登录状态", "description": "检查当前用户登录状态是否有效", "operationId": "checkLoginStatus", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"ResultUserSession": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/UserSession"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ResultVoid": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "UserSession": {"type": "object", "properties": {"sessionId": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}, "wechatOpenid": {"type": "string"}, "wechatUnionid": {"type": "string"}, "nickname": {"type": "string"}, "avatarUrl": {"type": "string"}, "gender": {"type": "integer", "format": "int32"}, "phoneNumber": {"type": "string"}, "country": {"type": "string"}, "province": {"type": "string"}, "city": {"type": "string"}, "loginTime": {"type": "string", "format": "date-time"}, "lastAccessTime": {"type": "string", "format": "date-time"}, "loginIp": {"type": "string"}, "userAgent": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "isNewUser": {"type": "boolean"}}}, "ResultBoolean": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "boolean"}, "timestamp": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}}}}