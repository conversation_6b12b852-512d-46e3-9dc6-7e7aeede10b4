# 微信手机号获取功能实现说明

## 🎯 功能概述

根据后端接口文档实现了完整的微信手机号获取功能：
- 用户点击【我的】页面【用户昵称】下面的【获取手机号】按钮
- 弹出微信授权弹窗
- 用户授权后获取手机号并展示

## ✅ 已实现的功能

### 1. 后端接口对接 (`utils/auth.ts`)

#### 新增获取手机号方法
```typescript
async getPhoneNumber(code: string): Promise<{success: boolean; message: string; phoneNumber?: string}> {
  // 1. 检查登录状态
  if (!this.isLoggedIn()) {
    return { success: false, message: '请先登录' }
  }

  // 2. 调用后端接口
  const result = await this.sendPhoneRequest(code)
  
  // 3. 更新本地用户信息
  const updatedUser = {
    ...currentUser,
    phone: result.data.purePhoneNumber,
    phoneNumber: result.data.purePhoneNumber,
    hasPhoneNumber: true
  }
  
  this.saveUserInfo(updatedUser)
  return { success: true, message: '手机号获取成功', phoneNumber: result.data.purePhoneNumber }
}
```

#### 后端接口调用
```typescript
private async sendPhoneRequest(code: string): Promise<any> {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${this.baseUrl}/api/auth/phone/get`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'sessionId': token,
        'appId': this.appId
      },
      data: { code: code },
      success: (res) => resolve(res.data),
      fail: reject
    })
  })
}
```

### 2. 页面交互实现 (`pages/profile/profile.ts`)

#### 获取手机号授权回调
```typescript
getPhoneNumber(e: any) {
  console.log('getPhoneNumber event:', e)
  
  if (e.detail.code) {
    // 用户同意授权，获取到code
    this.requestPhoneNumber(e.detail.code)
  } else {
    // 用户拒绝授权
    wx.showToast({
      title: '需要手机号授权才能继续',
      icon: 'none',
      duration: 2000
    })
  }
}
```

#### 调用后端接口
```typescript
async requestPhoneNumber(code: string) {
  wx.showLoading({ title: '获取手机号中...' })

  try {
    const result = await AuthManager.getPhoneNumber(code)
    
    if (result.success) {
      wx.showToast({ title: result.message, icon: 'success' })
      this.getUserInfo() // 更新页面显示
    } else {
      wx.showToast({ title: result.message, icon: 'none', duration: 2000 })
    }
  } catch (error) {
    wx.showToast({ title: '获取手机号失败，请重试', icon: 'none' })
  } finally {
    wx.hideLoading()
  }
}
```

### 3. 页面模板更新 (`pages/profile/profile.wxml`)

#### 手机号显示区域
```xml
<!-- 手机号显示区域 -->
<view class="phone-section">
  <!-- 已有手机号：显示手机号 -->
  <view wx:if="{{userInfo.hasPhoneNumber}}" class="phone-info">
    <text class="phone">手机号: {{userInfo.phone}}</text>
  </view>
  
  <!-- 已登录但无手机号：显示获取按钮 -->
  <button wx:elif="{{isLoggedIn}}" 
          class="get-phone-btn" 
          open-type="getPhoneNumber" 
          bindgetphonenumber="getPhoneNumber">
    获取手机号
  </button>
  
  <!-- 未登录：显示默认手机号 -->
  <text wx:else class="phone">手机号: 138****8888</text>
</view>
```

### 4. 样式设计 (`pages/profile/profile.wxss`)

#### 获取手机号按钮样式
```css
.get-phone-btn {
  background: linear-gradient(135deg, #07c160, #05a850);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  padding: 12rpx 24rpx;
  margin: 8rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
}

.get-phone-btn.button-hover {
  background: linear-gradient(135deg, #05a850, #048a43);
  transform: scale(0.98);
}
```

## 🔄 用户体验流程

### 1. 未登录状态
```
显示：手机号: 138****8888
操作：无法获取手机号
```

### 2. 已登录未获取手机号
```
显示：[获取手机号] 按钮（绿色渐变）
点击：弹出微信授权弹窗
授权：调用后端接口获取手机号
成功：显示真实手机号
```

### 3. 已登录已获取手机号
```
显示：手机号: 138****8888
操作：无需再次获取
```

### 4. 用户拒绝授权
```
显示：提示"需要手机号授权才能继续"
操作：可以重新点击按钮再次授权
```

## 🔧 技术实现要点

### 1. 微信授权机制
- 使用 `open-type="getPhoneNumber"` 触发微信授权
- 在 `bindgetphonenumber` 回调中获取授权码
- 授权码有效期很短，需要立即发送到后端

### 2. 后端接口对接
- 接口地址：`POST /api/auth/phone/get`
- 请求头：`sessionId` 和 `appId`
- 请求体：`{code: "phone_code_from_wechat_api"}`

### 3. 状态管理
- 使用 `hasPhoneNumber` 字段标记是否已获取手机号
- 本地存储用户信息包含手机号数据
- 页面状态实时更新

### 4. 错误处理
- 用户拒绝授权：友好提示
- 网络错误：显示重试提示
- 会话过期：自动处理登录状态

## 📱 界面效果

### 未登录状态
```
[默认头像]  用户昵称
           手机号: 138****8888
           点击登录获取完整功能
```

### 已登录未获取手机号
```
[头像]     完善信息 ← 可点击
           点击完善头像和昵称
           [获取手机号] ← 绿色按钮
```

### 已登录已获取手机号
```
[头像]     张三
           手机号: 138****8888
```

## 🧪 测试场景

### 测试场景1：正常获取手机号
1. 用户已登录但未获取手机号
2. 点击"获取手机号"按钮
3. 弹出微信授权弹窗
4. 用户点击"允许"
5. 显示"手机号获取成功"
6. 页面显示真实手机号

### 测试场景2：用户拒绝授权
1. 用户已登录但未获取手机号
2. 点击"获取手机号"按钮
3. 弹出微信授权弹窗
4. 用户点击"拒绝"
5. 显示"需要手机号授权才能继续"
6. 按钮仍然可以再次点击

### 测试场景3：重复获取
1. 用户已获取手机号
2. 页面显示真实手机号
3. 不显示"获取手机号"按钮

### 测试场景4：会话过期
1. 用户sessionId已过期
2. 点击"获取手机号"按钮
3. 后端返回401错误
4. 提示"登录已过期"

## 🔍 调试方法

### 1. 控制台日志
```typescript
// 查看授权回调
console.log('getPhoneNumber event:', e)

// 查看后端响应
console.log('手机号接口响应:', res.data)

// 查看用户信息更新
console.log('更新后的用户信息:', updatedUser)
```

### 2. 网络请求调试
- 在微信开发者工具的Network面板查看请求
- 检查请求头是否包含正确的sessionId和appId
- 查看响应数据结构是否符合预期

### 3. 存储调试
```javascript
// 查看本地存储
console.log('用户信息:', wx.getStorageSync('user_info'))
console.log('登录凭证:', wx.getStorageSync('user_token'))
```

## 🎯 核心优势

1. **符合微信规范**：使用官方推荐的 `open-type="getPhoneNumber"` 方式
2. **用户体验好**：清晰的状态提示和流畅的交互
3. **错误处理完善**：覆盖各种异常情况
4. **状态管理清晰**：实时更新页面显示状态
5. **安全可靠**：通过后端接口安全获取手机号

## 📋 注意事项

1. **权限配置**：确保小程序已开通手机号获取权限
2. **授权码时效**：微信返回的code有效期很短，需要立即使用
3. **用户体验**：提供清晰的加载状态和错误提示
4. **数据安全**：手机号等敏感信息通过HTTPS传输

现在您的微信小程序已经具备了完整的手机号获取功能！
