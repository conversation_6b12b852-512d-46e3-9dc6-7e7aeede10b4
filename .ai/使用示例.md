# 优化后的登录功能使用示例

## 1. 配置AppId

首先在 `utils/config.ts` 中配置您的小程序AppId：

```typescript
export const CONFIG = {
  API_BASE_URL: 'http://localhost:8080',
  
  WECHAT: {
    APP_ID: 'wx1234567890abcdef', // 替换为您的实际AppId
  },
  
  // ... 其他配置
}
```

## 2. 在页面中使用登录检查

### 示例1：购物车结算时检查登录
```typescript
// pages/cart/cart.ts
import LoginHelper from '../../utils/loginHelper'

Page({
  checkout() {
    // 检查登录状态
    if (!LoginHelper.checkLogin()) {
      return // 未登录，已显示提示
    }
    
    // 已登录，继续结算流程
    wx.navigateTo({
      url: '/pages/checkout/checkout'
    })
  }
})
```

### 示例2：收藏商品时检查登录
```typescript
// pages/product/product.ts
import LoginHelper from '../../utils/loginHelper'

Page({
  addToFavorites() {
    LoginHelper.requireLogin(() => {
      // 登录成功后执行收藏操作
      this.doAddToFavorites()
    }, () => {
      // 用户未登录或取消登录
      console.log('用户未登录，无法收藏')
    })
  },
  
  doAddToFavorites() {
    // 实际的收藏逻辑
    wx.showToast({
      title: '收藏成功',
      icon: 'success'
    })
  }
})
```

## 3. API请求示例

### 自动携带请求头
```typescript
// 任何页面中
import RequestManager from '../../utils/request'

Page({
  async loadUserOrders() {
    try {
      // 请求会自动携带 sessionId 和 appId
      const orders = await RequestManager.get('/api/orders/list')
      console.log('用户订单:', orders)
    } catch (error) {
      console.error('获取订单失败:', error)
    }
  },
  
  async updateUserProfile(profileData) {
    try {
      // POST请求也会自动携带请求头
      const result = await RequestManager.post('/api/user/profile', profileData)
      console.log('更新成功:', result)
    } catch (error) {
      console.error('更新失败:', error)
    }
  }
})
```

### 实际发送的请求头
```
POST /api/user/profile HTTP/1.1
Host: localhost:8080
Content-Type: application/json
sessionId: 2b76e99b855c43748eb8935cee2146a4
appId: wx1234567890abcdef

{
  "nickname": "新昵称",
  "avatar": "新头像URL"
}
```

## 4. 【我的】页面登录流程

### 用户体验流程
1. 用户点击底部导航的【我的】
2. 页面自动检查登录状态
3. 未登录时弹出提示："请先登录以获取完整功能"
4. 用户点击"立即登录"
5. 触发微信授权弹窗
6. 用户确认授权
7. 登录成功，页面自动刷新显示用户信息

### 退出登录流程
1. 已登录用户在【我的】页面看到"退出"按钮
2. 点击"退出"按钮
3. 弹出确认对话框："确定要退出登录吗？"
4. 用户点击"确定"
5. 清理本地存储，显示"已退出登录"
6. 页面状态刷新为未登录状态
7. 用户停留在当前页面

## 5. 错误处理示例

### 登录过期处理
```typescript
// 当API返回401时，RequestManager会自动处理
// 显示"登录已过期"提示，清理本地存储
// 用户需要重新登录
```

### 网络错误处理
```typescript
Page({
  async loadData() {
    try {
      const data = await RequestManager.get('/api/data')
      // 处理成功响应
    } catch (error) {
      // 处理网络错误或其他错误
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  }
})
```

## 6. 完整的页面示例

```typescript
// pages/orders/orders.ts
import AuthManager from '../../utils/auth'
import RequestManager from '../../utils/request'
import LoginHelper from '../../utils/loginHelper'

Page({
  data: {
    orders: [],
    loading: false
  },

  onLoad() {
    this.loadOrders()
  },

  onShow() {
    // 页面显示时检查登录状态
    if (AuthManager.isLoggedIn()) {
      this.loadOrders()
    }
  },

  async loadOrders() {
    // 检查登录状态
    if (!LoginHelper.checkLogin()) {
      return
    }

    this.setData({ loading: true })

    try {
      // 自动携带sessionId和appId
      const orders = await RequestManager.get('/api/orders/list')
      this.setData({ orders })
    } catch (error) {
      wx.showToast({
        title: '加载订单失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  async cancelOrder(e) {
    const orderId = e.currentTarget.dataset.id
    
    try {
      await RequestManager.post(`/api/orders/${orderId}/cancel`)
      wx.showToast({
        title: '订单已取消',
        icon: 'success'
      })
      // 重新加载订单列表
      this.loadOrders()
    } catch (error) {
      wx.showToast({
        title: '取消失败',
        icon: 'none'
      })
    }
  }
})
```

## 7. 后端接口适配

### 后端需要从请求头获取信息
```java
@RestController
public class OrderController {
    
    @GetMapping("/api/orders/list")
    public Result getOrderList(HttpServletRequest request) {
        // 从请求头获取sessionId和appId
        String sessionId = request.getHeader("sessionId");
        String appId = request.getHeader("appId");
        
        // 验证sessionId
        if (!sessionService.isValid(sessionId)) {
            return Result.error(401, "登录已过期");
        }
        
        // 验证appId
        if (!appId.equals(configService.getAppId())) {
            return Result.error(403, "无效的应用ID");
        }
        
        // 获取用户信息
        User user = sessionService.getUserBySessionId(sessionId);
        
        // 查询订单
        List<Order> orders = orderService.getOrdersByUserId(user.getId());
        
        return Result.success(orders);
    }
}
```

## 8. 调试技巧

### 查看请求头
在微信开发者工具的Network面板中可以看到：
```
Request Headers:
Content-Type: application/json
sessionId: 2b76e99b855c43748eb8935cee2146a4
appId: wx1234567890abcdef
```

### 查看本地存储
在微信开发者工具的Storage面板中可以看到：
```
user_token: 2b76e99b855c43748eb8935cee2146a4
user_info: {"id":1,"nickName":"用户昵称",...}
```

### 控制台调试
```javascript
// 在控制台中检查登录状态
console.log('登录状态:', AuthManager.isLoggedIn())
console.log('用户信息:', AuthManager.getUserInfo())
console.log('AppId:', AuthManager.getAppId())
```
