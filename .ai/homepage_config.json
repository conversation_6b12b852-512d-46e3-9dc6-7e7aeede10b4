{"data": [{"configKey": "announcement.information.config", "configValue": "夏日墨镜大促，大额优惠来袭！", "configType": "text", "category": "homepage", "description": "首页公告信息配置", "createdAt": "2025-07-27T21:16:14", "updatedAt": "2025-07-28T14:49:41"}, {"configKey": "homepage.banner.config", "configValue": "{\"banners\":[{\"title\":\"截屏2025-02-05 13\",\"imageUrl\":\"https://kk-bucket-01.oss-cn-hangzhou.aliyuncs.com/images/1753508744851_bjb054e2d.png?Expires=1753512344&OSSAccessKeyId=LTAI5tBXifU1pzn7vavUQYTE&Signature=g6bT%2BPg3nfFg529yA0FoIo6IPRA%3D\",\"sortOrder\":1},{\"title\":\"重要会议\",\"imageUrl\":\"https://kk-bucket-01.oss-cn-hangzhou.aliyuncs.com/images/1753504087336_tlgir37jk.jpg?Expires=1753507687&OSSAccessKeyId=LTAI5tBXifU1pzn7vavUQYTE&Signature=e%2BJBl3gqDcx1PxMQqlTwUeyfKA8%3D\",\"sortOrder\":2},{\"title\":\"首页\",\"imageUrl\":\"https://kk-bucket-01.oss-cn-hangzhou.aliyuncs.com/images/1753503440770_u4c6ct5vy.jpg?Expires=1753507040&OSSAccessKeyId=LTAI5tBXifU1pzn7vavUQYTE&Signature=VHxRDKL3rxOPqsjlhR7Hd4z5Tcc%3D\",\"sortOrder\":3},{\"title\":\"截屏2025-02-05 13.28.29\",\"imageUrl\":\"https://kk-bucket-01.oss-cn-hangzhou.aliyuncs.com/images/1753509215934_8wkp54qnc.png?Expires=1753512816&OSSAccessKeyId=LTAI5tBXifU1pzn7vavUQYTE&Signature=0fYvmDhnsks3meCNAVdoRjVKSOk%3D\",\"sortOrder\":4}]}", "configType": "json", "category": "homepage", "description": "首页轮播图配置", "createdAt": "2025-07-26T12:18:06", "updatedAt": "2025-07-26T13:53:39"}, {"configKey": "import.statement.config", "configValue": "{\"statements\":[{\"title\":\"0990\",\"ossUrl\":\"\",\"url\":\"\",\"sortOrder\":1,\"updateTime\":\"2025-07-26T22:49:43\"},{\"title\":\"98239324792374\",\"ossUrl\":\"\",\"url\":\"\",\"sortOrder\":2,\"updateTime\":\"2025-07-26T22:49:53\"}],\"config\":{\"max_items\":8}}", "configType": "json", "category": "homepage", "description": "重点说明配置", "createdAt": "2025-07-26T22:29:12", "updatedAt": "2025-07-26T22:49:54"}, {"configKey": "homepage.category1.config", "configValue": "{\"categories\":[{\"id\":46,\"name\":\"热门商品\",\"slug\":\"热门商品\",\"description\":\"热门商品\",\"imageUrl\":\"\",\"sortOrder\":100,\"isActive\":true}]}", "configType": "json", "category": "homepage", "description": "首页分类1配置", "createdAt": null, "updatedAt": null}, {"configKey": "homepage.category2.config", "configValue": "{\"categories\":[{\"id\":47,\"name\":\"超值套餐\",\"slug\":\"超值套餐\",\"description\":\"超值套餐\",\"imageUrl\":\"\",\"sortOrder\":100,\"isActive\":true}]}", "configType": "json", "category": "homepage", "description": "首页分类2配置", "createdAt": null, "updatedAt": null}]}