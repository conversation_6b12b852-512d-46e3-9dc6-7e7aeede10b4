# 商品列表页面集成说明

## 概述

已成功实现分类页面点击二级分类跳转到商品列表页面，并调用 `/api/public/categories/{categoryId}/products` 接口获取和展示商品数据。

## 主要功能

### 1. 页面跳转
- **触发方式**: 在分类页面点击任意二级分类
- **跳转路径**: `/pages/products/products`
- **传递参数**: `categoryId` (分类ID) 和 `categoryName` (分类名称)

### 2. 商品数据展示
- **网格布局**: 2列商品网格展示
- **商品信息**: 商品图片、名称、品牌、描述、价格、库存
- **推荐标识**: 推荐商品显示红色"推荐"标签
- **价格显示**: 支持价格区间显示 (最低价-最高价)

### 3. 交互功能
- **排序功能**: 支持按最新、销量、价格排序
- **下拉刷新**: 支持下拉刷新商品列表
- **上拉加载**: 支持分页加载更多商品
- **商品点击**: 点击商品可跳转到商品详情页

### 4. 状态处理
- **加载状态**: 显示加载动画和提示
- **空状态**: 当分类下无商品时显示空状态提示
- **错误处理**: API失败时自动使用模拟数据降级

## 技术实现

### 文件结构
```
miniprogram/
├── types/product.ts              # 商品相关类型定义
├── services/productService.ts    # 商品API服务封装
└── pages/products/
    ├── products.json            # 页面配置
    ├── products.ts              # 页面逻辑
    ├── products.wxml            # 页面模板
    └── products.wxss            # 页面样式
```

### API接口
- **接口地址**: `/api/public/categories/{categoryId}/products`
- **请求方法**: GET
- **接口类型**: 公开接口（无需登录认证）
- **支持参数**: page, pageSize, sortBy, sortOrder

### 数据流程
1. 分类页面点击二级分类 → 传递 categoryId 和 categoryName
2. 商品页面接收参数 → 调用商品API接口
3. 获取商品数据 → 转换为页面展示格式
4. 渲染商品列表 → 支持分页和排序

## 使用说明

### 1. 从分类页面跳转
在分类页面点击任意二级分类，会自动跳转到对应的商品列表页面：

<augment_code_snippet path="miniprogram/pages/category/category.ts" mode="EXCERPT">
```typescript
onSubCategoryTap(e: any) {
  const subcategory = e.currentTarget.dataset.subcategory
  wx.navigateTo({
    url: `/pages/products/products?categoryId=${subcategory.id}&categoryName=${subcategory.name}`
  })
}
```
</augment_code_snippet>

### 2. 商品API调用
商品页面会自动调用对应的API接口获取商品数据：

<augment_code_snippet path="miniprogram/services/productService.ts" mode="EXCERPT">
```typescript
async getProductsByCategoryId(categoryId: number, params?: ProductQueryParams): Promise<ProductDTO[]> {
  const url = `/api/public/categories/${categoryId}/products`
  const response: ProductListResponse = await RequestManager.get(url)
  return response.data
}
```
</augment_code_snippet>

### 3. 商品数据展示
页面会将API返回的商品数据转换为适合展示的格式：

<augment_code_snippet path="miniprogram/pages/products/products.ts" mode="EXCERPT">
```typescript
transformProductData(productList: ProductDTO[]): ProductItem[] {
  return productList.map(product => ({
    id: product.id,
    name: product.name,
    brandName: product.brandName,
    mainImageUrl: product.mainImageUrl || this.getDefaultProductImage(product.name),
    minPrice: product.minPrice,
    maxPrice: product.maxPrice
  }))
}
```
</augment_code_snippet>

## 后端接口要求

后端需要实现 `/api/public/categories/{categoryId}/products` 接口，返回格式如下：

```json
{
  "code": "200",
  "message": "success",
  "data": [
    {
      "id": 1000,
      "name": "雷朋经典款太阳镜",
      "brandId": 1,
      "brandName": "雷朋",
      "shortDescription": "经典款太阳镜，时尚百搭",
      "mainImageUrl": "https://example.com/product.jpg",
      "isActive": true,
      "isFeatured": false,
      "minPrice": "299.00",
      "maxPrice": "599.00",
      "totalStock": 100
    }
  ],
  "success": true,
  "timestamp": 1640995200000
}
```

## 支持的查询参数

- **page**: 页码 (默认: 1)
- **pageSize**: 每页数量 (默认: 20)
- **sortBy**: 排序字段 ('price' | 'sales' | 'created')
- **sortOrder**: 排序顺序 ('asc' | 'desc')

## 调试功能

### 1. 控制台日志
页面会输出详细的调试信息：
- API请求URL和参数
- 返回的商品数据
- 数据转换过程

### 2. 模拟数据
当API调用失败时，会自动使用模拟数据确保页面正常显示。

### 3. 错误提示
- API成功: 显示"刷新成功"
- API失败: 显示"使用本地数据"

## 注意事项

1. **页面配置**: 已在 `app.json` 中添加商品页面路径
2. **图片资源**: 确保默认商品图片存在
3. **网络权限**: 确保小程序配置中允许访问后端API域名
4. **分页处理**: 支持无限滚动加载更多商品
5. **性能优化**: 使用图片懒加载和数据缓存

## 扩展功能

### 1. 添加搜索功能
可以在页面顶部添加搜索框，调用商品搜索API。

### 2. 添加筛选功能
可以添加品牌、价格区间等筛选条件。

### 3. 添加收藏功能
可以为每个商品添加收藏按钮。

### 4. 优化图片加载
可以添加图片懒加载和占位图功能。
