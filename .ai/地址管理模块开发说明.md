# 地址管理模块开发说明

## 🎯 功能概述

完整的地址管理模块，支持新增、删除、修改、设置默认地址等功能，所有数据存储在服务端。

## ✅ 已实现功能

### 1. 地址列表管理
- ✅ 显示所有收货地址
- ✅ 空状态提示和引导
- ✅ 默认地址标识
- ✅ 下拉刷新功能

### 2. 地址操作功能
- ✅ 新增地址
- ✅ 编辑地址
- ✅ 删除地址（带确认弹窗）
- ✅ 设置默认地址

### 3. 地址表单功能
- ✅ 收货人姓名输入
- ✅ 手机号输入和验证
- ✅ 省市区选择器
- ✅ 详细地址输入
- ✅ 默认地址开关

### 4. 数据验证
- ✅ 必填字段验证
- ✅ 手机号格式验证
- ✅ 地区选择验证
- ✅ 详细地址验证

### 5. 服务端存储
- ✅ 数据持久化存储到服务端
- ✅ 服务端自动生成唯一ID
- ✅ 默认地址逻辑处理
- ✅ 支持异步API调用
- ✅ 完整的错误处理机制

## 📁 文件结构

```
miniprogram/
├── types/address.ts                    # 地址相关类型定义
├── services/addressService.ts          # 地址管理服务
├── pages/addresses/                    # 地址列表页面
│   ├── addresses.json
│   ├── addresses.ts
│   ├── addresses.wxml
│   └── addresses.wxss
└── pages/address-edit/                 # 地址编辑页面
    ├── address-edit.json
    ├── address-edit.ts
    ├── address-edit.wxml
    └── address-edit.wxss
```

## 🔧 核心功能实现

### 1. 地址数据结构

```typescript
interface Address {
  id: number             // 地址ID (服务端返回的是数字类型)
  userId: number         // 用户ID
  recipientName: string  // 收件人姓名
  phoneNumber: string    // 收件人电话
  regionProvince: string // 省份
  regionCity: string     // 城市
  regionDistrict: string // 区县
  streetAddress: string  // 详细街道地址
  postalCode?: string    // 邮政编码
  isDefault: boolean     // 是否为默认地址
  createdAt: string      // 创建时间
  updatedAt: string      // 更新时间
}
```

### 2. 本地存储服务

```typescript
class AddressService {
  // 获取所有地址
  getAllAddresses(): Address[]
  
  // 新增地址
  addAddress(formData: AddressFormData): AddressOperationResult
  
  // 更新地址
  updateAddress(id: string, formData: AddressFormData): AddressOperationResult
  
  // 删除地址
  deleteAddress(id: string): AddressOperationResult
  
  // 设置默认地址
  setDefaultAddress(id: string): AddressOperationResult
  
  // 获取默认地址
  getDefaultAddress(): Address | null
}
```

### 3. 页面导航流程

```
我的页面 → 收货地址 → 地址列表页面
                    ├── 新增地址 → 地址编辑页面
                    ├── 编辑地址 → 地址编辑页面
                    ├── 删除地址 → 确认弹窗
                    └── 设为默认 → 即时更新
```

## 🎨 UI 设计特点

### 1. 地址列表页面
- **卡片式布局**: 每个地址独立卡片显示
- **信息层次**: 姓名、手机号、地址分层显示
- **默认标识**: 红色"默认"标签突出显示
- **操作按钮**: 编辑、设为默认、删除按钮
- **空状态**: 友好的空状态提示和引导

### 2. 地址编辑页面
- **分组表单**: 收货人信息、收货地址分组
- **地区选择**: 使用微信原生地区选择器
- **实时验证**: 表单提交时进行数据验证
- **默认开关**: 简洁的开关控件

### 3. 交互体验
- **加载状态**: 数据加载时显示加载动画
- **操作反馈**: 所有操作都有Toast提示
- **确认弹窗**: 删除操作需要用户确认
- **自动返回**: 保存成功后自动返回列表

## 🔄 数据流程

### 1. 新增地址流程
```
用户填写表单 → 验证数据 → 处理默认地址逻辑 → 生成ID → 保存到本地 → 返回列表
```

### 2. 编辑地址流程
```
加载现有数据 → 用户修改表单 → 验证数据 → 更新本地数据 → 返回列表
```

### 3. 删除地址流程
```
用户点击删除 → 显示确认弹窗 → 确认删除 → 处理默认地址逻辑 → 更新列表
```

### 4. 设置默认地址流程
```
用户点击设为默认 → 取消其他默认状态 → 设置当前为默认 → 更新列表显示
```

## 📱 使用方法

### 1. 从我的页面进入
```typescript
// 在 profile 页面中
goToAddresses() {
  wx.navigateTo({
    url: '/pages/addresses/addresses'
  })
}
```

### 2. 新增地址
```typescript
// 在地址列表页面中
addAddress() {
  wx.navigateTo({
    url: '/pages/address-edit/address-edit'
  })
}
```

### 3. 编辑地址
```typescript
// 在地址列表页面中
editAddress(e: any) {
  const addressId = e.currentTarget.dataset.id
  wx.navigateTo({
    url: `/pages/address-edit/address-edit?id=${addressId}`
  })
}
```

## 🧪 测试功能

### 1. 基础功能测试
- ✅ 新增地址功能
- ✅ 编辑地址功能
- ✅ 删除地址功能
- ✅ 设置默认地址功能

### 2. 数据验证测试
- ✅ 必填字段验证
- ✅ 手机号格式验证
- ✅ 地区选择验证

### 3. 边界情况测试
- ✅ 空地址列表显示
- ✅ 删除默认地址后的处理
- ✅ 多个地址的默认状态管理

### 4. 用户体验测试
- ✅ 页面加载和跳转
- ✅ 表单输入和提交
- ✅ 错误提示和成功反馈

## 🔮 扩展功能

### 1. 地址验证
- 可以集成地址验证API
- 自动补全详细地址
- 地址格式标准化

### 2. 地图选择
- 集成地图选择地址
- 获取精确坐标
- 地址逆解析

### 3. 常用地址
- 添加地址标签（家、公司等）
- 快速选择常用地址
- 地址使用频率统计

### 4. 导入导出
- 从通讯录导入地址
- 导出地址数据
- 地址数据备份

## 📊 API接口说明

### 1. 获取地址列表
```typescript
GET /api/user/addresses
Headers: X-Session-Id: {sessionId}
```

### 2. 创建地址
```typescript
POST /api/user/addresses
Headers: X-Session-Id: {sessionId}
Body: CreateAddressRequest
```

### 3. 更新地址
```typescript
PUT /api/user/addresses/{id}
Headers: X-Session-Id: {sessionId}
Body: UpdateAddressRequest
```

### 4. 删除地址
```typescript
DELETE /api/user/addresses/{id}
Headers: X-Session-Id: {sessionId}
```

### 5. 设置默认地址
```typescript
POST /api/user/addresses/{id}/set-default
Headers: X-Session-Id: {sessionId}
```

### 6. 获取默认地址
```typescript
GET /api/user/addresses/default
Headers: X-Session-Id: {sessionId}
```

## 📊 数据格式说明

### 服务端返回格式
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {
    "id": 123,
    "userId": 100001,
    "recipientName": "张三",
    "phoneNumber": "13800138000",
    "regionProvince": "广东省",
    "regionCity": "深圳市",
    "regionDistrict": "南山区",
    "streetAddress": "科技园南区深南大道10000号",
    "postalCode": "518000",
    "isDefault": true,
    "createdAt": "2023-12-31T16:00:00.000Z",
    "updatedAt": "2023-12-31T16:00:00.000Z"
  },
  "timestamp": 1640995200000,
  "success": true
}
```

### 数据管理特性
- 服务端自动生成唯一ID
- 时间戳记录创建和更新时间
- 默认地址唯一性保证
- 数据持久化存储到服务端
- 完整的异步API调用支持
- 统一的错误处理机制

现在地址管理模块已经完全升级为服务端存储，支持所有基础功能和高级特性！🎉
