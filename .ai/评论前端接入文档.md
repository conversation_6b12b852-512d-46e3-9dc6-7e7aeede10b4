# ProductReviewController 前端接入文档

## 接口概述

ProductReviewController 提供了商品评价相关的查询接口，包括获取商品评价列表和评价统计信息。

---

## 1. 获取商品评价列表

### 接口信息
- **接口名称**: 获取商品评价列表
- **请求方法**: GET
- **请求路径**: `/api/products/{productId}/reviews`
- **接口描述**: 分页获取指定商品的评价列表

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| productId | Long | 是 | 商品ID |

#### 查询参数
| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| page | Integer | 否 | 1 | 页码，从1开始 |
| size | Integer | 否 | 20 | 每页大小 |
| sortBy | String | 否 | created_at | 排序方式 |

### 请求示例

```javascript
// 获取商品ID为123的评价列表，第1页，每页10条
GET /api/products/123/reviews?page=1&size=10&sortBy=created_at

// 使用 axios
const response = await axios.get('/api/products/123/reviews', {
  params: {
    page: 1,
    size: 10,
    sortBy: 'created_at'
  }
});
```

### 响应数据结构

```typescript
interface ApiResponse {
  code: number;           // 响应状态码
  message: string;        // 响应消息
  data: PageResult<ReviewDTO>;  // 分页数据
  timestamp: string;      // 响应时间戳
}

interface PageResult<T> {
  records: T[];           // 数据列表
  total: number;          // 总记录数
  pageNum: number;        // 当前页码
  pageSize: number;       // 每页大小
  totalPages: number;     // 总页数
  hasNext: boolean;       // 是否有下一页
  hasPrevious: boolean;   // 是否有上一页
}

interface ReviewDTO {
  id: number;                    // 评价ID
  productId: number;             // 商品ID
  productName: string;           // 商品名称
  productMainImage: string;      // 商品主图
  skuId: number;                 // SKU ID
  skuName: string;               // SKU名称
  userId: number;                // 评价用户ID
  userName: string;              // 用户名称
  userAvatar: string;            // 用户头像
  isAnonymous: boolean;          // 是否匿名评价
  rating: number;                // 评分（1-5星）
  title: string;                 // 评价标题
  content: string;               // 评价内容
  replyCount: number;            // 回复数量
  likeCount: number;             // 点赞数量
  isLiked: boolean;              // 当前用户是否已点赞
  isVerifiedPurchase: boolean;   // 是否验证购买
  adminReply: string;            // 商家回复内容
  adminReplyTime: string;        // 商家回复时间
  images: Image[];               // 评价图片列表
  replies: ReplyDTO[];           // 回复列表
  createdAt: string;             // 创建时间
  updatedAt: string;             // 更新时间
}

interface Image {
  id: number;           // 图片ID
  imageUrl: string;     // 图片URL
  imageName: string;    // 图片名称
  description: string;  // 图片描述
  category: string;     // 图片分类
  businessId: number;   // 关联的业务ID
  businessType: string; // 业务类型
  tags: string;         // 图片标签
  fileSize: number;     // 文件大小（字节）
  width: number;        // 图片宽度（像素）
  height: number;       // 图片高度（像素）
  fileType: string;     // 文件类型
  sortOrder: number;    // 排序序号
  uploadedBy: number;   // 上传用户ID
  status: number;       // 状态：1-正常，0-删除
  createdAt: string;    // 创建时间
  updatedAt: string;    // 更新时间
}

interface ReplyDTO {
  id: number;              // 回复ID
  reviewId: number;        // 评价ID
  parentId: number;        // 父回复ID
  userId: number;          // 回复用户ID
  userName: string;        // 回复用户名称
  userAvatar: string;      // 回复用户头像
  replyToUserId: number;   // 被回复用户ID
  replyToUserName: string; // 被回复用户名称
  content: string;         // 回复内容
  isAdminReply: boolean;   // 是否为管理员回复
  likeCount: number;       // 点赞数量
  createdAt: string;       // 创建时间
  updatedAt: string;       // 更新时间
}
```

### 响应示例

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "productId": 123,
        "productName": "时尚眼镜框",
        "productMainImage": "https://example.com/product.jpg",
        "skuId": 456,
        "skuName": "黑色-大号",
        "userId": 789,
        "userName": "张三",
        "userAvatar": "https://example.com/avatar.jpg",
        "isAnonymous": false,
        "rating": 5,
        "title": "非常满意",
        "content": "质量很好，戴着很舒服",
        "replyCount": 2,
        "likeCount": 10,
        "isLiked": false,
        "isVerifiedPurchase": true,
        "adminReply": "感谢您的好评！",
        "adminReplyTime": "2024-01-15T10:30:00",
        "images": [
          {
            "id": 1,
            "imageUrl": "https://example.com/review1.jpg",
            "imageName": "评价图片1",
            "description": "实拍图",
            "category": "review_image",
            "businessId": 1,
            "businessType": "review",
            "fileSize": 102400,
            "width": 800,
            "height": 600,
            "fileType": "jpg",
            "sortOrder": 1,
            "status": 1,
            "createdAt": "2024-01-15T09:00:00",
            "updatedAt": "2024-01-15T09:00:00"
          }
        ],
        "replies": [
          {
            "id": 1,
            "reviewId": 1,
            "parentId": null,
            "userId": 999,
            "userName": "客服小王",
            "userAvatar": "https://example.com/service.jpg",
            "replyToUserId": 789,
            "replyToUserName": "张三",
            "content": "感谢您的支持！",
            "isAdminReply": true,
            "likeCount": 5,
            "createdAt": "2024-01-15T10:00:00",
            "updatedAt": "2024-01-15T10:00:00"
          }
        ],
        "createdAt": "2024-01-15T09:00:00",
        "updatedAt": "2024-01-15T09:00:00"
      }
    ],
    "total": 50,
    "pageNum": 1,
    "pageSize": 10,
    "totalPages": 5,
    "hasNext": true,
    "hasPrevious": false
  },
  "timestamp": "2024-01-15T12:00:00"
}
```

---

## 2. 获取商品评价统计

### 接口信息
- **接口名称**: 获取商品评价统计
- **请求方法**: GET
- **请求路径**: `/api/products/{productId}/reviews/stats`
- **接口描述**: 获取指定商品的评价统计信息

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| productId | Long | 是 | 商品ID |

### 请求示例

```javascript
// 获取商品ID为123的评价统计
GET /api/products/123/reviews/stats

// 使用 axios
const response = await axios.get('/api/products/123/reviews/stats');
```

### 响应数据结构

```typescript
interface ApiResponse {
  code: number;              // 响应状态码
  message: string;           // 响应消息
  data: ReviewStatsDTO;      // 评价统计数据
  timestamp: string;         // 响应时间戳
}

interface ReviewStatsDTO {
  productId: number;              // 商品ID
  totalReviews: number;           // 总评价数
  averageRating: number;          // 平均评分
  rating1Count: number;           // 1星评价数
  rating2Count: number;           // 2星评价数
  rating3Count: number;           // 3星评价数
  rating4Count: number;           // 4星评价数
  rating5Count: number;           // 5星评价数
  withImagesCount: number;        // 带图评价数
  verifiedPurchaseCount: number;  // 验证购买评价数
  positiveRate: number;           // 好评率（4-5星占比）
}
```

### 响应示例

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "productId": 123,
    "totalReviews": 150,
    "averageRating": 4.2,
    "rating1Count": 5,
    "rating2Count": 8,
    "rating3Count": 22,
    "rating4Count": 45,
    "rating5Count": 70,
    "withImagesCount": 80,
    "verifiedPurchaseCount": 120,
    "positiveRate": 0.77
  },
  "timestamp": "2024-01-15T12:00:00"
}
```

---

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 404 | 商品不存在 | 确认商品ID是否正确 |
| 500 | 服务器内部错误 | 联系技术支持 |

---

## 前端使用示例

### Vue.js 示例

```vue
<template>
  <div class="product-reviews">
    <!-- 评价统计 -->
    <div class="review-stats" v-if="stats">
      <h3>评价统计</h3>
      <p>总评价数: {{ stats.totalReviews }}</p>
      <p>平均评分: {{ stats.averageRating }}</p>
      <p>好评率: {{ (stats.positiveRate * 100).toFixed(1) }}%</p>
    </div>

    <!-- 评价列表 -->
    <div class="review-list">
      <h3>用户评价</h3>
      <div v-for="review in reviews" :key="review.id" class="review-item">
        <div class="user-info">
          <img :src="review.userAvatar" :alt="review.userName" />
          <span>{{ review.isAnonymous ? '匿名用户' : review.userName }}</span>
          <div class="rating">{{ '★'.repeat(review.rating) }}</div>
        </div>
        <div class="review-content">
          <h4>{{ review.title }}</h4>
          <p>{{ review.content }}</p>
          <div class="review-images" v-if="review.images.length">
            <img v-for="img in review.images" :key="img.id" :src="img.imageUrl" />
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="pageInfo">
      <button @click="prevPage" :disabled="!pageInfo.hasPrevious">上一页</button>
      <span>{{ pageInfo.pageNum }} / {{ pageInfo.totalPages }}</span>
      <button @click="nextPage" :disabled="!pageInfo.hasNext">下一页</button>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'ProductReviews',
  props: {
    productId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      reviews: [],
      stats: null,
      pageInfo: null,
      currentPage: 1,
      pageSize: 10
    };
  },
  async mounted() {
    await this.loadStats();
    await this.loadReviews();
  },
  methods: {
    async loadStats() {
      try {
        const response = await axios.get(`/api/products/${this.productId}/reviews/stats`);
        this.stats = response.data.data;
      } catch (error) {
        console.error('加载评价统计失败:', error);
      }
    },
    async loadReviews() {
      try {
        const response = await axios.get(`/api/products/${this.productId}/reviews`, {
          params: {
            page: this.currentPage,
            size: this.pageSize,
            sortBy: 'created_at'
          }
        });
        const data = response.data.data;
        this.reviews = data.records;
        this.pageInfo = {
          pageNum: data.pageNum,
          totalPages: data.totalPages,
          hasNext: data.hasNext,
          hasPrevious: data.hasPrevious
        };
      } catch (error) {
        console.error('加载评价列表失败:', error);
      }
    },
    async prevPage() {
      if (this.pageInfo.hasPrevious) {
        this.currentPage--;
        await this.loadReviews();
      }
    },
    async nextPage() {
      if (this.pageInfo.hasNext) {
        this.currentPage++;
        await this.loadReviews();
      }
    }
  }
};
</script>
```

### React 示例

```jsx
import React, { useState, useEffect } from 'react';
import axios from 'axios';

const ProductReviews = ({ productId }) => {
  const [reviews, setReviews] = useState([]);
  const [stats, setStats] = useState(null);
  const [pageInfo, setPageInfo] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  useEffect(() => {
    loadStats();
    loadReviews();
  }, [productId, currentPage]);

  const loadStats = async () => {
    try {
      const response = await axios.get(`/api/products/${productId}/reviews/stats`);
      setStats(response.data.data);
    } catch (error) {
      console.error('加载评价统计失败:', error);
    }
  };

  const loadReviews = async () => {
    try {
      const response = await axios.get(`/api/products/${productId}/reviews`, {
        params: {
          page: currentPage,
          size: pageSize,
          sortBy: 'created_at'
        }
      });
      const data = response.data.data;
      setReviews(data.records);
      setPageInfo({
        pageNum: data.pageNum,
        totalPages: data.totalPages,
        hasNext: data.hasNext,
        hasPrevious: data.hasPrevious
      });
    } catch (error) {
      console.error('加载评价列表失败:', error);
    }
  };

  const prevPage = () => {
    if (pageInfo?.hasPrevious) {
      setCurrentPage(currentPage - 1);
    }
  };

  const nextPage = () => {
    if (pageInfo?.hasNext) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <div className="product-reviews">
      {/* 评价统计 */}
      {stats && (
        <div className="review-stats">
          <h3>评价统计</h3>
          <p>总评价数: {stats.totalReviews}</p>
          <p>平均评分: {stats.averageRating}</p>
          <p>好评率: {(stats.positiveRate * 100).toFixed(1)}%</p>
        </div>
      )}

      {/* 评价列表 */}
      <div className="review-list">
        <h3>用户评价</h3>
        {reviews.map(review => (
          <div key={review.id} className="review-item">
            <div className="user-info">
              <img src={review.userAvatar} alt={review.userName} />
              <span>{review.isAnonymous ? '匿名用户' : review.userName}</span>
              <div className="rating">{'★'.repeat(review.rating)}</div>
            </div>
            <div className="review-content">
              <h4>{review.title}</h4>
              <p>{review.content}</p>
              {review.images.length > 0 && (
                <div className="review-images">
                  {review.images.map(img => (
                    <img key={img.id} src={img.imageUrl} alt="评价图片" />
                  ))}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* 分页 */}
      {pageInfo && (
        <div className="pagination">
          <button onClick={prevPage} disabled={!pageInfo.hasPrevious}>
            上一页
          </button>
          <span>{pageInfo.pageNum} / {pageInfo.totalPages}</span>
          <button onClick={nextPage} disabled={!pageInfo.hasNext}>
            下一页
          </button>
        </div>
      )}
    </div>
  );
};

export default ProductReviews;
```

---

## 注意事项

1. **分页参数**: page 参数从 1 开始，不是从 0 开始
2. **图片处理**: 评价图片支持多种尺寸，可根据需要选择合适的尺寸
3. **匿名评价**: 当 `isAnonymous` 为 true 时，应显示"匿名用户"而不是真实用户名
4. **验证购买**: `isVerifiedPurchase` 字段可用于标识"已购买"标签
5. **错误处理**: 建议在前端添加适当的错误处理和加载状态
6. **性能优化**: 对于图片较多的评价，建议实现懒加载
7. **数据缓存**: 可以考虑对评价统计数据进行适当缓存，减少重复请求
8. **响应式设计**: 在移动端需要适配不同屏幕尺寸的显示效果
9. **图片预览**: 建议为评价图片添加点击预览功能
10. **排序选项**: sortBy 参数支持多种排序方式，可根据业务需求扩展

---

## 更新日志

- **2024-01-15**: 初始版本，包含获取评价列表和统计信息两个接口
- **文档版本**: v1.0.0
