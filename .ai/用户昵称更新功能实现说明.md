# 用户昵称更新功能实现说明

## 🎯 实现目标

1. 实现用户更新昵称后调用服务端更新用户信息接口，根据接口文档 `/api/auth/profile` 进行昵称更新
2. 优化登录成功后的昵称显示逻辑，如果接口返回昵称则显示真实昵称，否则显示"完善信息"

## ✅ 已实现的功能

### 1. AuthManager 更新 (`utils/auth.ts`)

#### 新增异步昵称更新方法
```typescript
/**
 * 更新用户昵称（调用服务端接口）
 * @param nickName 用户昵称
 * @returns 更新结果
 */
async updateUserNickname(nickName: string): Promise<{ success: boolean; message: string }>
```

#### 新增通用用户信息更新方法
```typescript
/**
 * 更新用户信息（调用服务端接口）
 * @param userInfo 要更新的用户信息
 * @returns 更新结果
 */
async updateUserProfile(userInfo: {
  nickname?: string;
  avatarUrl?: string;
  gender?: number;
  country?: string;
  province?: string;
  city?: string;
}): Promise<{ success: boolean; message: string; data?: any }>
```

#### 新增服务端请求方法
```typescript
/**
 * 发送更新用户信息请求到后端
 */
private async sendUpdateProfileRequest(userInfo): Promise<any>
```

### 2. 登录逻辑优化 (`utils/auth.ts`)

#### 优化登录成功后的用户信息处理
```typescript
// 优先使用服务端返回的昵称，如果没有则显示"完善信息"
const nickname = userInfoFromServer.nickname && userInfoFromServer.nickname.trim()
  ? userInfoFromServer.nickname
  : '完善信息'

const user = {
  id: userInfoFromServer.id,
  nickName: nickname,
  avatarUrl: userInfoFromServer.avatarUrl || '/images/default-avatar.png',
  phone: userInfoFromServer.phoneNumber || undefined,
  phoneNumber: userInfoFromServer.phoneNumber || undefined,
  hasPhoneNumber: userInfoFromServer.hasPhoneNumber || false,
  gender: userInfoFromServer.gender || undefined,
  // ...其他字段
  hasRealInfo: !!(nickname && nickname !== '完善信息') // 如果有真实昵称则标记为已完善信息
}
```

### 3. Profile 页面更新 (`pages/profile/profile.ts`)

#### 修改昵称保存方法为异步
```typescript
/**
 * 保存昵称的通用方法
 */
async saveNickname(nickName: string)
```

## 🔧 技术实现细节

### 接口调用
- **接口地址**: `PUT /api/auth/profile`
- **请求头**: 
  - `Content-Type: application/json`
  - `X-Session-Id: {sessionId}`
  - `appId: {appId}`
- **请求体**: `{ nickname: "新昵称" }`

### 错误处理
- 登录状态检查
- 网络请求异常处理
- 用户友好的错误提示
- 失败时恢复输入框状态

### 用户体验优化
- 显示加载提示 "更新中..."
- 成功/失败的Toast提示
- 自动保存（失去焦点或回车键触发）
- 本地状态与服务端同步

## 📋 使用流程

### 登录时的昵称显示
1. **用户登录**: 调用微信登录接口
2. **接口返回**: 服务端返回用户信息，包含nickname字段
3. **昵称处理**:
   - 如果nickname存在且不为空，显示真实昵称
   - 如果nickname为空或不存在，显示"完善信息"
4. **页面显示**: 在"我的"界面显示处理后的昵称

### 昵称更新流程
1. **用户输入昵称**: 在昵称输入框中输入新昵称
2. **触发保存**: 失去焦点或按回车键自动触发保存
3. **显示加载**: 显示"更新中..."加载提示
4. **调用接口**: 发送PUT请求到`/api/auth/profile`
5. **处理响应**:
   - 成功：更新页面显示，显示成功提示
   - 失败：显示错误信息，恢复输入框状态
6. **同步本地**: 成功后更新本地存储的用户信息

## 🔍 接口文档参考

根据 `.ai/restful-api/modules/user.json` 中的接口定义：

```json
{
  "url": "/api/auth/profile",
  "method": "PUT",
  "summary": "更新用户信息",
  "description": "更新用户个人信息，包括昵称、头像等",
  "parameters": [
    {
      "name": "X-Session-Id",
      "in": "header",
      "required": true,
      "schema": { "type": "string" }
    }
  ],
  "requestBody": {
    "content": {
      "application/json": {
        "schema": {
          "$ref": "#/components/schemas/UpdateUserInfoRequest"
        }
      }
    }
  }
}
```

### 请求数据结构
```typescript
interface UpdateUserInfoRequest {
  nickname?: string;        // 用户昵称，最大长度50
  avatarUrl?: string;       // 用户头像URL，最大长度500
  gender?: number;          // 性别：0-未知, 1-男, 2-女
  country?: string;         // 所在国家，最大长度50
  province?: string;        // 所在省份，最大长度50
  city?: string;           // 所在城市，最大长度50
}
```

### 响应数据结构
```typescript
interface UpdateUserInfoResponse {
  userId: number;           // 用户ID
  nickname: string;         // 用户昵称
  avatarUrl: string;        // 用户头像URL
  gender: number;           // 性别
  country: string;          // 所在国家
  province: string;         // 所在省份
  city: string;            // 所在城市
  updatedAt: string;        // 更新时间
  success: boolean;         // 操作是否成功
  message: string;          // 操作结果消息
}
```

## 🚀 测试建议

1. **正常流程测试**:
   - 登录后输入新昵称
   - 验证服务端接口调用
   - 确认页面显示更新

2. **异常情况测试**:
   - 未登录状态下尝试更新
   - 网络异常情况
   - 服务端返回错误

3. **用户体验测试**:
   - 加载提示显示
   - 成功/失败提示
   - 输入框状态恢复

## 📝 注意事项

- 昵称最大长度限制为50个字符
- 需要用户已登录才能更新
- 更新成功后会同步本地存储
- 支持其他用户信息字段的扩展更新
- 登录时会优先显示服务端返回的真实昵称
- 如果服务端没有昵称信息，会显示"完善信息"提示用户输入

## 🔄 登录接口返回数据示例

```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {
    "userId": 3,
    "sessionId": "78f4b1f197df4269a247dfe33544ae8c",
    "sessionExpireTime": "2025-06-24T19:26:40.033169",
    "isNewUser": false,
    "userInfo": {
      "id": 3,
      "nickname": "有戏",           // 关键字段：用户昵称
      "avatarUrl": null,
      "gender": null,
      "phoneNumber": "13083990786",
      "hasPhoneNumber": true,
      "createdAt": "2025-06-17T19:26:40.032794"
    }
  },
  "timestamp": 1750159600033,
  "success": true
}
```

### 昵称处理逻辑
- 如果 `userInfo.nickname` 存在且不为空字符串，则显示该昵称
- 如果 `userInfo.nickname` 为 `null`、`undefined` 或空字符串，则显示"完善信息"
