# 小程序端退款接口文档

## 📋 概述

本文档详细说明小程序端用户发起退款申请的完整流程和相关接口，指导前端开发人员进行接入。

## 🔄 退款流程

### 仅退款流程
```
用户申请 → 系统审核 → 退款处理 → 完成
```

### 退货退款流程  
```
用户申请 → 系统审核 → 用户寄回 → 商家收货 → 退款处理 → 完成
```

## 📊 状态说明

| 状态代码 | 状态名称 | 说明 | 用户可操作 |
|---------|---------|------|-----------|
| `pending_review` | 待审核 | 退款申请已提交，等待审核 | 可取消申请 |
| `approved` | 已同意 | 审核通过，需要用户操作 | 填写退货物流(退货退款) |
| `rejected` | 已拒绝 | 审核未通过 | 无 |
| `user_shipping` | 用户寄回中 | 用户已寄出退货商品 | 无 |
| `merchant_received` | 商家已收货 | 商家确认收到退货 | 无 |
| `refunded` | 已退款 | 退款已到账 | 无 |
| `cancelled` | 已取消 | 用户取消申请 | 无 |

## 🔧 退款类型

| 类型代码 | 类型名称 | 说明 | 适用场景 |
|---------|---------|------|---------|
| `refund_only` | 仅退款 | 不需要退货，直接退款 | 未发货或无需退货 |
| `return_refund` | 退货退款 | 需要退货后退款 | 已发货需要退货 |

## 🎯 退款原因

| 原因代码 | 原因名称 | 说明 |
|---------|---------|------|
| `quality_issue` | 质量问题 | 商品存在质量缺陷或损坏 |
| `size_mismatch` | 尺寸不符 | 商品尺寸与描述不符或不合适 |
| `not_as_described` | 与描述不符 | 商品与页面描述或图片不符 |
| `wrong_item` | 发错商品 | 收到的商品与订购的不一致 |
| `change_mind` | 不想要了 | 个人原因不想要该商品 |
| `other` | 其他原因 | 其他未列出的原因 |

## 🌐 接口列表

### 1. 检查退款资格

**接口地址：** `GET /api/refund/check-eligibility/{orderId}`

**请求参数：**
- `orderId` (路径参数): 订单ID

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "eligible": true,
    "reason": "订单可以申请退款",
    "maxRefundAmount": 299.00,
    "suggestedRefundType": "return_refund",
    "orderInfo": {
      "orderId": 123,
      "orderNumber": "ORD20240101001",
      "orderStatus": "shipped",
      "totalAmount": 299.00,
      "items": [
        {
          "orderItemId": 456,
          "productName": "时尚眼镜框",
          "quantity": 1,
          "price": 299.00,
          "canRefund": true,
          "maxRefundQuantity": 1
        }
      ]
    }
  }
}
```

### 2. 获取退款原因列表

**接口地址：** `GET /api/refund/reasons`

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "reasons": [
      {
        "code": "quality_issue",
        "name": "质量问题",
        "description": "商品存在质量缺陷或损坏"
      },
      {
        "code": "size_mismatch", 
        "name": "尺寸不符",
        "description": "商品尺寸与描述不符或不合适"
      }
    ]
  }
}
```

### 3. 创建退款申请

**接口地址：** `POST /api/refund/create`

**请求参数：**
```json
{
  "orderId": 123,
  "refundType": "return_refund",
  "refundReason": "quality_issue",
  "refundDescription": "镜框有划痕，影响使用",
  "refundAmount": 299.00,
  "refundItems": [
    {
      "orderItemId": 456,
      "refundQuantity": 1,
      "refundAmount": 299.00
    }
  ],
  "evidenceImages": [
    "https://example.com/evidence1.jpg",
    "https://example.com/evidence2.jpg"
  ]
}
```

**字段说明：**
- `orderId`: 订单ID（必填）
- `refundType`: 退款类型，`refund_only` 或 `return_refund`（必填）
- `refundReason`: 退款原因代码（必填）
- `refundDescription`: 退款详细说明（可选，最大500字符）
- `refundAmount`: 申请退款金额（必填，0.01-999999.99）
- `refundItems`: 退款商品明细（必填）
- `evidenceImages`: 证据图片URL列表（可选，最多9张）

**响应示例：**
```json
{
  "code": 200,
  "message": "退款申请提交成功",
  "data": {
    "id": 789,
    "refundNumber": "RF20240101001",
    "status": "pending_review",
    "statusDesc": "待审核",
    "createdAt": "2024-01-01T10:00:00"
  }
}
```

### 4. 查询我的退款申请列表

**接口地址：** `GET /api/refund/list`

**请求参数：**
- `pageNum`: 页码（默认1）
- `pageSize`: 页大小（默认20）
- `status`: 状态筛选（可选）

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 789,
        "refundNumber": "RF20240101001",
        "orderNumber": "ORD20240101001",
        "refundType": "return_refund",
        "refundTypeDesc": "退货退款",
        "refundReasonDesc": "质量问题",
        "refundAmount": 299.00,
        "status": "pending_review",
        "statusDesc": "待审核",
        "createdAt": "2024-01-01T10:00:00",
        "updatedAt": "2024-01-01T10:00:00"
      }
    ],
    "total": 1,
    "size": 20,
    "current": 1,
    "pages": 1
  }
}
```

### 5. 查询退款申请详情

**接口地址：** `GET /api/refund/detail/{refundId}`

**请求参数：**
- `refundId` (路径参数): 退款申请ID

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 789,
    "refundNumber": "RF20240101001",
    "orderId": 123,
    "orderNumber": "ORD20240101001",
    "refundType": "return_refund",
    "refundTypeDesc": "退货退款",
    "refundReason": "quality_issue",
    "refundReasonDesc": "质量问题",
    "refundDescription": "镜框有划痕，影响使用",
    "refundAmount": 299.00,
    "refundItems": [
      {
        "orderItemId": 456,
        "productName": "时尚眼镜框",
        "refundQuantity": 1,
        "refundAmount": 299.00
      }
    ],
    "evidenceImages": [
      "https://example.com/evidence1.jpg"
    ],
    "status": "approved",
    "statusDesc": "已同意",
    "adminReviewNotes": "同意退款申请",
    "reviewedAt": "2024-01-01T11:00:00",
    "createdAt": "2024-01-01T10:00:00",
    "updatedAt": "2024-01-01T11:00:00",
    "availableActions": ["submit_shipping", "cancel"]
  }
}
```

### 6. 提交退货物流信息

**接口地址：** `POST /api/refund/shipping`

**请求参数：**
```json
{
  "refundRequestId": 789,
  "shippingCompany": "顺丰速运",
  "trackingNumber": "SF1234567890",
  "shippingNotes": "已按要求包装寄出"
}
```

**字段说明：**
- `refundRequestId`: 退款申请ID（必填）
- `shippingCompany`: 物流公司（必填，最大100字符）
- `trackingNumber`: 物流单号（必填，最大100字符）
- `shippingNotes`: 物流备注（可选，最大500字符）

**响应示例：**
```json
{
  "code": 200,
  "message": "退货物流信息提交成功",
  "data": null
}
```

### 7. 取消退款申请

**接口地址：** `POST /api/refund/cancel/{refundId}`

**请求参数：**
- `refundId` (路径参数): 退款申请ID

**响应示例：**
```json
{
  "code": 200,
  "message": "退款申请已取消",
  "data": null
}
```

### 8. 获取退货收货地址

**接口地址：** `GET /api/refund/return-address`

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "contactName": "客服中心",
    "contactPhone": "************",
    "address": "广东省深圳市南山区科技园南区",
    "zipCode": "518000",
    "notes": "请在包裹上注明退款单号"
  }
}
```

## 🚀 前端接入指南

### 1. 退款申请流程

```javascript
// 1. 检查退款资格
async function checkRefundEligibility(orderId) {
  const response = await api.get(`/api/refund/check-eligibility/${orderId}`);
  return response.data;
}

// 2. 获取退款原因
async function getRefundReasons() {
  const response = await api.get('/api/refund/reasons');
  return response.data.reasons;
}

// 3. 提交退款申请
async function createRefundRequest(refundData) {
  const response = await api.post('/api/refund/create', refundData);
  return response.data;
}
```

### 2. 状态处理

```javascript
// 根据状态显示不同操作按钮
function getAvailableActions(status) {
  switch(status) {
    case RefundStatus.PENDING_REVIEW:
      return [RefundAction.CANCEL]; // 可取消
    case RefundStatus.APPROVED:
      return [RefundAction.SUBMIT_SHIPPING, RefundAction.CANCEL]; // 可提交物流或取消
    case RefundStatus.USER_SHIPPING:
      return []; // 等待商家确认收货
    default:
      return [];
  }
}
```

### 3. 错误处理

```javascript
// 统一错误处理
function handleRefundError(error) {
  const errorMessages = {
    'REFUND_NOT_ELIGIBLE': '该订单不符合退款条件',
    'REFUND_AMOUNT_INVALID': '退款金额不正确',
    'REFUND_ALREADY_EXISTS': '该订单已有退款申请'
  };
  
  const message = errorMessages[error.code] || error.message;
  showToast(message);
}
```

## ⚠️ 注意事项

1. **权限验证**：所有接口都需要用户登录，会自动获取当前用户ID
2. **数据校验**：前端需要进行基础校验，后端会进行完整校验
3. **图片上传**：证据图片需要先上传到文件服务器，再传递URL
4. **状态轮询**：可以定期查询退款状态更新
5. **错误处理**：需要处理网络错误和业务错误
6. **用户体验**：提供清晰的状态说明和操作指引

## 📱 UI建议

1. **申请页面**：提供订单选择、原因选择、金额输入、图片上传
2. **列表页面**：显示退款状态、金额、时间，支持筛选
3. **详情页面**：完整信息展示，根据状态显示操作按钮
4. **物流页面**：物流公司选择、单号输入、备注填写
