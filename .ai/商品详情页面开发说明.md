# 商品详情页面开发说明

## 概述

已成功实现商品详情页面，支持从商品列表页面点击商品跳转到详情页面，并调用 `/api/public/categories/{id}/with-skus` 接口获取商品详情和SKU信息。

**重要说明**: 接口路径 `/api/public/categories/{id}/with-skus` 中的 `{id}` 参数是商品ID，不是分类ID。虽然路径包含 "categories"，但这是用来获取商品详情的公开接口。

## 主要功能

### 1. 页面跳转
- **触发方式**: 在商品列表页面点击任意商品
- **跳转路径**: `/pages/product-detail/product-detail`
- **传递参数**: `productId` (商品ID) 和 `productName` (商品名称)

### 2. 商品详情展示
- **标签页切换**: 商品、详情、评价三个标签页
- **商品图片**: 支持商品主图展示
- **价格信息**: 显示当前价格、原价、库存信息
- **商品信息**: 商品名称、品牌、描述等基本信息
- **SKU选择**: 支持规格选择和数量调整

### 3. 交互功能
- **SKU选择器**: 弹窗式规格选择，支持多种SKU规格
- **数量调整**: 支持增减数量，限制最大库存数量
- **加入购物车**: 选择规格后可加入购物车
- **立即购买**: 选择规格后可立即购买
- **客服咨询**: 跳转到客服页面
- **购物车查看**: 跳转到购物车页面

### 4. 状态处理
- **加载状态**: 显示加载动画和提示
- **错误处理**: API失败时自动使用模拟数据降级
- **SKU状态**: 支持缺货、禁用状态显示

## 技术实现

### 文件结构
```
miniprogram/
├── types/product.ts                    # 更新商品相关类型定义
├── services/productService.ts          # 更新商品API服务封装
└── pages/product-detail/
    ├── product-detail.json            # 页面配置
    ├── product-detail.ts              # 页面逻辑
    ├── product-detail.wxml            # 页面模板
    └── product-detail.wxss            # 页面样式
```

### API接口
- **接口地址**: `/api/public/categories/{id}/with-skus`
- **请求方法**: GET
- **接口类型**: 公开接口（无需登录认证）
- **返回数据**: 商品详情、SKU列表、统计信息

### 数据流程
1. 商品列表页面点击商品 → 传递 productId 和 productName
2. 商品详情页面接收参数 → 调用商品详情API接口
3. 获取商品数据 → 解析商品信息和SKU列表
4. 渲染商品详情 → 支持SKU选择和购买操作

## 使用说明

### 1. 从商品列表页面跳转
在商品列表页面点击任意商品，会自动跳转到对应的商品详情页面：

```typescript
onProductTap(e: any) {
  const product = e.currentTarget.dataset.product
  wx.navigateTo({
    url: `/pages/product-detail/product-detail?productId=${product.id}&productName=${product.name}`
  })
}
```

### 2. 商品详情API调用
商品详情页面会自动调用对应的API接口获取商品数据：

```typescript
async getProductWithSkus(productId: number): Promise<ProductWithSkusDTO> {
  const url = `/api/public/categories/${productId}/with-skus`
  const response: ProductWithSkusResponse = await RequestManager.get(url)
  return response.data
}
```

### 3. SKU选择功能
用户可以通过弹窗选择不同的商品规格：

```typescript
onSkuSelect(e: any) {
  const skuIndex = e.currentTarget.dataset.index
  const { productData } = this.data
  
  if (productData && productData.skus[skuIndex]) {
    this.setData({
      selectedSku: productData.skus[skuIndex],
      selectedSkuIndex: skuIndex
    })
  }
}
```

## 数据结构

### 1. 商品详情数据
```typescript
interface ProductWithSkusDTO {
  product: ProductDTO          // 商品基本信息
  skus: ProductSkuDTO[]       // SKU列表
  statistics: ProductStatistics // 统计信息
}
```

### 2. SKU数据
```typescript
interface ProductSkuDTO {
  id: number
  skuCode: string
  nameExtension: string       // SKU名称后缀
  price: number              // 销售价
  originalPrice: number      // 原价
  stockQuantity: number      // 库存数量
  attributes: { [key: string]: string } // 规格属性
  isActive: boolean          // 是否可用
}
```

### 3. 统计信息
```typescript
interface ProductStatistics {
  totalSkuCount: number      // SKU总数
  activeSkuCount: number     // 活跃SKU数量
  totalStockQuantity: number // 总库存数量
  minPrice: number          // 最低价格
  maxPrice: number          // 最高价格
}
```

## 页面特性

### 1. 响应式设计
- 适配不同屏幕尺寸
- 支持横竖屏切换
- 优化触摸交互体验

### 2. 用户体验
- 流畅的页面切换动画
- 直观的SKU选择界面
- 清晰的价格和库存信息
- 便捷的购买操作流程

### 3. 错误处理
- API调用失败时使用模拟数据
- 网络异常时显示友好提示
- SKU缺货时禁用选择按钮

## 调试功能

### 1. 控制台日志
页面会输出详细的调试信息：
- API请求URL和参数
- 返回的商品详情数据
- SKU选择和数量变化
- 用户操作事件

### 2. 模拟数据
当API调用失败时，会自动使用模拟数据确保页面正常显示，包含：
- 完整的商品信息
- 多个SKU规格选项
- 真实的价格和库存数据

### 3. 错误提示
- API成功: 正常显示商品数据
- API失败: 显示"使用本地数据"提示

## 注意事项

1. **页面配置**: 已在 `app.json` 中添加商品详情页面路径
2. **图片资源**: 确保默认商品图片存在
3. **网络权限**: 确保小程序配置中允许访问后端API域名
4. **SKU处理**: 支持多规格商品和单规格商品
5. **性能优化**: 使用数据缓存和懒加载
6. **布局修复**: 已修复标签页与导航栏之间的间距问题，移除了调试用的背景色

## 问题修复记录

### 2024-06-19: 修复标签页间距问题
- **问题**: 商品详情页面的标签页（商品、详情、评价）与导航栏之间有很大的黄色空白区域
- **原因**:
  1. 标签页设置了 `position: sticky; top: 0;` 导致定位异常
  2. 容器有调试用的黄色背景色 `background-color: yellow;`
- **解决方案**:
  1. 移除标签页的 sticky 定位，让其正常流动
  2. 清理调试用的背景色，恢复正常的灰色背景
- **修改文件**: `miniprogram/pages/product-detail/product-detail.wxss`

## 扩展功能

### 1. 图片轮播
可以添加商品图片轮播功能，支持多张商品图片展示。

### 2. 商品评价
可以集成商品评价系统，显示用户评价和评分。

### 3. 收藏功能
可以添加商品收藏功能，支持用户收藏喜欢的商品。

### 4. 分享功能
可以添加商品分享功能，支持分享到微信好友或朋友圈。

### 5. 相关推荐
可以在页面底部添加相关商品推荐功能。
