# 地址管理服务端迁移说明

## 📋 迁移概述

将地址管理模块从本地存储迁移到服务端存储，使用RESTful API进行数据管理。

## 🔄 主要变更

### 1. 数据结构调整

#### 原字段名 → 新字段名
- `id: string` → `id: number` (服务端返回数字ID)
- `name` → `recipientName` (收件人姓名)
- `phone` → `phoneNumber` (收件人电话)
- `province` → `regionProvince` (省份)
- `city` → `regionCity` (城市)
- `district` → `regionDistrict` (区县)
- `detail` → `streetAddress` (详细街道地址)
- 新增 `userId: number` (用户ID)
- 新增 `postalCode?: string` (邮政编码，可选)

### 2. API接口集成

#### 实现的接口
- `GET /api/user/addresses` - 获取地址列表
- `POST /api/user/addresses` - 创建地址
- `GET /api/user/addresses/{id}` - 获取地址详情
- `PUT /api/user/addresses/{id}` - 更新地址
- `DELETE /api/user/addresses/{id}` - 删除地址
- `POST /api/user/addresses/{id}/set-default` - 设置默认地址
- `GET /api/user/addresses/default` - 获取默认地址

#### 请求头配置
所有API请求自动携带：
- `X-Session-Id`: 用户会话ID
- `Content-Type`: application/json

### 3. 文件修改清单

#### 类型定义 (`types/address.ts`)
- ✅ 更新 `Address` 接口字段名
- ✅ 添加 `CreateAddressRequest` 类型
- ✅ 添加 `UpdateAddressRequest` 类型
- ✅ 添加 `ApiResult` 响应类型
- ✅ 更新 `AddressEditPageData` 中的 `addressId` 类型

#### 服务层 (`services/addressService.ts`)
- ✅ 移除本地存储相关代码
- ✅ 实现所有API调用方法
- ✅ 所有方法改为异步 (`async/await`)
- ✅ 更新数据验证逻辑
- ✅ 统一错误处理机制

#### 页面逻辑
**地址列表页面 (`pages/addresses/addresses.ts`)**
- ✅ `loadAddresses()` 改为异步方法
- ✅ `setDefaultAddress()` 改为异步方法
- ✅ `performDeleteAddress()` 改为异步方法
- ✅ 更新字段名引用

**地址编辑页面 (`pages/address-edit/address-edit.ts`)**
- ✅ 更新表单数据结构
- ✅ `loadAddressData()` 改为异步方法
- ✅ `saveAddress()` 改为异步方法
- ✅ 更新表单验证逻辑
- ✅ 添加加载状态处理

#### 页面模板
**地址列表模板 (`pages/addresses/addresses.wxml`)**
- ✅ 更新字段名绑定：`name` → `recipientName`
- ✅ 更新字段名绑定：`phone` → `phoneNumber`
- ✅ 更新地址显示格式

**地址编辑模板 (`pages/address-edit/address-edit.wxml`)**
- ✅ 更新表单字段绑定
- ✅ 添加邮政编码输入框
- ✅ 更新地区选择器绑定

#### 文档更新
- ✅ 更新开发说明文档
- ✅ 添加API接口说明
- ✅ 更新数据格式示例

## 🔧 技术要点

### 1. 异步处理
所有数据操作都改为异步，使用 `async/await` 语法：
```typescript
// 原来
const addresses = AddressService.getAllAddresses()

// 现在
const addresses = await AddressService.getAllAddresses()
```

### 2. 错误处理
统一的错误处理机制：
```typescript
try {
  const result = await AddressService.addAddress(formData)
  if (result.success) {
    // 成功处理
  } else {
    // 业务错误处理
  }
} catch (error) {
  // 网络或系统错误处理
}
```

### 3. 加载状态
添加了完整的加载状态管理：
- 数据加载时显示 loading
- 操作进行时显示相应提示
- 错误时显示错误信息

## ✅ 迁移完成

地址管理模块已成功从本地存储迁移到服务端存储，具备以下特性：

1. **完整的API集成** - 支持所有CRUD操作
2. **异步数据处理** - 所有操作都是异步的
3. **统一错误处理** - 网络错误和业务错误分别处理
4. **用户体验优化** - 加载状态、错误提示等
5. **数据结构标准化** - 与服务端API完全匹配
6. **向后兼容** - 保持原有功能不变

## 🚀 使用说明

迁移后的使用方式与之前完全一致，用户无感知：
- 地址列表正常显示
- 新增/编辑地址功能正常
- 删除地址功能正常
- 设置默认地址功能正常
- 所有数据现在存储在服务端

所有API调用都会自动携带用户认证信息，确保数据安全。
