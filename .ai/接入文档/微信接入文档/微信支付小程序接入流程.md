# 微信支付小程序接入流程

## 📋 **开发进度总结**

### ✅ **购物车模块 - 开发完成度：100%**

| 组件 | 状态 | 说明 |
|------|------|------|
| **Controller** | ✅ 完成 | `UserCartController` - 8个API接口 |
| **Service** | ✅ 完成 | `CartService` + `CartServiceImpl` |
| **Repository** | ✅ 完成 | `CartItemMapper` + XML映射文件 |
| **Entity** | ✅ 完成 | `CartItem` 实体类 |
| **DTO** | ✅ 完成 | 4个DTO类（请求/响应） |
| **API文档** | ✅ 完成 | Swagger注解完整 |
| **测试** | ✅ 完成 | 功能测试通过 |

**功能列表**：
- ✅ 添加商品到购物车
- ✅ 获取购物车列表
- ✅ 获取购物车汇总
- ✅ 更新购物车项数量
- ✅ 删除购物车项
- ✅ 批量删除购物车项
- ✅ 清空购物车
- ✅ 获取购物车商品数量

### ⚠️ **微信支付模块 - 开发完成度：85%**

| 组件 | 状态 | 说明 |
|------|------|------|
| **Controller** | ✅ 完成 | `UserPaymentController` - 2个API接口 |
| **Service** | ✅ 完成 | `WechatPayV3Service` + 实现类 |
| **配置类** | ✅ 完成 | `WechatPayConfig` 配置完整 |
| **DTO** | ✅ 完成 | 请求/响应DTO完整 |
| **工具类** | ✅ 完成 | 签名、加解密工具 |
| **支付通知** | ❌ **缺失** | 缺少支付通知Controller |
| **证书配置** | ✅ 完成 | 2024年新公钥方案 |

**已完成功能**：
- ✅ 创建支付订单（统一下单）
- ✅ 查询支付状态
- ✅ 生成小程序支付参数
- ✅ 支付通知处理逻辑（Service层）

**待完成功能**：
- ❌ 支付通知接收Controller
- ❌ 订单状态更新逻辑
- ❌ 支付失败处理

---

## 🚀 **微信支付小程序接入流程**

### 📱 **前端接入步骤**

#### 1. **用户下单流程**

```javascript
// 1. 用户选择商品，加入购物车
async function addToCart(skuId, quantity) {
  const response = await wx.request({
    url: 'https://your-domain.com/api/user/cart/add',
    method: 'POST',
    header: {
      'X-Session-Id': getSessionId() // 用户登录凭证
    },
    data: {
      skuId: skuId,
      quantity: quantity
    }
  });
  
  if (response.data.success) {
    wx.showToast({ title: '添加成功' });
    updateCartBadge(); // 更新购物车角标
  }
}

// 2. 获取购物车汇总
async function getCartSummary() {
  const response = await wx.request({
    url: 'https://your-domain.com/api/user/cart/summary',
    method: 'GET',
    header: {
      'X-Session-Id': getSessionId()
    }
  });
  
  return response.data.data; // 返回购物车汇总信息
}
```

#### 2. **创建支付订单**

```javascript
// 用户点击"立即支付"按钮
async function createPayment(orderData) {
  try {
    // 1. 创建支付订单
    const response = await wx.request({
      url: 'https://your-domain.com/api/user/payment/create',
      method: 'POST',
      header: {
        'X-Session-Id': getSessionId(),
        'Content-Type': 'application/json'
      },
      data: {
        orderNo: orderData.orderNo,        // 订单号
        totalAmount: orderData.totalAmount, // 支付金额（分）
        description: orderData.description, // 商品描述
        // openId 和 clientIp 后端自动获取
      }
    });

    if (response.data.success) {
      // 2. 获取支付参数
      const payParams = response.data.data;
      
      // 3. 调用微信支付
      await callWechatPay(payParams);
    } else {
      wx.showToast({
        title: response.data.message || '创建支付失败',
        icon: 'error'
      });
    }
  } catch (error) {
    console.error('创建支付订单失败:', error);
    wx.showToast({
      title: '网络错误，请重试',
      icon: 'error'
    });
  }
}
```

#### 3. **调用微信支付**

```javascript
// 调用微信支付API
async function callWechatPay(payParams) {
  try {
    // 调用微信支付
    const result = await wx.requestPayment({
      timeStamp: payParams.timeStamp,
      nonceStr: payParams.nonceStr,
      package: payParams.packageValue,
      signType: payParams.signType,
      paySign: payParams.paySign
    });

    // 支付成功
    console.log('支付成功:', result);
    
    // 跳转到支付成功页面
    wx.redirectTo({
      url: '/pages/payment/success?orderNo=' + payParams.orderNo
    });
    
  } catch (error) {
    console.error('支付失败:', error);
    
    if (error.errMsg === 'requestPayment:cancel') {
      // 用户取消支付
      wx.showToast({
        title: '支付已取消',
        icon: 'none'
      });
    } else {
      // 支付失败
      wx.showToast({
        title: '支付失败，请重试',
        icon: 'error'
      });
    }
  }
}
```

#### 4. **查询支付状态**

```javascript
// 查询支付状态（用于支付成功页面确认）
async function queryPaymentStatus(orderNo) {
  try {
    const response = await wx.request({
      url: `https://your-domain.com/api/user/payment/status/${orderNo}`,
      method: 'GET',
      header: {
        'X-Session-Id': getSessionId()
      }
    });

    if (response.data.success) {
      const status = response.data.data;
      
      switch (status) {
        case 'SUCCESS':
          // 支付成功
          showPaymentSuccess();
          break;
        case 'NOTPAY':
          // 未支付
          showPaymentPending();
          break;
        case 'CLOSED':
          // 已关闭
          showPaymentClosed();
          break;
        default:
          // 其他状态
          showPaymentUnknown();
      }
    }
  } catch (error) {
    console.error('查询支付状态失败:', error);
  }
}
```

### 📄 **页面示例代码**

#### 购物车页面 (cart.js)

```javascript
Page({
  data: {
    cartItems: [],
    totalAmount: 0,
    totalQuantity: 0,
    selectedItems: []
  },

  onLoad() {
    this.loadCartData();
  },

  // 加载购物车数据
  async loadCartData() {
    try {
      const response = await wx.request({
        url: 'https://your-domain.com/api/user/cart/summary',
        method: 'GET',
        header: {
          'X-Session-Id': getApp().globalData.sessionId
        }
      });

      if (response.data.success) {
        const summary = response.data.data;
        this.setData({
          cartItems: summary.items,
          totalAmount: summary.totalAmount,
          totalQuantity: summary.totalQuantity
        });
      }
    } catch (error) {
      console.error('加载购物车失败:', error);
    }
  },

  // 更新商品数量
  async updateQuantity(e) {
    const { cartItemId, quantity } = e.currentTarget.dataset;
    
    try {
      const response = await wx.request({
        url: `https://your-domain.com/api/user/cart/items/${cartItemId}`,
        method: 'PUT',
        header: {
          'X-Session-Id': getApp().globalData.sessionId,
          'Content-Type': 'application/json'
        },
        data: { quantity }
      });

      if (response.data.success) {
        this.loadCartData(); // 重新加载数据
      }
    } catch (error) {
      console.error('更新数量失败:', error);
    }
  },

  // 删除商品
  async deleteItem(e) {
    const { cartItemId } = e.currentTarget.dataset;
    
    try {
      const response = await wx.request({
        url: `https://your-domain.com/api/user/cart/items/${cartItemId}`,
        method: 'DELETE',
        header: {
          'X-Session-Id': getApp().globalData.sessionId
        }
      });

      if (response.data.success) {
        this.loadCartData(); // 重新加载数据
        wx.showToast({ title: '删除成功' });
      }
    } catch (error) {
      console.error('删除失败:', error);
    }
  },

  // 结算
  async checkout() {
    if (this.data.selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要结算的商品',
        icon: 'none'
      });
      return;
    }

    // 创建订单并跳转到支付
    const orderData = {
      orderNo: 'ORDER_' + Date.now(),
      totalAmount: this.data.totalAmount * 100, // 转换为分
      description: '眼镜商城订单'
    };

    await this.createPayment(orderData);
  },

  // 创建支付
  async createPayment(orderData) {
    try {
      const response = await wx.request({
        url: 'https://your-domain.com/api/user/payment/create',
        method: 'POST',
        header: {
          'X-Session-Id': getApp().globalData.sessionId,
          'Content-Type': 'application/json'
        },
        data: orderData
      });

      if (response.data.success) {
        const payParams = response.data.data;
        await this.callWechatPay(payParams, orderData.orderNo);
      } else {
        wx.showToast({
          title: response.data.message || '创建支付失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('创建支付失败:', error);
    }
  },

  // 调用微信支付
  async callWechatPay(payParams, orderNo) {
    try {
      await wx.requestPayment({
        timeStamp: payParams.timeStamp,
        nonceStr: payParams.nonceStr,
        package: payParams.packageValue,
        signType: payParams.signType,
        paySign: payParams.paySign
      });

      // 支付成功，跳转到成功页面
      wx.redirectTo({
        url: `/pages/payment/success?orderNo=${orderNo}`
      });

    } catch (error) {
      if (error.errMsg === 'requestPayment:cancel') {
        wx.showToast({
          title: '支付已取消',
          icon: 'none'
        });
      } else {
        wx.showToast({
          title: '支付失败，请重试',
          icon: 'error'
        });
      }
    }
  }
});
```

#### 支付成功页面 (payment-success.js)

```javascript
Page({
  data: {
    orderNo: '',
    paymentStatus: 'checking'
  },

  onLoad(options) {
    this.setData({
      orderNo: options.orderNo
    });
    
    // 查询支付状态
    this.checkPaymentStatus();
  },

  async checkPaymentStatus() {
    try {
      const response = await wx.request({
        url: `https://your-domain.com/api/user/payment/status/${this.data.orderNo}`,
        method: 'GET',
        header: {
          'X-Session-Id': getApp().globalData.sessionId
        }
      });

      if (response.data.success) {
        this.setData({
          paymentStatus: response.data.data
        });
      }
    } catch (error) {
      console.error('查询支付状态失败:', error);
      this.setData({
        paymentStatus: 'error'
      });
    }
  },

  // 返回首页
  goHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 查看订单
  viewOrder() {
    wx.navigateTo({
      url: `/pages/order/detail?orderNo=${this.data.orderNo}`
    });
  }
});
```

### 🔧 **工具函数**

```javascript
// utils/api.js
const API_BASE_URL = 'https://your-domain.com';

// 获取会话ID
function getSessionId() {
  return wx.getStorageSync('sessionId') || '';
}

// 更新购物车角标
async function updateCartBadge() {
  try {
    const response = await wx.request({
      url: `${API_BASE_URL}/api/user/cart/count`,
      method: 'GET',
      header: {
        'X-Session-Id': getSessionId()
      }
    });

    if (response.data.success) {
      const count = response.data.data;
      if (count > 0) {
        wx.setTabBarBadge({
          index: 2, // 购物车tab的索引
          text: count.toString()
        });
      } else {
        wx.removeTabBarBadge({
          index: 2
        });
      }
    }
  } catch (error) {
    console.error('更新购物车角标失败:', error);
  }
}

module.exports = {
  getSessionId,
  updateCartBadge
};
```

### 📝 **注意事项**

1. **用户登录**：所有API调用都需要在请求头中携带 `X-Session-Id`
2. **金额单位**：后端使用分为单位，前端需要进行转换
3. **错误处理**：需要对网络错误、业务错误进行适当处理
4. **支付状态**：支付成功后建议查询后端确认状态
5. **用户体验**：添加loading状态、错误提示等

### 🎯 **后续开发建议**

1. **完善支付通知处理**：添加支付通知接收Controller
2. **订单状态管理**：完善订单状态更新逻辑
3. **异常处理**：增强支付失败、网络异常的处理
4. **日志记录**：完善支付相关的日志记录
5. **测试验证**：进行完整的支付流程测试

这个接入流程涵盖了从购物车到支付完成的完整流程，可以直接用于小程序开发！🚀
