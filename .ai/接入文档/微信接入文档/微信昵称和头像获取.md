在微信小程序中获取用户头像和昵称，需遵循微信官方调整后的规则（2022年10月25日起生效）。原接口 `wx.getUserProfile` 和 `wx.getUserInfo` 已无法直接获取真实信息，改为通过 **「头像昵称填写能力」** 实现。以下是完整方案：

---

### ⚠️ 一、新规则核心变化
1. **强制调整**：用户主动点击按钮才能触发头像/昵称获取，禁止自动弹窗强制授权。
2. **安全检测**：微信会对用户上传的头像和输入的昵称进行安全审核，未通过内容会被清空。
3. **企业认证要求**：仅企业主体小程序可使用此能力（个人开发者无权限）。

---

### 📸 二、获取头像步骤
#### 方法：使用 `button` 组件 + `chooseAvatar` 事件
1. **WXML 代码**  
   设置按钮的 `open-type="chooseAvatar"` 并绑定事件：  
   ```html
   <button open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
     <image src="{{avatarUrl}}"></image>
   </button>
   ```

2. **JS 代码**  
   在事件回调中获取临时头像路径，并立即上传至服务器（临时路径会失效）：  
   ```javascript
   Page({
     data: { avatarUrl: '默认头像URL' },
     onChooseAvatar(e) {
       const { avatarUrl } = e.detail; // 获取临时路径
       this.setData({ avatarUrl });
       // 上传到服务器保存为永久地址
       wx.uploadFile({
         url: '你的服务器接口',
         filePath: avatarUrl,
         name: 'file',
         success: (res) => {
           const permanentUrl = JSON.parse(res.data).url; // 存储永久URL
         }
       });
     }
   });
   ```
   > 💡 注意：头像需通过 `wx.uploadFile` 上传保存，不可直接使用临时路径。

---

### 📝 三、获取昵称步骤
#### 方法：使用 `input` 组件 + `type="nickname"`
1. **WXML 代码**  
   设置输入框类型为 `nickname`，用户输入时微信自动关联其昵称：  
   ```html
   <form bindsubmit="onSubmit">
     <input name="nickname" type="nickname" placeholder="请输入昵称" />
     <button form-type="submit">提交</button>
   </form>
   ```

2. **JS 代码**  
   通过表单提交事件获取昵称：  
   ```javascript
   Page({
     onSubmit(e) {
       const nickName = e.detail.value.nickname;
       // 安全检测：若昵称违规，微信会清空输入值
       if (nickName) {
         console.log('用户昵称:', nickName); // 存储到服务器
       }
     }
   });
   ```

---

### ⚙️ 四、关键注意事项
1. **基础库要求**：  
   - 最低基础库版本 **2.21.2**（需兼容低版本可降级处理）。
   - 在 `app.json` 中设置最低基础库：  
     ```json
     "settings": { "miniprogramVersion": "2.21.2" }
     ```

2. **用户拒绝授权处理**：  
   若用户首次拒绝，需引导手动开启权限：  
   ```javascript
   wx.showModal({
     title: '提示',
     content: '请在设置中开启权限',
     success: (res) => {
       if (res.confirm) wx.openSetting(); // 跳转设置页
     }
   });
   ```

3. **安全策略**：  
   - 头像需通过微信安全检测（基础库 ≥2.24.4 自动支持）。
   - 昵称在输入框失焦（`onBlur`）时触发检测，违规内容自动清空。

---

### 💻 五、完整代码示例（企业小程序适用）
```html
<!-- WXML -->
<button open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
  <image src="{{avatarUrl}}"></image>
</button>
<form bindsubmit="onSubmit">
  <input name="nickname" type="nickname" placeholder="微信昵称" />
  <button form-type="submit">保存</button>
</form>
```

```javascript
// JS
Page({
  data: { avatarUrl: '默认头像URL' },
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail;
    this.setData({ avatarUrl });
    // 上传头像到服务器（略）
  },
  onSubmit(e) {
    const nickName = e.detail.value.nickname;
    // 提交昵称到服务器（略）
  }
});
```

---

### ✅ 六、常见问题解决
- **返回灰色头像/“微信用户”**：  
  - 检查小程序是否完成企业认证。
  - 确认基础库版本 ≥2.21.2。
- **移动端兼容问题**：  
  - iOS 需微信客户端 ≥8.0.16，安卓同理。

---

> 完整适配逻辑：**企业认证 + 用户主动触发 + 安全检测 + 临时路径转存**。若需低版本兼容（基础库 <2.21.2），可降级使用 `wx.getUserProfile`，但新版本必须按此规则调整。