# 用户头像管理接口调用指南

## 概述

本文档说明如何通过API接口管理用户头像，包括微信头像的上传、保存和更新操作。

## 基础信息

- **基础URL**: `http://localhost:8080` (开发环境) / `https://ss.seekeyes.cn` (生产环境)
- **认证方式**: 需要在请求头中包含 `X-Session-Id`
- **响应格式**: 统一使用 `Result<T>` 包装

## 检查结果

✅ **user模块已有完整的用户头像更新功能**

### 现有接口分析

#### 1. 更新用户信息接口（包含头像）

**接口**: `PUT /api/auth/profile`

**功能**: 更新用户个人信息，包括昵称、头像等

**请求头**:
```
X-Session-Id: {用户会话ID}
Content-Type: application/json
```

**请求示例**:
```json
{
  "nickname": "新昵称",
  "avatarUrl": "https://kk-bucket-01.oss-cn-hangzhou.aliyuncs.com/images/avatar_123456.jpg",
  "gender": 1,
  "country": "中国",
  "province": "广东省",
  "city": "深圳市"
}
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {
    "userId": 100001,
    "nickname": "新昵称",
    "avatarUrl": "https://kk-bucket-01.oss-cn-hangzhou.aliyuncs.com/images/avatar_123456.jpg",
    "gender": 1,
    "country": "中国",
    "province": "广东省",
    "city": "深圳市",
    "updatedAt": "2025-07-15T17:30:00",
    "success": true,
    "message": "用户信息更新成功"
  },
  "success": true
}
```

#### 2. 获取当前用户信息接口

**接口**: `GET /api/user/profile/current`

**功能**: 获取当前登录用户的详细信息（包含头像）

**请求头**:
```
X-Session-Id: {用户会话ID}
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "data": {
    "userId": 100001,
    "nickname": "用户昵称",
    "avatarUrl": "https://kk-bucket-01.oss-cn-hangzhou.aliyuncs.com/images/avatar_123456.jpg",
    "gender": 1,
    "phoneNumber": "13800138000"
  },
  "success": true
}
```

#### 3. 微信登录接口（自动获取头像）

**接口**: `POST /api/auth/wechat/login`

**功能**: 微信小程序登录时自动获取并保存微信头像

**请求示例**:
```json
{
  "code": "微信授权码",
  "userInfo": {
    "nickName": "微信昵称",
    "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/...",
    "gender": 1,
    "country": "中国",
    "province": "广东",
    "city": "深圳"
  }
}
```

## 完整的用户头像管理流程

### 方案一：直接使用微信头像（推荐）

```javascript
// 1. 微信登录时自动获取头像
const loginResult = await fetch('/api/auth/wechat/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    code: wechatCode,
    userInfo: {
      nickName: userInfo.nickName,
      avatarUrl: userInfo.avatarUrl,  // 微信头像URL
      gender: userInfo.gender
    }
  })
});

// 2. 系统自动保存微信头像到数据库
// 用户登录成功后，头像已经保存完成
```

### 方案二：上传自定义头像到OSS

```javascript
// 1. 上传头像图片到OSS
async function uploadAvatar(file) {
  // 获取OSS上传token
  const tokenResponse = await fetch('/api/oss/upload-token?category=avatar', {
    headers: {
      'X-Session-Id': sessionId
    }
  });
  
  const tokenData = await tokenResponse.json();
  
  // 直传到OSS
  const formData = new FormData();
  formData.append('key', tokenData.data.key);
  formData.append('policy', tokenData.data.policy);
  formData.append('OSSAccessKeyId', tokenData.data.accessKeyId);
  formData.append('signature', tokenData.data.signature);
  formData.append('file', file);
  
  const uploadResponse = await fetch(tokenData.data.host, {
    method: 'POST',
    body: formData
  });
  
  if (uploadResponse.ok) {
    return tokenData.data.key; // 返回OSS文件key
  }
  throw new Error('上传失败');
}

// 2. 保存头像信息到数据库
async function saveAvatarToDatabase(ossKey) {
  const avatarUrl = `https://kk-bucket-01.oss-cn-hangzhou.aliyuncs.com/${ossKey}`;
  
  // 保存图片信息到images表
  const saveImageResponse = await fetch('/api/oss/save-image', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Session-Id': sessionId
    },
    body: JSON.stringify({
      imageUrl: avatarUrl,
      imageName: '用户头像',
      description: '用户头像',
      category: 'avatar',
      businessType: 'user',
      businessId: userId
    })
  });
  
  return avatarUrl;
}

// 3. 更新用户头像
async function updateUserAvatar(avatarUrl) {
  const response = await fetch('/api/auth/profile', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'X-Session-Id': sessionId
    },
    body: JSON.stringify({
      avatarUrl: avatarUrl
    })
  });
  
  return response.json();
}

// 完整流程
async function uploadAndUpdateAvatar(file) {
  try {
    // 1. 上传到OSS
    const ossKey = await uploadAvatar(file);
    
    // 2. 保存到数据库
    const avatarUrl = await saveAvatarToDatabase(ossKey);
    
    // 3. 更新用户信息
    const result = await updateUserAvatar(avatarUrl);
    
    console.log('头像更新成功:', result);
    return result;
  } catch (error) {
    console.error('头像更新失败:', error);
    throw error;
  }
}
```

### 方案三：微信头像转存到OSS（推荐用于生产环境）✨新增

**接口**: `POST /api/oss/transfer-wechat-avatar`

**功能**: 下载微信头像并上传到OSS，解决微信头像URL过期问题

**请求示例**:
```json
{
  "wechatAvatarUrl": "https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKVUskibDnhMs4eeJN2OWMiaBIGgKicQn7jQFqTImqKcOwRmAoqt6nia9O6WRW0xts5BmCvdwK4RJOYcg/132",
  "userId": 100001,
  "description": "用户头像"
}
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "微信头像转存成功",
  "data": {
    "ossUrl": "https://kk-bucket-01.oss-cn-hangzhou.aliyuncs.com/images/avatar/1752571234567_abc123.jpg",
    "signedUrl": "https://kk-bucket-01.oss-cn-hangzhou.aliyuncs.com/images/avatar/1752571234567_abc123.jpg?Expires=xxx",
    "ossKey": "images/avatar/1752571234567_abc123.jpg",
    "originalWechatUrl": "https://thirdwx.qlogo.cn/mmopen/...",
    "imageId": 1945051248220225537,
    "fileSize": 51200,
    "fileType": "jpg",
    "transferTime": "2025-07-15T17:30:00",
    "success": true,
    "message": "微信头像转存成功"
  },
  "success": true
}
```

**JavaScript实现**:
```javascript
// 1. 转存微信头像到OSS
async function transferWechatAvatarToOSS(wechatAvatarUrl, userId) {
  const response = await fetch('/api/oss/transfer-wechat-avatar', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Session-Id': sessionId
    },
    body: JSON.stringify({
      wechatAvatarUrl: wechatAvatarUrl,
      userId: userId,
      description: '用户头像'
    })
  });

  return response.json();
}

// 2. 更新用户头像为OSS地址
async function updateAvatarFromWechat(wechatAvatarUrl, userId) {
  try {
    // 转存微信头像到OSS
    const transferResult = await transferWechatAvatarToOSS(wechatAvatarUrl, userId);

    if (transferResult.success) {
      // 更新用户头像为OSS地址
      const updateResult = await updateUserAvatar(transferResult.data.ossUrl);
      return updateResult;
    }
  } catch (error) {
    console.error('微信头像转存失败:', error);
    // 降级：直接使用微信头像URL
    return await updateUserAvatar(wechatAvatarUrl);
  }
}

// 3. 完整的微信登录后头像处理流程
async function handleWechatLoginAvatar(userInfo, userId) {
  if (userInfo.avatarUrl) {
    try {
      // 方案1：转存到OSS（推荐）
      const transferResult = await transferWechatAvatarToOSS(userInfo.avatarUrl, userId);
      if (transferResult.success) {
        // 使用OSS地址更新用户头像
        await updateUserAvatar(transferResult.data.ossUrl);
        return transferResult.data.signedUrl; // 返回可访问的预签名URL
      }
    } catch (error) {
      console.warn('微信头像转存失败，使用原始URL:', error);
    }

    // 方案2：降级使用微信原始URL
    await updateUserAvatar(userInfo.avatarUrl);
    return userInfo.avatarUrl;
  }
}
```

## 数据库设计

### users表中的头像字段
```sql
-- 用户表中的头像字段
avatar_url VARCHAR(500) COMMENT '用户头像URL'
```

### images表中的头像记录（可选）
```sql
-- 如果需要在images表中记录头像信息
INSERT INTO images (
  image_url, image_name, description, category, 
  business_type, business_id, sort_order, status
) VALUES (
  'https://kk-bucket-01.oss-cn-hangzhou.aliyuncs.com/images/avatar_123456.jpg',
  '用户头像', '用户头像', 'avatar', 'user', 100001, 0, 1
);
```

## 注意事项

1. **头像URL格式**: 支持微信头像URL和OSS URL两种格式
2. **图片大小限制**: 建议头像文件大小不超过2MB
3. **图片格式**: 支持JPG、PNG、WEBP格式
4. **缓存策略**: 微信头像URL有时效性，建议转存到OSS
5. **预签名URL**: OSS头像URL会自动转换为预签名URL，有效期1小时
6. **权限控制**: 只能更新自己的头像，需要有效的会话ID

## 错误处理

### 常见错误码
- `NOT_LOGIN`: 未登录或会话无效
- `PARAM_ERROR`: 参数错误（如头像URL格式不正确）
- `BUSINESS_ERROR`: 业务逻辑错误
- `SYSTEM_ERROR`: 系统错误

### 错误示例
```json
{
  "code": "NOT_LOGIN",
  "message": "会话无效或已过期，请重新登录",
  "data": null,
  "success": false
}
```

## 总结

✅ **用户头像管理功能已完全实现**

### 现有功能
- **完整的用户信息更新接口**: 已有完整的用户信息更新接口，包含头像字段
- **微信集成**: 微信登录时自动获取并保存头像
- **灵活更新**: 支持单独更新头像或批量更新用户信息
- **数据库支持**: users表中有avatar_url字段存储头像URL
- **安全验证**: 所有接口都需要有效的会话ID

### 新增功能 ✨
- **微信头像转存**: 新增 `POST /api/oss/transfer-wechat-avatar` 接口
- **解决过期问题**: 将微信头像下载并上传到OSS，避免微信URL过期
- **完整的响应信息**: 返回OSS URL、预签名URL、文件信息等
- **数据库记录**: 可选择将头像信息保存到images表
- **API文档**: 完整的Swagger文档和示例

### 接口测试结果
- ✅ 接口调用成功
- ✅ 参数验证正常
- ✅ 错误处理完善
- ✅ 响应格式正确

### 推荐使用方案
1. **生产环境**: 使用微信头像转存接口，确保头像永久可用
2. **开发环境**: 可直接使用微信头像URL，简化开发流程
3. **用户自定义**: 支持用户上传自定义头像到OSS

**当前状态**: 用户头像管理功能已完全实现，支持微信头像和自定义头像的完整管理流程！ 🎉
