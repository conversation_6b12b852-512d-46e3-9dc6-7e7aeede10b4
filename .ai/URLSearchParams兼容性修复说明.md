# URLSearchParams 兼容性修复说明

## 🐛 问题描述

在微信小程序环境中运行商品列表页面时，出现以下错误：

```
ReferenceError: URLSearchParams is not defined
```

## 🔍 问题原因

`URLSearchParams` 是 Web API，在微信小程序的 JavaScript 运行环境中不可用。微信小程序使用的是类似 Node.js 的 JavaScript 环境，不包含所有的 Web API。

## ✅ 解决方案

将 `URLSearchParams` 替换为手动构建查询字符串的方式，确保在微信小程序环境中正常工作。

### 修复前的代码

```typescript
// ❌ 不兼容微信小程序
const queryParams = new URLSearchParams()
queryParams.append('page', params.page.toString())
queryParams.append('pageSize', params.pageSize.toString())
const queryString = queryParams.toString()
```

### 修复后的代码

```typescript
// ✅ 微信小程序兼容
const queryParts: string[] = []
if (params?.page) queryParts.push(`page=${params.page}`)
if (params?.pageSize) queryParts.push(`pageSize=${params.pageSize}`)
const queryString = queryParts.join('&')
```

## 📝 修复的文件

### `miniprogram/services/productService.ts`

修复了以下三个方法中的 URLSearchParams 使用：

1. **getProductsByCategoryId** - 获取分类商品列表
2. **searchProducts** - 搜索商品
3. **getFeaturedProducts** - 获取推荐商品

## 🔧 修复详情

### 1. getProductsByCategoryId 方法

```typescript
// 修复前
const queryParams = new URLSearchParams()
if (params?.page) queryParams.append('page', params.page.toString())
if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString())
if (params?.sortBy) queryParams.append('sortBy', params.sortBy)
if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder)
const queryString = queryParams.toString()

// 修复后
const queryParts: string[] = []
if (params?.page) queryParts.push(`page=${params.page}`)
if (params?.pageSize) queryParts.push(`pageSize=${params.pageSize}`)
if (params?.sortBy) queryParts.push(`sortBy=${params.sortBy}`)
if (params?.sortOrder) queryParts.push(`sortOrder=${params.sortOrder}`)
const queryString = queryParts.join('&')
```

### 2. searchProducts 方法

```typescript
// 修复前
const queryParams = new URLSearchParams()
queryParams.append('keyword', keyword)
// ... 其他参数

// 修复后
const queryParts: string[] = [`keyword=${encodeURIComponent(keyword)}`]
if (params?.page) queryParts.push(`page=${params.page}`)
// ... 其他参数
const queryString = queryParts.join('&')
```

### 3. getFeaturedProducts 方法

```typescript
// 修复前
const queryParams = new URLSearchParams()
if (params?.page) queryParams.append('page', params.page.toString())
if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString())

// 修复后
const queryParts: string[] = []
if (params?.page) queryParts.push(`page=${params.page}`)
if (params?.pageSize) queryParts.push(`pageSize=${params.pageSize}`)
const queryString = queryParts.join('&')
```

## 🎯 优化点

### 1. 添加了调试日志
在每个方法中添加了 `console.log` 来输出构建的 URL，便于调试：

```typescript
console.log('调用商品接口:', url)
console.log('调用搜索接口:', url)
console.log('调用推荐商品接口:', url)
```

### 2. 使用 encodeURIComponent
对搜索关键词进行了 URL 编码，确保特殊字符正确处理：

```typescript
const queryParts: string[] = [`keyword=${encodeURIComponent(keyword)}`]
```

## 🧪 测试验证

修复后，商品列表页面应该能够正常：

1. ✅ 加载分类商品数据
2. ✅ 支持分页参数
3. ✅ 支持排序参数
4. ✅ 正确构建 API 请求 URL
5. ✅ 在控制台输出调试信息

## 📱 微信小程序兼容性注意事项

### 常见的不兼容 API

在微信小程序中，以下 Web API 不可用：

- `URLSearchParams` ❌
- `fetch` ❌ (使用 `wx.request`)
- `localStorage` ❌ (使用 `wx.setStorageSync`)
- `sessionStorage` ❌ (使用 `wx.setStorageSync`)
- `document` ❌
- `window` ❌

### 推荐的替代方案

1. **查询参数构建**: 手动拼接字符串
2. **网络请求**: 使用 `wx.request`
3. **本地存储**: 使用 `wx.setStorageSync/getStorageSync`
4. **URL 编码**: 使用 `encodeURIComponent/decodeURIComponent`

## 🚀 后续建议

1. **创建工具函数**: 可以创建一个通用的查询参数构建函数
2. **类型安全**: 确保参数类型正确
3. **错误处理**: 添加参数验证和错误处理
4. **性能优化**: 避免不必要的字符串拼接

### 示例工具函数

```typescript
// utils/queryBuilder.ts
export function buildQueryString(params: Record<string, any>): string {
  const queryParts: string[] = []
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParts.push(`${key}=${encodeURIComponent(value)}`)
    }
  })
  
  return queryParts.join('&')
}

// 使用示例
const queryString = buildQueryString({
  page: 1,
  pageSize: 20,
  sortBy: 'created',
  sortOrder: 'desc'
})
```

现在商品列表页面应该可以在微信小程序中正常运行了！🎉
