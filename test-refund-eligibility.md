# 退款资格检查测试用例

## 测试场景

### 1. 正常可退款订单
**请求**: `GET /api/refund/check-eligibility/67`
**期望响应**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {
    "canRefund": true,
    "suggestedRefundType": "refund_only",
    "refundableItems": [...],
    "order": {...}
  },
  "success": true
}
```
**前端行为**: 正常进入退款申请页面

### 2. 已有退款申请的订单
**请求**: `GET /api/refund/check-eligibility/68`
**期望响应**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {
    "canRefund": false,
    "reason": "该订单已有退款申请正在处理中"
  },
  "success": true
}
```
**前端行为**: 显示弹窗，内容为"该订单已有退款申请正在处理中"

### 3. 超过退款时限的订单
**请求**: `GET /api/refund/check-eligibility/69`
**期望响应**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {
    "canRefund": false,
    "reason": "订单已超过退款时限"
  },
  "success": true
}
```
**前端行为**: 显示弹窗，内容为"订单已超过退款时限"

### 4. 订单状态不符合退款条件
**请求**: `GET /api/refund/check-eligibility/70`
**期望响应**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {
    "canRefund": false,
    "reason": "订单状态不支持退款"
  },
  "success": true
}
```
**前端行为**: 显示弹窗，内容为"订单状态不支持退款"

## 修复前后对比

### 修复前
- 所有不能退款的情况都显示："该订单不符合退款条件"
- 用户无法了解具体原因

### 修复后
- 显示服务端返回的具体原因
- 如果服务端没有返回原因，则显示默认提示
- 用户能够清楚了解为什么不能退款

## 测试步骤

1. 找一个已有退款申请的订单（如订单ID 68）
2. 在订单详情页面点击"申请退款"
3. 观察弹窗提示内容
4. 确认显示的是"该订单已有退款申请正在处理中"而不是"该订单不符合退款条件"
