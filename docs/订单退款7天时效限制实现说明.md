# 订单退款7天时效限制实现说明

## 🔍 **需求描述**

微信小程序端的【订单详情】页面当订单完成后如果超过7天，【申请退款】按钮不可点击，实现退款时效限制。

## 🎯 **实现方案**

### 业务规则
- **时效期限**：订单完成后7天内可申请退款
- **计算基准**：以订单的 `completedAt`（完成时间）为起点
- **显示逻辑**：
  - 超过7天：按钮禁用，显示"申请退款（已过期）"
  - 当天到期：显示"申请退款（今日到期）"
  - 3天内到期：显示"申请退款（X天后到期）"
  - 其他情况：显示"申请退款"

## ✅ **后端实现**

### 1. 修改OrderDetailDTO

#### 文件：`OrderDetailDTO.java`
```java
/**
 * 是否可以申请退款
 */
private Boolean canApplyRefund;

/**
 * 退款时效剩余天数（-1表示已过期，0表示今天到期，>0表示剩余天数）
 */
private Integer refundDaysRemaining;

/**
 * 退款截止时间
 */
private LocalDateTime refundDeadline;
```

### 2. 修改OrderServiceImpl

#### 文件：`OrderServiceImpl.java`
```java
/**
 * 计算退款时效信息
 */
private void calculateRefundTimeLimit(OrderDetailDTO dto, Order order) {
    // 只有已完成的订单才需要计算退款时效
    if (!"completed".equals(order.getStatus()) || order.getCompletedAt() == null) {
        dto.setCanApplyRefund(false);
        dto.setRefundDaysRemaining(null);
        dto.setRefundDeadline(null);
        return;
    }

    // 退款时效：订单完成后7天内可申请退款
    LocalDateTime completedAt = order.getCompletedAt();
    LocalDateTime refundDeadline = completedAt.plusDays(7);
    LocalDateTime now = LocalDateTime.now();

    // 计算剩余天数
    long daysRemaining = java.time.temporal.ChronoUnit.DAYS.between(now.toLocalDate(), refundDeadline.toLocalDate());
    
    // 设置退款信息
    dto.setRefundDeadline(refundDeadline);
    dto.setRefundDaysRemaining((int) daysRemaining);
    dto.setCanApplyRefund(daysRemaining >= 0); // 大于等于0表示还可以申请退款
}
```

## ✅ **前端实现**

### 1. 修改OrderService

#### 文件：`orderService.ts`
```typescript
/**
 * 获取订单状态对应的操作按钮
 * @param status 订单状态
 * @param orderInfo 订单信息（可选，用于退款时效判断）
 */
getOrderActions(status: string, orderInfo?: any): Array<{text: string, action: string, type?: string, disabled?: boolean}> {
  switch (status) {
    case OrderStatus.COMPLETED:
      // 检查是否可以申请退款（基于7天时效）
      const canApplyRefund = orderInfo?.canApplyRefund !== false
      const refundDaysRemaining = orderInfo?.refundDaysRemaining
      
      let refundButtonText = '申请退款'
      if (refundDaysRemaining !== undefined && refundDaysRemaining !== null) {
        if (refundDaysRemaining < 0) {
          refundButtonText = '申请退款（已过期）'
        } else if (refundDaysRemaining === 0) {
          refundButtonText = '申请退款（今日到期）'
        } else if (refundDaysRemaining <= 3) {
          refundButtonText = `申请退款（${refundDaysRemaining}天后到期）`
        }
      }
      
      return [
        { 
          text: refundButtonText, 
          action: 'refund', 
          type: 'primary',
          disabled: !canApplyRefund
        }
      ]
    // 其他状态...
  }
}
```

### 2. 修改订单详情页面

#### 文件：`order-detail.ts`
```typescript
// 获取订单可用操作，传递订单信息用于退款时效判断
const orderActions = OrderService.getOrderActions(orderInfo.status, orderInfo)

// 处理订单操作时检查按钮是否被禁用
async onOrderAction(e: any) {
  const { action } = e.currentTarget.dataset
  const { orderInfo, orderActions } = this.data
  
  // 检查按钮是否被禁用
  const actionButton = orderActions.find(btn => btn.action === action)
  if (actionButton?.disabled) {
    wx.showToast({
      title: '该操作已过期',
      icon: 'none'
    })
    return
  }
  // 继续处理...
}
```

#### 文件：`order-detail.wxml`
```xml
<button wx:for="{{orderActions}}" wx:key="action"
        class="action-btn {{item.type === 'primary' ? 'primary' : 'secondary'}} {{item.disabled ? 'disabled' : ''}}" 
        bindtap="onOrderAction" 
        data-action="{{item.action}}"
        disabled="{{item.disabled}}">
  {{item.text}}
</button>
```

#### 文件：`order-detail.wxss`
```css
.action-btn.disabled {
  background-color: #f5f5f5 !important;
  color: #ccc !important;
  border-color: #eee !important;
  cursor: not-allowed;
}

.action-btn.disabled:active {
  opacity: 1;
}
```

## 🎯 **功能效果**

### 按钮状态变化
| 剩余天数 | 按钮文本 | 按钮状态 | 点击效果 |
|----------|----------|----------|----------|
| > 3天 | "申请退款" | 正常 | 可点击 |
| 1-3天 | "申请退款（X天后到期）" | 正常 | 可点击 |
| 0天 | "申请退款（今日到期）" | 正常 | 可点击 |
| < 0天 | "申请退款（已过期）" | 禁用 | 显示"该操作已过期" |

### 计算示例
```
订单完成时间：2024-01-01 10:00:00
退款截止时间：2024-01-08 10:00:00

当前时间：2024-01-05 15:00:00
剩余天数：3天 → 显示"申请退款（3天后到期）"

当前时间：2024-01-08 15:00:00  
剩余天数：0天 → 显示"申请退款（今日到期）"

当前时间：2024-01-09 15:00:00
剩余天数：-1天 → 显示"申请退款（已过期）"，按钮禁用
```

## 🧪 **测试验证**

### 测试场景
1. **正常情况**：
   - 订单完成后1-6天内，按钮正常可点击
   - 显示相应的剩余天数提示

2. **临界情况**：
   - 订单完成后第7天（当天到期），按钮可点击
   - 显示"今日到期"提示

3. **过期情况**：
   - 订单完成后第8天及以后，按钮禁用
   - 显示"已过期"，点击显示提示

4. **边界测试**：
   - 跨天时间点的计算准确性
   - 不同时区的时间计算

### API响应示例
```json
{
  "success": true,
  "data": {
    "id": 123,
    "orderNumber": "202401010001",
    "status": "completed",
    "completedAt": "2024-01-01T10:00:00",
    "canApplyRefund": true,
    "refundDaysRemaining": 3,
    "refundDeadline": "2024-01-08T10:00:00",
    // 其他字段...
  }
}
```

## 📋 **部署清单**

### 后端部署
- [ ] 修改 `OrderDetailDTO.java`
- [ ] 修改 `OrderServiceImpl.java`
- [ ] 测试订单详情接口返回数据
- [ ] 验证时效计算逻辑

### 前端部署
- [ ] 修改 `orderService.ts`
- [ ] 修改 `order-detail.ts`
- [ ] 修改 `order-detail.wxml`
- [ ] 修改 `order-detail.wxss`
- [ ] 修改 `orders.ts`（订单列表页面）
- [ ] 测试按钮显示和交互

### 测试验证
- [ ] 创建测试订单并完成
- [ ] 验证不同时间点的按钮状态
- [ ] 测试禁用按钮的点击效果
- [ ] 验证时效计算的准确性

---

**实现完成**：订单完成后7天内可申请退款，超过7天按钮自动禁用，提供清晰的时效提示。
