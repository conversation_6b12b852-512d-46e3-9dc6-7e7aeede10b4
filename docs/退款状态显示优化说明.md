# 微信小程序端退款状态显示优化说明

## 📋 优化需求

**问题描述**：微信小程序端的【我的退款】页面中，服务端返回的订单状态为【MERCHANT_RECEIVED】（商家已收货）的退款申请，需要显示到【等待退款】的标签页面下。

## 🎯 优化目标

1. **用户体验优化**：将 `MERCHANT_RECEIVED` 状态在小程序端显示为"等待退款"，让用户更清楚地了解当前状态
2. **状态归类优化**：在退款列表的标签页中，将 `MERCHANT_RECEIVED` 和 `PENDING_REFUND` 都归类到"等待退款"标签页下
3. **显示一致性**：确保列表页和详情页的状态显示保持一致

## 🔧 实现方案

### 1. 退款列表页面优化 (`refund-list.ts`)

#### 1.1 标签页结构调整
```typescript
// 修改前
{ key: RefundStatus.PENDING_REFUND, name: '等待退款', count: 0 }

// 修改后  
{ key: 'waiting_refund', name: '等待退款', count: 0 } // 使用虚拟状态聚合多个实际状态
```

#### 1.2 状态名称映射
```typescript
getStatusName(status: string): string {
  const statusMap: Record<string, string> = {
    [RefundStatus.PENDING_REVIEW]: '待审核',
    [RefundStatus.APPROVED]: '已同意',
    'waiting_refund': '等待退款', // 虚拟状态
    [RefundStatus.PENDING_REFUND]: '等待退款',
    [RefundStatus.MERCHANT_RECEIVED]: '等待退款', // 关键修改：显示为等待退款
    [RefundStatus.USER_SHIPPING]: '寄回中',
    [RefundStatus.REFUNDED]: '已退款'
  }
  return statusMap[status] || ''
}
```

#### 1.3 数据过滤逻辑
```typescript
// 如果是"等待退款"标签页，需要过滤出相关状态的退款
let filteredRecords = result.records
if (currentStatus === 'waiting_refund') {
  filteredRecords = result.records.filter(refund => 
    refund.status === RefundStatus.PENDING_REFUND || 
    refund.status === RefundStatus.MERCHANT_RECEIVED
  )
}
```

#### 1.4 样式类名统一
```typescript
getStatusClass(status: RefundStatus): string {
  const classMap: Record<RefundStatus, string> = {
    // ...其他状态
    [RefundStatus.PENDING_REFUND]: 'pending-refund',
    [RefundStatus.MERCHANT_RECEIVED]: 'pending-refund', // 使用相同样式
    // ...
  }
  return classMap[status] || 'default'
}
```

### 2. 退款详情页面优化 (`refund-detail.ts`)

#### 2.1 状态样式统一
```typescript
getStatusClass(status: RefundStatus): string {
  const classMap: Record<RefundStatus, string> = {
    // ...其他状态
    [RefundStatus.MERCHANT_RECEIVED]: 'pending-refund', // 与列表页保持一致
    // ...
  }
  return classMap[status] || 'default'
}
```

### 3. 退款服务优化 (`refundService.ts`)

#### 3.1 状态描述统一
```typescript
getStatusDescription(status: string): string {
  const statusMap: Record<string, string> = {
    'pending_review': '待审核',
    'approved': '已同意',
    'pending_refund': '等待退款',
    'merchant_received': '等待退款', // 关键修改：显示为等待退款
    'user_shipping': '用户寄回中',
    'refunded': '已退款',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知状态'
}
```

#### 3.2 状态格式化统一
```typescript
formatRefundStatus(status: RefundStatus): string {
  const statusMap: Record<RefundStatus, string> = {
    [RefundStatus.PENDING_REVIEW]: '待审核',
    [RefundStatus.APPROVED]: '已同意',
    [RefundStatus.PENDING_REFUND]: '等待退款',
    [RefundStatus.MERCHANT_RECEIVED]: '等待退款', // 统一显示
    [RefundStatus.USER_SHIPPING]: '用户寄回中',
    [RefundStatus.REFUNDED]: '已退款',
    [RefundStatus.CANCELLED]: '已取消'
  }
  return statusMap[status] || status
}
```

## 📊 优化效果

### 优化前
- `MERCHANT_RECEIVED` 状态显示为"商家已收货"
- 用户不清楚下一步操作，需要等待什么
- 状态分散在不同标签页，查找不便

### 优化后
- `MERCHANT_RECEIVED` 状态显示为"等待退款"
- 用户明确知道正在等待退款处理
- `PENDING_REFUND` 和 `MERCHANT_RECEIVED` 都归类到"等待退款"标签页
- 提升用户体验和操作便利性

## 🔄 状态流转说明

### 仅退款流程
1. `PENDING_REVIEW` (待审核) → `PENDING_REFUND` (等待退款) → `REFUNDED` (已退款)

### 退货退款流程  
1. `PENDING_REVIEW` (待审核) → `APPROVED` (已同意) → `USER_SHIPPING` (寄回中) → `MERCHANT_RECEIVED` (等待退款) → `REFUNDED` (已退款)

## ✅ 测试验证

1. **列表页测试**：
   - 验证"等待退款"标签页包含 `PENDING_REFUND` 和 `MERCHANT_RECEIVED` 状态的退款
   - 验证状态显示文本正确

2. **详情页测试**：
   - 验证 `MERCHANT_RECEIVED` 状态显示为"等待退款"
   - 验证样式类名与列表页一致

3. **交互测试**：
   - 验证标签页切换功能正常
   - 验证数据过滤逻辑正确

## 📝 注意事项

1. **向后兼容**：保持原有状态枚举不变，只修改显示逻辑
2. **服务端状态**：服务端状态保持不变，只在客户端进行显示优化
3. **样式一致性**：确保所有页面的状态显示保持一致
4. **用户理解**：优化后的状态名称更符合用户的理解习惯

## 🚀 部署说明

1. 更新小程序代码
2. 测试各个退款状态的显示效果
3. 验证标签页过滤功能
4. 发布小程序版本更新
