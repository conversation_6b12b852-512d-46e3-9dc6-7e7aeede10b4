# 退款页面重复请求修复说明

## 🐛 **问题描述**

在【我的】页面点击【退款/售后】按钮时，会发送两个重复的 `/api/refund/list` 请求：
1. `/api/refund/list?pageNum=1&pageSize=10` - 用于获取退款列表数据
2. `/api/refund/list?pageNum=1&pageSize=1` - 用于获取退款总数统计

这种重复请求是不合理的，会造成不必要的网络开销和服务器负担。

## 🔍 **问题根本原因**

### 调用链路分析：
1. **用户操作**：点击【我的】页面的【退款/售后】按钮
2. **触发方法**：`profile.ts` 中的 `goToRefunds()` 方法
3. **页面跳转**：跳转到 `/pages/orders/orders?status=refund_afterSale`
4. **页面加载**：`orders.ts` 的 `onLoad()` 方法被调用
5. **两个并行调用**：
   - `loadRefunds()` → 发送 `pageSize=10` 的请求获取退款列表
   - `loadOrderCounts()` → 发送 `pageSize=1` 的请求获取统计数据

### 问题代码位置：
```typescript
// miniprogram/pages/orders/orders.ts - onLoad 方法
if (status === 'refund_afterSale') {
  this.loadRefunds()        // 第一个请求：pageSize=10
} else {
  this.loadOrders()
}
this.loadOrderCounts()      // 第二个请求：pageSize=1
```

```typescript
// miniprogram/pages/orders/orders.ts - loadOrderCounts 方法
async loadOrderCounts() {
  const [counts, refundResult] = await Promise.all([
    OrderService.getOrderCounts(),
    RefundService.getRefundList({ pageNum: 1, pageSize: 1 })  // 重复请求
  ])
}
```

## ✅ **修复方案**

### 核心思路：
优化退款页面的数据加载逻辑，避免重复请求同一个接口。当进入退款页面时，使用一个统一的方法来加载退款数据和统计信息。

### 具体修改：

#### 1. 新增 `loadRefundsAndCounts()` 方法
```typescript
/**
 * 加载退款数据和统计信息（避免重复请求）
 */
async loadRefundsAndCounts() {
  try {
    // 并行加载退款数据和订单统计
    const [refundResult, orderCounts] = await Promise.all([
      this.loadRefunds(),
      OrderService.getOrderCounts()
    ])

    // 使用退款数据的总数更新统计标签
    const statusTabs = this.data.statusTabs.map(tab => {
      if (tab.key === '') {
        return { ...tab, count: orderCounts.total }
      } else if (tab.key === 'refund_afterSale') {
        return { ...tab, count: refundResult.total || 0 }  // 复用已获取的总数
      } else {
        return { ...tab, count: orderCounts[tab.key] || 0 }
      }
    })

    this.setData({ statusTabs })
  } catch (error) {
    console.error('加载退款数据和统计失败:', error)
    this.loadOrderCounts()
  }
}
```

#### 2. 修改 `onLoad()` 方法
```typescript
// 根据状态类型加载不同的数据
if (status === 'refund_afterSale') {
  // 对于退款页面，使用优化的加载方法，避免重复请求
  this.loadRefundsAndCounts()
} else {
  this.loadOrders()
  this.loadOrderCounts()
}
```

#### 3. 修改 `loadRefunds()` 方法
```typescript
async loadRefunds(isRefresh: boolean = false) {
  // ... 原有逻辑 ...
  
  return result  // 返回结果，供其他方法使用
}
```

#### 4. 优化其他相关方法
- `refreshOrders()` - 刷新时也使用优化逻辑
- `onTabChange()` - 标签切换时使用优化逻辑

## 🎯 **修复效果**

### 修复前：
- 点击【退款/售后】→ 发送 2 个 `/api/refund/list` 请求
- 网络请求冗余，增加服务器负担

### 修复后：
- 点击【退款/售后】→ 发送 1 个 `/api/refund/list` 请求 + 1 个 `/api/user/orders/count/all-status` 请求
- 消除重复请求，提升性能
- 保持功能完整性，用户体验不变

## 📋 **测试验证**

### 测试步骤：
1. 打开小程序，进入【我的】页面
2. 点击【退款/售后】按钮
3. 观察网络请求面板

### 预期结果：
- 只发送一个 `/api/refund/list?pageNum=1&pageSize=10` 请求
- 发送一个 `/api/user/orders/count/all-status` 请求
- 不再有 `/api/refund/list?pageNum=1&pageSize=1` 请求
- 页面功能正常，退款列表和统计数据正确显示

## 📝 **修改文件清单**

- `miniprogram/pages/orders/orders.ts` - 主要修改文件
  - 新增 `loadRefundsAndCounts()` 方法
  - 修改 `onLoad()` 方法
  - 修改 `loadRefunds()` 方法返回值
  - 优化 `refreshOrders()` 和 `onTabChange()` 方法

## 🔄 **兼容性说明**

- 修改完全向后兼容
- 不影响其他页面和功能
- 不需要后端配合修改
- 保持原有的用户体验
