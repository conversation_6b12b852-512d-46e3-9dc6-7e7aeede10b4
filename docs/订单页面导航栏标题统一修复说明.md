# 订单页面导航栏标题统一修复说明

## 🐛 **问题描述**

在【我的】页面点击不同的订单状态按钮（如【退款/售后】、【待发货】、【待收货】等）跳转到订单页面后，顶部的 navigationBar 标题显示不一致：

- 点击【待支付】→ 显示"待支付订单"
- 点击【待发货】→ 显示"待发货订单"  
- 点击【待收货】→ 显示"待收货订单"
- 点击【退款/售后】→ 显示"退款/售后订单"

**期望行为**：所有这些 tab 的标题都应该统一显示为"我的订单"。

## 🔍 **问题根本原因**

问题出现在 `orders.ts` 页面的 `onLoad` 方法中：

```typescript
// 设置页面标题
const statusName = this.getStatusName(status)
wx.setNavigationBarTitle({
  title: statusName ? `${statusName}订单` : '我的订单'
})
```

代码根据传入的状态参数动态设置不同的标题，导致标题不统一。

### 相关代码位置：
1. **主要问题**：`miniprogram/pages/orders/orders.ts` 第 58-61 行
2. **次要问题**：`miniprogram/pages/orders/orders.ts` 第 759-761 行（微信订单管理跳转场景）

## ✅ **修复方案**

### 核心思路：
统一所有场景下的导航栏标题，无论从哪个入口进入订单页面，都显示"我的订单"。

### 具体修改：

#### 1. 修改 onLoad 方法中的标题设置
```typescript
// 修复前：
const statusName = this.getStatusName(status)
wx.setNavigationBarTitle({
  title: statusName ? `${statusName}订单` : '我的订单'
})

// 修复后：
// 设置页面标题 - 统一显示为"我的订单"
wx.setNavigationBarTitle({
  title: '我的订单'
})
```

#### 2. 修改微信订单管理跳转场景的标题设置
```typescript
// 修复前：
wx.setNavigationBarTitle({
  title: '订单详情'
})

// 修复后：
// 设置页面标题 - 统一显示为"我的订单"
wx.setNavigationBarTitle({
  title: '我的订单'
})
```

## 🎯 **修复效果**

### 修复前：
- 【待支付】→ "待支付订单"
- 【待发货】→ "待发货订单"
- 【待收货】→ "待收货订单"
- 【退款/售后】→ "退款/售后订单"
- 微信订单管理跳转 → "订单详情"

### 修复后：
- 【待支付】→ "我的订单"
- 【待发货】→ "我的订单"
- 【待收货】→ "我的订单"
- 【退款/售后】→ "我的订单"
- 微信订单管理跳转 → "我的订单"

## 📋 **测试验证**

### 测试步骤：
1. 打开小程序，进入【我的】页面
2. 分别点击以下按钮，观察导航栏标题：
   - 【待支付】订单状态
   - 【待发货】订单状态
   - 【待收货】订单状态
   - 【退款/售后】按钮
3. 从微信订单管理跳转到小程序（如果有配置）

### 预期结果：
- 所有场景下导航栏标题都显示为"我的订单"
- 页面功能正常，订单列表正确显示
- 标签切换功能正常

## 📝 **修改文件清单**

### 主要修改：
- `miniprogram/pages/orders/orders.ts`
  - 第 57-60 行：统一 onLoad 方法中的标题设置
  - 第 758-761 行：统一微信订单管理跳转场景的标题设置

### 配置文件（无需修改）：
- `miniprogram/pages/orders/orders.json` - 已正确配置默认标题为"我的订单"

## 🔄 **兼容性说明**

- 修改完全向后兼容
- 不影响页面功能和用户体验
- 不需要后端配合修改
- 符合用户界面一致性原则

## 📊 **代码质量**

### 遵循编码规范：
- ✅ 添加了详细的注释说明修改原因
- ✅ 保持代码结构清晰
- ✅ 不影响现有功能逻辑
- ✅ 符合微信小程序开发规范

### 保留的方法：
- `getStatusName()` 方法保留，虽然当前未使用，但可能在其他地方有用

## 🧪 **回归测试**

需要测试以下场景确保没有引入新问题：
- [ ] 订单列表正常显示
- [ ] 标签切换功能正常
- [ ] 下拉刷新功能正常
- [ ] 上拉加载更多功能正常
- [ ] 订单操作按钮功能正常
- [ ] 退款列表功能正常
- [ ] 从其他页面跳转到订单页面功能正常
