# 退款页面问题修复说明

## 🔍 **问题描述**

### 问题1：订单状态显示英文
- **现象**：订单状态显示为英文代码（如 "shipped"）
- **期望**：应该显示中文状态名称（如 "已发货"）

### 问题2：仅退款逻辑错误
- **现象**：已发货的商品仍然显示"仅退款"选项
- **正确逻辑**：只有"已支付+待发货"状态的订单才应该显示"仅退款"

### 问题3：退款数量显示宽度问题
- **现象**：退款数量控件超出页面宽度范围
- **影响**：用户体验差，操作不便

## ✅ **修复方案**

### 1. 修复订单状态显示中文

#### 修改文件：`refund-apply.ts`
```typescript
// 订单状态映射为中文
const getOrderStatusText = (status: string): string => {
  const statusMap: { [key: string]: string } = {
    'pending_payment': '待支付',
    'paid_shipped': '已支付+待发货',
    'shipped': '已发货',
    'completed': '已完成',
    'cancelled': '已取消',
    'refunded': '已退款'
  }
  return statusMap[status] || status
}

// 在订单信息中添加中文状态
const orderInfo = {
  // ... 其他字段
  orderStatus: eligibilityData.order.status,
  orderStatusText: getOrderStatusText(eligibilityData.order.status),
  // ... 其他字段
}
```

#### 修改文件：`refund-apply.wxml`
```xml
<view class="order-item">
  <text class="label">订单状态：</text>
  <text class="value">{{orderInfo.orderStatusText}}</text>
</view>
```

### 2. 修复退款类型显示逻辑

#### 修改文件：`refund-apply.wxml`
```xml
<!-- 退款类型 -->
<view class="refund-type-section">
  <view class="section-title">退款类型</view>
  <radio-group bindchange="onRefundTypeChange">
    <!-- 仅退款：只有未发货状态(paid_shipped)才显示 -->
    <label wx:if="{{orderInfo.orderStatus === 'paid_shipped'}}" class="radio-item">
      <radio value="refund_only" checked="{{refundType === 'refund_only'}}" />
      <text class="radio-text">仅退款</text>
      <text class="radio-desc">不需要退货，直接退款</text>
    </label>
    <!-- 退货退款：已发货状态(shipped/completed)才显示 -->
    <label wx:if="{{orderInfo.orderStatus === 'shipped' || orderInfo.orderStatus === 'completed'}}" class="radio-item">
      <radio value="return_refund" checked="{{refundType === 'return_refund'}}" />
      <text class="radio-text">退货退款</text>
      <text class="radio-desc">需要退货后退款</text>
    </label>
  </radio-group>
</view>
```

#### 修改文件：`refund-apply.ts`
```typescript
// 根据订单状态确定退款类型
let finalRefundType = eligibilityData.suggestedRefundType

// 确保退款类型与订单状态匹配
if (orderInfo.orderStatus === 'paid_shipped') {
  finalRefundType = RefundType.REFUND_ONLY
} else if (orderInfo.orderStatus === 'shipped' || orderInfo.orderStatus === 'completed') {
  finalRefundType = RefundType.RETURN_REFUND
}
```

### 3. 修复退款数量显示宽度问题

#### 修改文件：`refund-apply.wxss`
```css
/* 内容区域优化 */
.content {
  padding: 16rpx;
  box-sizing: border-box;
}

/* 通用区块样式优化 */
.order-section,
.refund-type-section,
.refund-items-section,
.refund-reason-section,
.refund-description-section,
.evidence-section,
.refund-amount-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

/* 商品卡片优化 */
.item-card {
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  padding: 16rpx;
  box-sizing: border-box;
  width: 100%;
}

/* 退款数量选择器优化 */
.quantity-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
}

.quantity-label {
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
  max-width: 140rpx;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-shrink: 0;
  max-width: 180rpx;
}

.quantity-btn {
  width: 48rpx;
  height: 48rpx;
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  background-color: #fff;
  font-size: 24rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  flex-shrink: 0;
  line-height: 1;
}

.quantity-value {
  font-size: 26rpx;
  color: #333;
  min-width: 40rpx;
  text-align: center;
  flex-shrink: 0;
}
```

#### 修改文件：`refund-apply.wxml`
```xml
<view wx:if="{{item.canRefund}}" class="item-refund">
  <view class="quantity-selector">
    <text class="quantity-label">退款数量：</text>
    <view class="quantity-controls">
      <button class="quantity-btn" bindtap="onQuantityChange"
              data-item-id="{{item.orderItemId}}"
              data-action="decrease">-</button>
      <text class="quantity-value">{{item.refundQuantity || 0}}</text>
      <button class="quantity-btn" bindtap="onQuantityChange"
              data-item-id="{{item.orderItemId}}"
              data-action="increase">+</button>
    </view>
  </view>
  <view class="refund-amount-row">
    <text class="refund-amount">退款：¥{{utils.formatAmount(item.refundAmount || 0)}}</text>
  </view>
</view>
```

## 🎯 **修复效果**

### 1. 订单状态显示优化
- ✅ **订单状态显示中文**：`shipped` → `已发货`
- ✅ **状态映射完整**：支持所有订单状态的中文显示

### 2. 退款类型显示逻辑
- ✅ **已支付+待发货** (`paid_shipped`) 状态：只显示"仅退款"
- ✅ **已发货** (`shipped`) 状态：只显示"退货退款"
- ✅ **已完成** (`completed`) 状态：只显示"退货退款"

### 3. 退款数量显示优化
- ✅ 控件不再超出页面宽度
- ✅ 在小屏幕设备上正常显示
- ✅ 按钮尺寸优化（48rpx），更适合小屏幕
- ✅ 间距优化（8rpx），节省空间
- ✅ 添加最大宽度限制，防止超出
- ✅ 退款金额单独一行显示，布局更清晰

## 📋 **测试验证**

### 测试场景1：订单状态显示
1. 进入不同状态的订单退款申请页面
2. 确认订单状态显示为中文（如"已发货"而不是"shipped"）
3. 验证所有状态映射正确

### 测试场景2：已支付+待发货订单
1. 进入退款申请页面
2. 确认订单状态显示"已支付+待发货"
3. 确认只显示"仅退款"选项
4. 确认退款数量控件显示正常

### 测试场景3：已发货订单
1. 进入退款申请页面
2. 确认订单状态显示"已发货"
3. 确认只显示"退货退款"选项
4. 确认退款数量控件显示正常

### 测试场景4：小屏幕设备
1. 在不同尺寸的设备上测试
2. 确认退款数量控件不超出屏幕
3. 确认所有按钮都能正常点击
4. 验证在最小屏幕宽度下的显示效果

## 🔧 **技术要点**

### 1. 条件渲染
使用 `wx:if` 根据订单状态条件渲染不同的退款类型选项

### 2. 响应式布局
- 使用 `flex-wrap: wrap` 允许元素换行
- 使用 `flex-shrink: 0` 防止关键元素被压缩
- 调整元素尺寸和间距适应小屏幕

### 3. 状态管理
在TypeScript中确保退款类型与订单状态保持一致

## 🚀 **部署说明**

1. 修改完成后重新编译小程序
2. 在开发者工具中测试各种订单状态
3. 在真机上测试不同屏幕尺寸的显示效果
4. 确认功能正常后发布到生产环境

---

**修复完成**：退款页面的逻辑错误和显示问题已全部解决，用户体验得到显著改善。
