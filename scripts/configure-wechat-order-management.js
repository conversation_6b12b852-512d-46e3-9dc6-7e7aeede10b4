/**
 * 微信小程序订单管理配置脚本
 * 
 * 使用说明：
 * 1. 这个脚本需要在后端服务器上运行
 * 2. 需要有效的 access_token
 * 3. 配置成功后，小程序支付接口将恢复正常
 */

const https = require('https');
const querystring = require('querystring');

// 配置信息
const CONFIG = {
  // 小程序的 AppID
  appId: 'wx5fc510f8990d896e', // 从错误信息中获取的AppID
  
  // 小程序的 AppSecret（需要从微信公众平台获取）
  appSecret: 'YOUR_APP_SECRET', // 请替换为实际的AppSecret
  
  // 订单详情页面路径（必须包含 ${商品订单号} 占位符）
  orderDetailPath: 'pages/orders/orders?orderNo=${商品订单号}&from=wechat'
};

/**
 * 获取 access_token
 */
async function getAccessToken() {
  return new Promise((resolve, reject) => {
    const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${CONFIG.appId}&secret=${CONFIG.appSecret}`;
    
    https.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (result.access_token) {
            console.log('✅ 获取 access_token 成功');
            resolve(result.access_token);
          } else {
            console.error('❌ 获取 access_token 失败:', result);
            reject(new Error(result.errmsg || '获取access_token失败'));
          }
        } catch (error) {
          reject(error);
        }
      });
    }).on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * 配置订单详情路径
 */
async function configureOrderDetailPath(accessToken) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      path: CONFIG.orderDetailPath
    });
    
    const options = {
      hostname: 'api.weixin.qq.com',
      port: 443,
      path: `/wxa/sec/order/update_order_detail_path?access_token=${accessToken}`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (result.errcode === 0) {
            console.log('✅ 配置订单详情路径成功');
            resolve(result);
          } else {
            console.error('❌ 配置订单详情路径失败:', result);
            reject(new Error(result.errmsg || '配置失败'));
          }
        } catch (error) {
          reject(error);
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.write(postData);
    req.end();
  });
}

/**
 * 查询当前配置的订单详情路径
 */
async function getOrderDetailPath(accessToken) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({});
    
    const options = {
      hostname: 'api.weixin.qq.com',
      port: 443,
      path: `/wxa/sec/order/get_order_detail_path?access_token=${accessToken}`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (result.errcode === 0) {
            console.log('✅ 查询订单详情路径成功:', result.path || '未配置');
            resolve(result);
          } else {
            console.error('❌ 查询订单详情路径失败:', result);
            reject(new Error(result.errmsg || '查询失败'));
          }
        } catch (error) {
          reject(error);
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.write(postData);
    req.end();
  });
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🚀 开始配置微信小程序订单管理...');
    console.log('📱 小程序 AppID:', CONFIG.appId);
    console.log('📄 订单详情路径:', CONFIG.orderDetailPath);
    console.log('');
    
    // 检查配置
    if (CONFIG.appSecret === 'YOUR_APP_SECRET') {
      console.error('❌ 请先配置正确的 AppSecret');
      console.log('💡 获取方式：微信公众平台 -> 开发 -> 开发设置 -> 开发者密码');
      return;
    }
    
    // 1. 获取 access_token
    console.log('1️⃣ 获取 access_token...');
    const accessToken = await getAccessToken();
    
    // 2. 查询当前配置
    console.log('2️⃣ 查询当前配置...');
    await getOrderDetailPath(accessToken);
    
    // 3. 配置订单详情路径
    console.log('3️⃣ 配置订单详情路径...');
    await configureOrderDetailPath(accessToken);
    
    // 4. 验证配置结果
    console.log('4️⃣ 验证配置结果...');
    await getOrderDetailPath(accessToken);
    
    console.log('');
    console.log('🎉 微信小程序订单管理配置完成！');
    console.log('');
    console.log('📋 接下来的步骤：');
    console.log('1. 等待微信审核（通常几分钟到几小时）');
    console.log('2. 审核通过后，支付接口将恢复正常');
    console.log('3. 用户可以在"我-订单与卡包-小程序购物订单"中查看订单');
    console.log('4. 点击订单会跳转到你的小程序订单页面');
    
  } catch (error) {
    console.error('❌ 配置失败:', error.message);
    console.log('');
    console.log('🔧 常见问题解决：');
    console.log('1. 检查 AppSecret 是否正确');
    console.log('2. 确认小程序已发布（开发版无法配置）');
    console.log('3. 检查网络连接');
    console.log('4. 联系微信客服');
  }
}

// 运行脚本
main();

module.exports = {
  getAccessToken,
  configureOrderDetailPath,
  getOrderDetailPath,
  CONFIG
};
