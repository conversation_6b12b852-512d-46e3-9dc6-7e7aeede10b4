#!/usr/bin/env node

/**
 * 微信支付问题快速修复脚本
 * 
 * 使用方法：
 * 1. 替换下面的 APP_SECRET
 * 2. 运行：node scripts/quick-fix.js
 */

const https = require('https');

// 🔧 配置区域 - 请修改这里
const APP_ID = 'wx5fc510f8990d896e';  // 从错误信息中获取
const APP_SECRET = '';  // ⚠️ 请替换为真实的AppSecret

// 订单详情页面路径（不要修改，除非你的页面路径不同）
const ORDER_PATH = 'pages/orders/orders?orderNo=${商品订单号}&from=wechat';

console.log('🚀 微信支付问题快速修复工具');
console.log('=====================================');
console.log('');

// 检查配置
if (APP_SECRET === 'YOUR_APP_SECRET') {
  console.error('❌ 错误：请先配置 APP_SECRET');
  console.log('');
  console.log('📋 获取步骤：');
  console.log('1. 登录微信公众平台：https://mp.weixin.qq.com');
  console.log('2. 进入：开发 → 开发设置');
  console.log('3. 复制：开发者密码(AppSecret)');
  console.log('4. 替换脚本中的 YOUR_APP_SECRET');
  console.log('');
  process.exit(1);
}

/**
 * HTTP请求封装
 */
function request(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve(result);
        } catch (error) {
          reject(new Error('解析响应失败: ' + responseData));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * 主修复流程
 */
async function quickFix() {
  try {
    console.log('1️⃣ 获取访问令牌...');
    
    // 获取 access_token
    const tokenUrl = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${APP_ID}&secret=${APP_SECRET}`;
    const tokenResult = await request(tokenUrl);
    
    if (!tokenResult.access_token) {
      throw new Error(`获取access_token失败: ${tokenResult.errmsg || '未知错误'}`);
    }
    
    console.log('✅ 访问令牌获取成功');
    
    console.log('2️⃣ 配置订单管理路径...');
    
    // 配置订单详情路径
    const configUrl = `https://api.weixin.qq.com/wxa/sec/order/update_order_detail_path?access_token=${tokenResult.access_token}`;
    const configResult = await request(configUrl, 'POST', { path: ORDER_PATH });
    
    if (configResult.errcode !== 0) {
      throw new Error(`配置失败: ${configResult.errmsg || '未知错误'}`);
    }
    
    console.log('✅ 订单管理路径配置成功');
    
    console.log('3️⃣ 验证配置结果...');
    
    // 验证配置
    const verifyUrl = `https://api.weixin.qq.com/wxa/sec/order/get_order_detail_path?access_token=${tokenResult.access_token}`;
    const verifyResult = await request(verifyUrl, 'POST', {});
    
    if (verifyResult.errcode !== 0) {
      throw new Error(`验证失败: ${verifyResult.errmsg || '未知错误'}`);
    }
    
    console.log('✅ 配置验证成功');
    console.log('📄 当前路径:', verifyResult.path || '未配置');
    
    console.log('');
    console.log('🎉 修复完成！');
    console.log('=====================================');
    console.log('');
    console.log('📋 接下来：');
    console.log('1. ⏳ 等待微信审核（几分钟到几小时）');
    console.log('2. ✅ 审核通过后支付接口恢复正常');
    console.log('3. 🛒 用户可在微信中查看小程序订单');
    console.log('4. 🔗 点击订单可跳转回小程序');
    console.log('');
    console.log('🧪 测试方法：');
    console.log('1. 在小程序中完成一笔支付');
    console.log('2. 微信 → 我 → 订单与卡包 → 小程序购物订单');
    console.log('3. 查看订单并测试跳转功能');
    
  } catch (error) {
    console.error('❌ 修复失败:', error.message);
    console.log('');
    console.log('🔧 可能的解决方案：');
    console.log('1. 检查 APP_SECRET 是否正确');
    console.log('2. 确认小程序已发布（开发版无法配置）');
    console.log('3. 检查网络连接是否正常');
    console.log('4. 稍后重试或联系微信客服');
    console.log('');
    console.log('📞 微信客服：https://developers.weixin.qq.com/community/');
  }
}

// 运行修复
quickFix();
