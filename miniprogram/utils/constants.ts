// utils/constants.ts
// 全局常量定义，统一管理项目中使用的常量字符串

/**
 * HTTP 请求头常量
 * 与后端 GlobalCommonConfig 保持一致
 */
export const HTTP_HEADERS = {
  /**
   * 会话ID请求头名称
   * 用于用户身份验证
   */
  SESSION_ID: 'X-Session-Id',

  /**
   * 会话过期时间请求头名称
   * 用于标识会话的过期时间戳
   */
  SESSION_EXPIRE: 'X-Session-Expire',

  /**
   * 应用ID请求头名称
   * 用于标识请求来源的小程序AppId
   */
  APP_ID: 'X-App-Id',

  /**
   * 租户ID请求头名称
   * 用于多租户系统的租户标识
   */
  TENANT_ID: 'X-tenant-Id'
} as const

// 注意：存储键名常量已在 config.ts 的 STORAGE_KEYS 中定义，避免重复定义

/**
 * API 相关常量
 */
export const API_CONSTANTS = {
  /**
   * 默认租户ID
   */
  DEFAULT_TENANT_ID: '1',

  /**
   * API 路径前缀
   */
  API_PREFIX: '/api'
} as const

/**
 * 登录相关常量
 */
export const LOGIN_CONSTANTS = {
  /**
   * 登录过期错误码
   */
  LOGIN_EXPIRED_CODE: 'LOGIN_EXPIRED',

  /**
   * 未登录错误码
   */
  NOT_LOGIN_CODE: 'NOT_LOGIN'
} as const






