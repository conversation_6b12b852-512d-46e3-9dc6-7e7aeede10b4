// utils/request.ts
import AuthManager from './auth'
import CONFIG from './config'
import LoginManager from './loginManager'
import { HTTP_HEADERS, API_CONSTANTS } from './constants'

interface RequestOptions {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: Record<string, string>
}

class RequestManager {
  private baseUrl: string

  constructor() {
    this.baseUrl = CONFIG.API_BASE_URL
  }

  /**
   * 发送请求
   */
  request(options: RequestOptions): Promise<any> {
    // 设置默认请求头
    options.header = {
      'Content-Type': 'application/json',
      ...options.header
    }

    // 添加sessionId和appId到请求头
    const sessionId = AuthManager.getToken()
    const appId = AuthManager.getAppId()

    // 需要登录态的接口添加会话信息
    if (sessionId) {
      options.header[HTTP_HEADERS.SESSION_ID] = sessionId
    }
    // 添加登录态过期时间
    if (sessionId) {
      options.header[HTTP_HEADERS.SESSION_EXPIRE] = (Date.now() + CONFIG.SESSION.TIMEOUT * 1000).toString()
    }

    if (appId) {
      options.header[HTTP_HEADERS.APP_ID] = appId
    }

    // 添加基础URL
    if (!options.url.startsWith('http')) {
      options.url = this.baseUrl + options.url
    }

    // 为所有 /api 请求添加租户ID请求头
    if (options.url.includes(API_CONSTANTS.API_PREFIX)) {
      options.header[HTTP_HEADERS.TENANT_ID] = API_CONSTANTS.DEFAULT_TENANT_ID
    }

    return new Promise((resolve, reject) => {
      wx.request({
        url: options.url,
        method: options.method || 'GET',
        data: options.data,
        header: options.header,
        success: async (res) => {
          // 处理HTTP状态码
          if (res.statusCode !== 200) {
            // 处理401未授权
            if (res.statusCode === 401) {
              const loginSuccess = await LoginManager.handleLoginExpired()
              if (loginSuccess) {
                // 登录成功后重新发起请求
                try {
                  const retryResult = await this.request(options)
                  resolve(retryResult)
                } catch (retryError) {
                  reject(retryError)
                }
              } else {
                reject(new Error('登录已过期，请重新登录'))
              }
              return
            }

            reject(new Error(`请求失败: ${res.statusCode}`))
            return
          }

          // 处理业务层面的登录态失效
          if (res.data && LoginManager.isLoginExpired(res.data)) {
            const loginSuccess = await LoginManager.handleLoginExpired(res.data as any)
            if (loginSuccess) {
              // 登录成功后重新发起请求
              try {
                const retryResult = await this.request(options)
                resolve(retryResult)
              } catch (retryError) {
                reject(retryError)
              }
            } else {
              const errorMessage = (res.data as any)?.message || '登录已过期，请重新登录'
              reject(new Error(errorMessage))
            }
            return
          }

          // 正常响应
          resolve(res.data)
        },
        fail: (error) => {
          console.error('请求失败:', error)
          reject(error)
        }
      })
    })
  }

  /**
   * GET请求
   */
  get(url: string, data?: any, header?: Record<string, string>): Promise<any> {
    return this.request({
      url,
      method: 'GET',
      data,
      header
    })
  }

  /**
   * POST请求
   */
  post(url: string, data?: any, header?: Record<string, string>): Promise<any> {
    return this.request({
      url,
      method: 'POST',
      data,
      header
    })
  }

  /**
   * PUT请求
   */
  put(url: string, data?: any, header?: Record<string, string>): Promise<any> {
    return this.request({
      url,
      method: 'PUT',
      data,
      header
    })
  }

  /**
   * DELETE请求
   */
  delete(url: string, data?: any, header?: Record<string, string>): Promise<any> {
    return this.request({
      url,
      method: 'DELETE',
      data,
      header
    })
  }


}

export default new RequestManager()
