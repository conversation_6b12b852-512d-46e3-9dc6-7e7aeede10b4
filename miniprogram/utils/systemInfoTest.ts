// utils/systemInfoTest.ts
// 系统信息工具类测试文件

import SystemInfoManager from './systemInfo'

/**
 * 测试系统信息工具类
 */
export class SystemInfoTest {
  /**
   * 测试新API支持情况
   */
  static testAPISupport() {
    console.log('=== 测试新API支持情况 ===')
    const support = SystemInfoManager.checkNewAPISupport()
    console.log('API支持情况:', support)
    
    // 检查是否所有新API都支持
    const allSupported = Object.values(support).every(Boolean)
    console.log('所有新API都支持:', allSupported)
    
    if (!allSupported) {
      console.warn('部分新API不支持，将使用降级方案')
    }
  }

  /**
   * 测试系统信息获取
   */
  static testSystemInfo() {
    console.log('=== 测试系统信息获取 ===')
    
    try {
      const systemInfo = SystemInfoManager.getSystemInfo()
      console.log('系统信息获取成功:', {
        SDKVersion: systemInfo.SDKVersion,
        platform: systemInfo.platform,
        brand: systemInfo.brand,
        model: systemInfo.model,
        windowWidth: systemInfo.windowWidth,
        windowHeight: systemInfo.windowHeight,
        safeArea: systemInfo.safeArea
      })
      
      return true
    } catch (error) {
      console.error('系统信息获取失败:', error)
      return false
    }
  }

  /**
   * 测试窗口信息获取
   */
  static testWindowInfo() {
    console.log('=== 测试窗口信息获取 ===')
    
    try {
      const windowInfo = SystemInfoManager.getWindowInfo()
      console.log('窗口信息获取成功:', {
        screenWidth: windowInfo.screenWidth,
        screenHeight: windowInfo.screenHeight,
        windowWidth: windowInfo.windowWidth,
        windowHeight: windowInfo.windowHeight,
        statusBarHeight: windowInfo.statusBarHeight,
        safeArea: windowInfo.safeArea
      })
      
      return true
    } catch (error) {
      console.error('窗口信息获取失败:', error)
      return false
    }
  }

  /**
   * 测试应用基础信息获取
   */
  static testAppBaseInfo() {
    console.log('=== 测试应用基础信息获取 ===')
    
    try {
      const appBaseInfo = SystemInfoManager.getAppBaseInfo()
      console.log('应用基础信息获取成功:', {
        SDKVersion: appBaseInfo.SDKVersion,
        version: appBaseInfo.version,
        language: appBaseInfo.language,
        theme: appBaseInfo.theme
      })
      
      return true
    } catch (error) {
      console.error('应用基础信息获取失败:', error)
      return false
    }
  }

  /**
   * 测试设备信息获取
   */
  static testDeviceInfo() {
    console.log('=== 测试设备信息获取 ===')
    
    try {
      const deviceInfo = SystemInfoManager.getDeviceInfo()
      console.log('设备信息获取成功:', {
        brand: deviceInfo.brand,
        model: deviceInfo.model,
        system: deviceInfo.system,
        platform: deviceInfo.platform,
        benchmarkLevel: deviceInfo.benchmarkLevel
      })
      
      return true
    } catch (error) {
      console.error('设备信息获取失败:', error)
      return false
    }
  }

  /**
   * 测试版本比较功能
   */
  static testVersionComparison() {
    console.log('=== 测试版本比较功能 ===')
    
    const testCases = [
      { v1: '2.21.2', v2: '2.21.1', expected: 1 },
      { v1: '2.21.1', v2: '2.21.2', expected: -1 },
      { v1: '2.21.2', v2: '2.21.2', expected: 0 },
      { v1: '3.0.0', v2: '2.21.2', expected: 1 },
      { v1: '2.20.1', v2: '2.21.2', expected: -1 }
    ]
    
    let allPassed = true
    
    testCases.forEach(({ v1, v2, expected }, index) => {
      const result = SystemInfoManager.compareVersion(v1, v2)
      const passed = result === expected
      
      console.log(`测试用例 ${index + 1}: ${v1} vs ${v2} = ${result} (期望: ${expected}) ${passed ? '✓' : '✗'}`)
      
      if (!passed) {
        allPassed = false
      }
    })
    
    console.log('版本比较测试结果:', allPassed ? '全部通过' : '存在失败')
    return allPassed
  }

  /**
   * 测试缓存功能
   */
  static testCache() {
    console.log('=== 测试缓存功能 ===')
    
    // 清除缓存
    SystemInfoManager.clearCache()
    console.log('缓存已清除')
    
    // 第一次获取（应该调用API）
    const start1 = Date.now()
    const info1 = SystemInfoManager.getSystemInfo()
    const time1 = Date.now() - start1
    console.log('第一次获取耗时:', time1, 'ms')
    
    // 第二次获取（应该使用缓存）
    const start2 = Date.now()
    const info2 = SystemInfoManager.getSystemInfo()
    const time2 = Date.now() - start2
    console.log('第二次获取耗时:', time2, 'ms')
    
    // 验证缓存是否生效
    const cacheWorking = time2 < time1 && JSON.stringify(info1) === JSON.stringify(info2)
    console.log('缓存功能:', cacheWorking ? '正常' : '异常')
    
    return cacheWorking
  }

  /**
   * 运行所有测试
   */
  static runAllTests() {
    console.log('🚀 开始运行系统信息工具类测试')
    console.log('=====================================')
    
    const results = {
      apiSupport: this.testAPISupport(),
      systemInfo: this.testSystemInfo(),
      windowInfo: this.testWindowInfo(),
      appBaseInfo: this.testAppBaseInfo(),
      deviceInfo: this.testDeviceInfo(),
      versionComparison: this.testVersionComparison(),
      cache: this.testCache()
    }
    
    console.log('=====================================')
    console.log('📊 测试结果汇总:')
    
    Object.entries(results).forEach(([test, result]) => {
      console.log(`${test}: ${result ? '✓ 通过' : '✗ 失败'}`)
    })
    
    const allPassed = Object.values(results).every(Boolean)
    console.log('=====================================')
    console.log(`🎯 总体结果: ${allPassed ? '✅ 全部通过' : '❌ 存在失败'}`)
    
    if (allPassed) {
      console.log('🎉 恭喜！wx.getSystemInfoSync 替换成功，所有功能正常！')
    } else {
      console.log('⚠️  部分功能存在问题，请检查具体错误信息')
    }
    
    return allPassed
  }
}

// 导出测试类
export default SystemInfoTest
