// utils/orderManagement.ts
// 微信小程序订单管理配置工具

import { CONFIG } from './config'

/**
 * 微信小程序订单管理配置类
 */
class OrderManagementConfig {
  
  /**
   * 配置订单详情路径
   * 这个方法需要在后端调用，前端无法直接调用微信API
   */
  static getOrderDetailPath(): string {
    // 根据你的小程序结构，订单详情页面路径
    // 注意：${商品订单号} 是微信要求的占位符，不要修改
    return 'pages/orders/orders?orderNo=${商品订单号}&from=wechat'
  }

  /**
   * 处理从微信订单管理跳转过来的订单查看
   * @param options 页面参数
   */
  static handleWechatOrderJump(options: any) {
    const { orderNo, from } = options
    
    if (from === 'wechat' && orderNo) {
      console.log('从微信订单管理跳转，订单号:', orderNo)
      
      // 可以在这里添加特殊处理逻辑
      // 比如显示特殊提示、统计等
      
      return {
        isFromWechat: true,
        orderNo: orderNo
      }
    }
    
    return {
      isFromWechat: false,
      orderNo: null
    }
  }

  /**
   * 获取后端配置订单管理的API调用信息
   */
  static getConfigApiInfo() {
    return {
      url: `${CONFIG.API_BASE_URL}/api/admin/wechat/order-management/config`,
      method: 'POST',
      data: {
        path: this.getOrderDetailPath()
      },
      description: '配置微信小程序订单管理路径'
    }
  }
}

export default OrderManagementConfig
