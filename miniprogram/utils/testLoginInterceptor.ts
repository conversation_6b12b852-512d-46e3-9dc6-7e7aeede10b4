// utils/testLoginInterceptor.ts
// 测试登录拦截功能的工具

import RequestManager from './request'
import LoginManager from './loginManager'
import { LOGIN_CONSTANTS } from './constants'

/**
 * 测试登录拦截功能
 */
export class LoginInterceptorTester {
  
  /**
   * 模拟服务端返回登录过期的响应
   */
  static mockLoginExpiredResponse() {
    return {
      code: LOGIN_CONSTANTS.LOGIN_EXPIRED_CODE,
      message: '登录态已过期，请重新登录',
      data: null,
      timestamp: Date.now(),
      success: false
    }
  }

  /**
   * 模拟服务端返回未登录的响应
   */
  static mockNotLoginResponse() {
    return {
      code: LOGIN_CONSTANTS.NOT_LOGIN_CODE,
      message: '用户未登录，请先登录',
      data: null,
      timestamp: Date.now(),
      success: false
    }
  }

  /**
   * 测试登录过期拦截
   */
  static async testLoginExpiredInterception() {
    console.log('开始测试登录过期拦截...')
    
    const mockResponse = this.mockLoginExpiredResponse()
    const isExpired = LoginManager.isLoginExpired(mockResponse)
    
    console.log('登录过期检测结果:', isExpired)
    
    if (isExpired) {
      console.log('检测到登录过期，触发拦截处理...')
      const result = await LoginManager.handleLoginExpired(mockResponse)
      console.log('拦截处理结果:', result)
      return result
    }
    
    return false
  }

  /**
   * 测试未登录拦截
   */
  static async testNotLoginInterception() {
    console.log('开始测试未登录拦截...')
    
    const mockResponse = this.mockNotLoginResponse()
    const isExpired = LoginManager.isLoginExpired(mockResponse)
    
    console.log('未登录检测结果:', isExpired)
    
    if (isExpired) {
      console.log('检测到未登录，触发拦截处理...')
      const result = await LoginManager.handleLoginExpired(mockResponse)
      console.log('拦截处理结果:', result)
      return result
    }
    
    return false
  }

  /**
   * 测试正常响应不被拦截
   */
  static testNormalResponse() {
    console.log('开始测试正常响应...')
    
    const normalResponse = {
      code: 'SUCCESS',
      message: '请求成功',
      data: { test: 'data' },
      timestamp: Date.now(),
      success: true
    }
    
    const isExpired = LoginManager.isLoginExpired(normalResponse)
    console.log('正常响应检测结果:', isExpired)
    
    return !isExpired
  }

  /**
   * 运行所有测试
   */
  static async runAllTests() {
    console.log('=== 开始登录拦截功能测试 ===')
    
    try {
      // 测试正常响应
      const normalTest = this.testNormalResponse()
      console.log('正常响应测试:', normalTest ? '通过' : '失败')
      
      // 测试登录过期拦截
      console.log('\n--- 测试登录过期拦截 ---')
      await this.testLoginExpiredInterception()
      
      // 测试未登录拦截
      console.log('\n--- 测试未登录拦截 ---')
      await this.testNotLoginInterception()
      
      console.log('\n=== 登录拦截功能测试完成 ===')
      
    } catch (error) {
      console.error('测试过程中出现错误:', error)
    }
  }

  /**
   * 重置登录管理器状态（用于测试）
   */
  static resetLoginManager() {
    LoginManager.reset()
    console.log('登录管理器状态已重置')
  }
}

export default LoginInterceptorTester
