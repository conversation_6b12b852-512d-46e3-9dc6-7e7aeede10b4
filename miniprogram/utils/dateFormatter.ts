// utils/dateFormatter.ts
// 时间格式化工具类

export class DateFormatter {
  /**
   * 格式化日期时间为 YYYY-MM-DD HH:mm
   * @param dateStr ISO格式的日期字符串
   * @returns 格式化后的日期时间字符串
   */
  static formatDateTime(dateStr: string): string {
    if (!dateStr) return ''
    console.warn('开始转换时间:', dateStr)

    try {
      const date = new Date(dateStr)
      
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('无效的日期格式:', dateStr)
        return dateStr
      }
      
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hour}:${minute}`
    } catch (error) {
      console.error('日期格式化失败:', error, dateStr)
      return dateStr
    }
  }

  /**
   * 格式化日期为 YYYY-MM-DD
   * @param dateStr ISO格式的日期字符串
   * @returns 格式化后的日期字符串
   */
  static formatDate(dateStr: string): string {
    if (!dateStr) return ''
    
    try {
      const date = new Date(dateStr)
      
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('无效的日期格式:', dateStr)
        return dateStr
      }
      
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      
      return `${year}-${month}-${day}`
    } catch (error) {
      console.error('日期格式化失败:', error, dateStr)
      return dateStr
    }
  }

  /**
   * 格式化时间为 HH:mm
   * @param dateStr ISO格式的日期字符串
   * @returns 格式化后的时间字符串
   */
  static formatTime(dateStr: string): string {
    if (!dateStr) return ''
    
    try {
      const date = new Date(dateStr)
      
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('无效的日期格式:', dateStr)
        return dateStr
      }
      
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')
      
      return `${hour}:${minute}`
    } catch (error) {
      console.error('时间格式化失败:', error, dateStr)
      return dateStr
    }
  }

  /**
   * 格式化相对时间（如：刚刚、5分钟前、1小时前等）
   * @param dateStr ISO格式的日期字符串
   * @returns 相对时间字符串
   */
  static formatRelativeTime(dateStr: string): string {
    if (!dateStr) return ''
    
    try {
      const date = new Date(dateStr)
      const now = new Date()
      
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('无效的日期格式:', dateStr)
        return dateStr
      }
      
      const diffMs = now.getTime() - date.getTime()
      const diffMinutes = Math.floor(diffMs / (1000 * 60))
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
      
      if (diffMinutes < 1) {
        return '刚刚'
      } else if (diffMinutes < 60) {
        return `${diffMinutes}分钟前`
      } else if (diffHours < 24) {
        return `${diffHours}小时前`
      } else if (diffDays < 7) {
        return `${diffDays}天前`
      } else {
        // 超过7天显示具体日期
        return this.formatDate(dateStr)
      }
    } catch (error) {
      console.error('相对时间格式化失败:', error, dateStr)
      return dateStr
    }
  }

  /**
   * 格式化为中文日期格式（如：2025年8月4日）
   * @param dateStr ISO格式的日期字符串
   * @returns 中文格式的日期字符串
   */
  static formatChineseDate(dateStr: string): string {
    if (!dateStr) return ''
    
    try {
      const date = new Date(dateStr)
      
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('无效的日期格式:', dateStr)
        return dateStr
      }
      
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      
      return `${year}年${month}月${day}日`
    } catch (error) {
      console.error('中文日期格式化失败:', error, dateStr)
      return dateStr
    }
  }

  /**
   * 格式化为完整的中文日期时间格式
   * @param dateStr ISO格式的日期字符串
   * @returns 中文格式的日期时间字符串
   */
  static formatChineseDateTime(dateStr: string): string {
    if (!dateStr) return ''
    
    try {
      const date = new Date(dateStr)
      
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('无效的日期格式:', dateStr)
        return dateStr
      }
      
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const hour = date.getHours()
      const minute = date.getMinutes()
      
      return `${year}年${month}月${day}日 ${hour}:${String(minute).padStart(2, '0')}`
    } catch (error) {
      console.error('中文日期时间格式化失败:', error, dateStr)
      return dateStr
    }
  }
}

export default DateFormatter
