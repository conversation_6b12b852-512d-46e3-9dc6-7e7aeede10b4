// utils/loginManager.ts
// 全局登录状态管理器，处理登录态失效的统一拦截

import AuthManager from './auth'
import { LOGIN_CONSTANTS } from './constants'

interface LoginExpiredResponse {
  code: string
  message: string
  data: any
  timestamp: number
  success: boolean
}

/**
 * 登录管理器
 * 负责处理登录态失效的统一拦截和处理
 */
class LoginManager {
  private isLoginModalShowing: boolean = false
  private pendingRequests: Array<() => void> = []

  /**
   * 检查响应是否为登录态失效
   */
  isLoginExpired(response: any): boolean {
    if (!response || typeof response !== 'object') {
      return false
    }

    const code = response.code
    return code === LOGIN_CONSTANTS.LOGIN_EXPIRED_CODE || code === LOGIN_CONSTANTS.NOT_LOGIN_CODE
  }

  /**
   * 处理登录态失效
   */
  async handleLoginExpired(response?: LoginExpiredResponse): Promise<boolean> {
    // 如果已经在显示登录弹窗，直接返回
    if (this.isLoginModalShowing) {
      return new Promise((resolve) => {
        this.pendingRequests.push(() => resolve(AuthManager.isLoggedIn()))
      })
    }

    this.isLoginModalShowing = true

    try {
      // 清除本地登录信息
      AuthManager.clearToken()

      // 显示登录弹窗
      const loginSuccess = await this.showLoginModal(response?.message)

      if (loginSuccess) {
        // 登录成功，处理所有待处理的请求
        this.resolvePendingRequests(true)
        
        // 刷新当前页面数据
        this.refreshCurrentPage()
        
        return true
      } else {
        // 用户取消登录
        this.resolvePendingRequests(false)
        return false
      }
    } finally {
      this.isLoginModalShowing = false
    }
  }

  /**
   * 显示登录弹窗
   */
  private showLoginModal(message?: string): Promise<boolean> {
    return new Promise((resolve) => {
      const defaultMessage = '登录态已过期，请重新登录'
      const displayMessage = message || defaultMessage

      wx.showModal({
        title: '登录提示',
        content: displayMessage,
        confirmText: '立即登录',
        cancelText: '稍后再说',
        success: async (res) => {
          if (res.confirm) {
            // 用户点击立即登录
            try {
              const result = await AuthManager.wechatLogin()
              resolve(result.success)
            } catch (error) {
              console.error('登录失败:', error)
              resolve(false)
            }
          } else {
            // 用户取消登录
            resolve(false)
          }
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  }

  /**
   * 处理所有待处理的请求
   */
  private resolvePendingRequests(success: boolean): void {
    const requests = [...this.pendingRequests]
    this.pendingRequests = []
    
    requests.forEach(resolve => {
      resolve()
    })
  }

  /**
   * 刷新当前页面数据
   */
  private refreshCurrentPage(): void {
    try {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        
        // 如果页面有 onShow 方法，调用它来刷新页面
        if (typeof currentPage.onShow === 'function') {
          currentPage.onShow()
        }
        
        // 如果页面有自定义的刷新方法，调用它
        if (typeof (currentPage as any).refreshData === 'function') {
          (currentPage as any).refreshData()
        }
        
        // 如果页面有 refreshLoginStatus 方法，调用它
        if (typeof (currentPage as any).refreshLoginStatus === 'function') {
          (currentPage as any).refreshLoginStatus()
        }
      }
    } catch (error) {
      console.error('刷新页面失败:', error)
    }
  }

  /**
   * 跳转到登录页面（备用方案）
   */
  navigateToLogin(): void {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentRoute = currentPage ? currentPage.route : ''

    wx.navigateTo({
      url: `/pages/login/login?from=${encodeURIComponent(currentRoute)}`,
      fail: (error) => {
        console.error('跳转登录页面失败:', error)
        // 如果跳转失败，回退到显示 Toast
        wx.showToast({
          title: '请重新登录',
          icon: 'none',
          duration: 2000
        })
      }
    })
  }

  /**
   * 重置状态（用于测试或特殊情况）
   */
  reset(): void {
    this.isLoginModalShowing = false
    this.pendingRequests = []
  }
}

export default new LoginManager()
