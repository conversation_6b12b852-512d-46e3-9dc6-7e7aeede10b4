// utils/avatarManager.ts
// 头像本地存储管理器

import AuthManager from './auth'
import { CONFIG } from './config'

interface AvatarInfo {
  localPath: string      // 本地存储路径
  originalUrl: string    // 原始临时URL
  downloadTime: number   // 下载时间戳
  fileSize: number       // 文件大小
  userId?: string        // 用户ID（可选）
  isGlobal?: boolean     // 是否为全局头像（不随用户登录状态变化）
}

/**
 * 头像本地存储管理器
 * 负责将临时头像链接下载并存储到本地，提供本地头像的管理功能
 */
class AvatarManager {
  private readonly STORAGE_KEY = CONFIG.STORAGE_KEYS.USER_AVATAR_INFO
  private readonly GLOBAL_AVATAR_KEY = CONFIG.STORAGE_KEYS.GLOBAL_AVATAR_INFO // 全局头像信息，不随登录状态清除
  private readonly AVATAR_DIR = 'avatars'
  private readonly MAX_CACHE_SIZE = 50 * 1024 * 1024 // 50MB 最大缓存大小
  private readonly CACHE_EXPIRE_TIME = 90 * 24 * 60 * 60 * 1000 // 90天过期时间（延长保存时间）

  /**
   * 下载并保存头像到本地
   * @param tempAvatarUrl 临时头像URL
   * @returns Promise<string> 本地头像路径
   */
  async downloadAndSaveAvatar(tempAvatarUrl: string): Promise<string> {
    try {
      console.log('开始下载头像:', tempAvatarUrl)

      // 显示下载进度
      wx.showLoading({
        title: '保存头像中...',
        mask: true
      })

      // 1. 下载临时文件
      const downloadResult = await this.downloadTempFile(tempAvatarUrl)
      console.log('头像下载完成:', downloadResult)

      // 2. 获取文件信息
      const fileInfo = await this.getFileInfo(downloadResult.tempFilePath)
      console.log('头像文件信息:', fileInfo)

      // 3. 生成本地存储路径
      const localPath = this.generateLocalPath(fileInfo.size)

      // 4. 保存到本地存储
      const savedPath = await this.saveToLocalStorage(downloadResult.tempFilePath, localPath)
      console.log('头像保存到本地:', savedPath)

      // 5. 更新头像信息记录
      const avatarInfo: AvatarInfo = {
        localPath: savedPath,
        originalUrl: tempAvatarUrl,
        downloadTime: Date.now(),
        fileSize: fileInfo.size,
        userId: this.getCurrentUserId(),
        isGlobal: true // 标记为全局头像，不随登录状态清除
      }

      await this.saveAvatarInfo(avatarInfo)

      // 6. 清理过期的头像缓存
      this.cleanExpiredAvatars()

      wx.hideLoading()
      
      return savedPath

    } catch (error) {
      wx.hideLoading()
      console.error('下载保存头像失败:', error)
      
      wx.showToast({
        title: '头像保存失败',
        icon: 'error',
        duration: 2000
      })
      
      throw error
    }
  }

  /**
   * 获取本地头像路径
   * @returns string | null 本地头像路径，如果没有则返回null
   */
  getLocalAvatarPath(): string | null {
    try {
      // 优先获取全局头像信息
      let avatarInfo = this.getGlobalAvatarInfo()

      // 如果没有全局头像，尝试获取用户头像
      if (!avatarInfo) {
        avatarInfo = this.getAvatarInfo()
      }

      if (!avatarInfo) {
        return null
      }

      // 检查文件是否还存在
      if (this.isFileExists(avatarInfo.localPath)) {
        // 检查是否过期
        if (this.isAvatarExpired(avatarInfo)) {
          console.log('本地头像已过期，需要重新下载')
          this.removeAvatarInfo()
          this.removeGlobalAvatarInfo()
          return null
        }

        console.log('使用本地头像:', avatarInfo.localPath)
        return avatarInfo.localPath
      } else {
        console.log('本地头像文件不存在，清理记录')
        this.removeAvatarInfo()
        this.removeGlobalAvatarInfo()
        return null
      }
    } catch (error) {
      console.error('获取本地头像路径失败:', error)
      return null
    }
  }

  /**
   * 清理本地头像缓存
   */
  async clearAvatarCache(): Promise<void> {
    try {
      const avatarInfo = this.getAvatarInfo()
      
      if (avatarInfo && avatarInfo.localPath) {
        // 删除本地文件
        await this.removeLocalFile(avatarInfo.localPath)
      }

      // 清理存储记录
      this.removeAvatarInfo()
      
      console.log('头像缓存清理完成')
    } catch (error) {
      console.error('清理头像缓存失败:', error)
    }
  }

  /**
   * 获取头像缓存信息
   */
  getAvatarCacheInfo(): AvatarInfo | null {
    // 优先返回全局头像信息
    const globalInfo = this.getGlobalAvatarInfo()
    if (globalInfo) {
      return globalInfo
    }

    return this.getAvatarInfo()
  }

  /**
   * 下载临时文件
   */
  private downloadTempFile(url: string): Promise<WechatMiniprogram.DownloadFileSuccessCallbackResult> {
    return new Promise((resolve, reject) => {
      wx.downloadFile({
        url: url,
        success: resolve,
        fail: reject
      })
    })
  }

  /**
   * 获取文件信息
   */
  private getFileInfo(filePath: string): Promise<WechatMiniprogram.GetFileInfoSuccessCallbackResult> {
    return new Promise((resolve, reject) => {
      wx.getFileInfo({
        filePath: filePath,
        success: resolve,
        fail: reject
      })
    })
  }

  /**
   * 生成本地存储路径
   */
  private generateLocalPath(fileSize: number): string {
    const timestamp = Date.now()
    const userId = this.getCurrentUserId() || 'anonymous'
    const fileName = `avatar_${userId}_${timestamp}.jpg`
    return `${wx.env.USER_DATA_PATH}/${this.AVATAR_DIR}/${fileName}`
  }

  /**
   * 保存文件到本地存储
   */
  private saveToLocalStorage(tempFilePath: string, localPath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      // 确保目录存在
      const dirPath = localPath.substring(0, localPath.lastIndexOf('/'))
      
      wx.getFileSystemManager().mkdir({
        dirPath: dirPath,
        recursive: true,
        success: () => {
          // 复制文件到本地存储
          wx.getFileSystemManager().copyFile({
            srcPath: tempFilePath,
            destPath: localPath,
            success: () => resolve(localPath),
            fail: reject
          })
        },
        fail: (error) => {
          // 目录可能已存在，直接尝试复制文件
          wx.getFileSystemManager().copyFile({
            srcPath: tempFilePath,
            destPath: localPath,
            success: () => resolve(localPath),
            fail: reject
          })
        }
      })
    })
  }

  /**
   * 保存头像信息到本地存储
   */
  private saveAvatarInfo(avatarInfo: AvatarInfo): void {
    try {
      // 保存到用户相关的存储
      wx.setStorageSync(this.STORAGE_KEY, avatarInfo)

      // 如果是全局头像，也保存到全局存储
      if (avatarInfo.isGlobal) {
        wx.setStorageSync(this.GLOBAL_AVATAR_KEY, avatarInfo)
      }
    } catch (error) {
      console.error('保存头像信息失败:', error)
    }
  }

  /**
   * 获取头像信息
   */
  private getAvatarInfo(): AvatarInfo | null {
    try {
      return wx.getStorageSync(this.STORAGE_KEY)
    } catch (error) {
      console.error('获取头像信息失败:', error)
      return null
    }
  }

  /**
   * 获取全局头像信息
   */
  private getGlobalAvatarInfo(): AvatarInfo | null {
    try {
      return wx.getStorageSync(this.GLOBAL_AVATAR_KEY)
    } catch (error) {
      console.error('获取全局头像信息失败:', error)
      return null
    }
  }

  /**
   * 删除头像信息记录
   */
  private removeAvatarInfo(): void {
    try {
      wx.removeStorageSync(this.STORAGE_KEY)
    } catch (error) {
      console.error('删除头像信息失败:', error)
    }
  }

  /**
   * 删除全局头像信息记录
   */
  private removeGlobalAvatarInfo(): void {
    try {
      wx.removeStorageSync(this.GLOBAL_AVATAR_KEY)
    } catch (error) {
      console.error('删除全局头像信息失败:', error)
    }
  }

  /**
   * 检查文件是否存在
   */
  private isFileExists(filePath: string): boolean {
    try {
      wx.getFileSystemManager().accessSync(filePath)
      return true
    } catch {
      return false
    }
  }

  /**
   * 检查头像是否过期
   */
  private isAvatarExpired(avatarInfo: AvatarInfo): boolean {
    const now = Date.now()
    return (now - avatarInfo.downloadTime) > this.CACHE_EXPIRE_TIME
  }

  /**
   * 删除本地文件
   */
  private removeLocalFile(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      wx.getFileSystemManager().unlink({
        filePath: filePath,
        success: () => resolve(),
        fail: reject
      })
    })
  }

  /**
   * 获取当前用户ID
   */
  private getCurrentUserId(): string | undefined {
    const userInfo = AuthManager.getUserInfo()
    return userInfo?.id?.toString()
  }

  /**
   * 清理过期的头像缓存
   */
  private async cleanExpiredAvatars(): Promise<void> {
    try {
      const avatarInfo = this.getAvatarInfo()
      
      if (avatarInfo && this.isAvatarExpired(avatarInfo)) {
        await this.clearAvatarCache()
        console.log('清理过期头像缓存完成')
      }
    } catch (error) {
      console.error('清理过期头像缓存失败:', error)
    }
  }
}

export default new AvatarManager()
