// utils/config.ts
// 配置文件，用于管理API地址等配置信息

export const CONFIG = {
  // API基础地址 - 请根据实际后端地址修改
  API_BASE_URL: 'http://localhost:8080',
  // API_BASE_URL: 'https://ss.seekeyes.cn',
  
  // 微信小程序配置
  WECHAT: {
    // 动态获取当前小程序的 AppId
    getAppId(): string {
      try {
        const accountInfo = wx.getAccountInfoSync()
        const appId = accountInfo.miniProgram.appId

        // 验证 AppId 格式
        if (appId && appId.startsWith('wx')) {
          return appId
        } else {
          console.warn('获取到的AppId格式异常:', appId)
          return ''
        }
      } catch (error) {
        console.error('获取AppId失败:', error)
        return '' // 返回空字符串，由调用方处理
      }
    },

    // 兼容原有的 APP_ID 属性访问方式
    get APP_ID(): string {
      return this.getAppId()
    }

    // APP_SECRET在前端不需要配置，由后端管理
  },
  
  // 存储键名
  STORAGE_KEYS: {
    USER_TOKEN: 'user_token',
    USER_INFO: 'user_info',
    LOGIN_TIME: 'login_time',
    MAIN_CATEGORY_TREE: 'main_category_tree',
    LAST_USER_AVATAR: 'last_user_avatar',
    LOGS: 'logs',
    HOMEPAGE_CONFIG: 'homepage-config',
    HOMEPAGE_CONFIG_EXPIRY: 'homepage-config-expiry',
    HOMEPAGE_CONFIGS: 'homepage-configs',
    USER_AVATAR_INFO: 'user_avatar_info',
    GLOBAL_AVATAR_INFO: 'global_avatar_info',
    CHECKOUT_SELECTED_SKU_IDS: 'checkout-selected-sku-ids',
    CART_SELECTED_MAP: 'cart-selected-map'
  },
  
  // 会话配置
  SESSION: {
    TIMEOUT: 7200, // 2小时，单位：秒
  }
}

export default CONFIG
