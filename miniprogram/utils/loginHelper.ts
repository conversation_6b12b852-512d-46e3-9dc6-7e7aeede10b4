// utils/loginHelper.ts
// 登录辅助工具，提供常用的登录相关功能

import AuthManager from './auth'

/**
 * 登录辅助工具类
 */
export class LoginHelper {
  /**
   * 检查登录状态，未登录则显示登录弹窗
   * @param showModal 是否显示登录提示弹窗
   * @returns 是否已登录
   */
  static checkLogin(showModal: boolean = true): boolean {
    if (AuthManager.isLoggedIn()) {
      return true
    }

    if (showModal) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      })
    }

    return false
  }

  /**
   * 要求登录后执行操作
   * @param callback 登录成功后执行的回调函数
   * @param errorCallback 登录失败或取消后执行的回调函数
   */
  static requireLogin(callback: () => void, errorCallback?: () => void): void {
    if (AuthManager.isLoggedIn()) {
      callback()
      return
    }

    wx.showToast({
      title: '请先登录后使用此功能',
      icon: 'none',
      duration: 2000
    })

    if (errorCallback) {
      errorCallback()
    }
  }

  /**
   * 静默检查登录状态（不显示弹窗）
   * @returns 是否已登录
   */
  static isLoggedIn(): boolean {
    return AuthManager.isLoggedIn()
  }

  /**
   * 获取当前用户信息
   * @returns 用户信息或null
   */
  static getCurrentUser(): any {
    return AuthManager.getUserInfo()
  }

  /**
   * 格式化用户显示信息
   * @param userInfo 用户信息
   * @returns 格式化后的显示信息
   */
  static formatUserDisplay(userInfo: any): { nickname: string; phone: string; avatar: string } {
    if (!userInfo) {
      return {
        nickname: '未登录用户',
        phone: '',
        avatar: '/images/default-avatar.png'
      }
    }

    return {
      nickname: userInfo.nickName || '用户昵称',
      phone: userInfo.phone ? this.maskPhone(userInfo.phone) : '138****8888',
      avatar: userInfo.avatarUrl || '/images/default-avatar.png'
    }
  }

  /**
   * 手机号脱敏处理
   * @param phone 手机号
   * @returns 脱敏后的手机号
   */
  private static maskPhone(phone: string): string {
    if (!phone || phone.length < 11) {
      return phone
    }
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }
}

export default LoginHelper
