// utils/systemInfo.ts
// 系统信息获取工具类，用于替代已废弃的 wx.getSystemInfoSync

/**
 * 系统信息接口，兼容原 wx.getSystemInfoSync 返回的数据结构
 */
export interface SystemInfo {
  // 窗口信息 (来自 wx.getWindowInfo)
  pixelRatio: number
  screenWidth: number
  screenHeight: number
  windowWidth: number
  windowHeight: number
  statusBarHeight: number
  safeArea?: {
    left: number
    right: number
    top: number
    bottom: number
    width: number
    height: number
  }

  // 应用基础信息 (来自 wx.getAppBaseInfo)
  SDKVersion: string
  version: string
  language: string
  theme?: 'light' | 'dark'
  host?: {
    appId: string
    enableDebug: boolean
  }

  // 设备信息 (来自 wx.getDeviceInfo)
  brand: string
  model: string
  system: string
  platform: string
  benchmarkLevel?: number
  abi?: string
  cpuType?: string
  memorySize?: string

  // 系统设置 (来自 wx.getSystemSetting)
  bluetoothEnabled?: boolean
  locationEnabled?: boolean
  wifiEnabled?: boolean
  deviceOrientation?: string

  // 授权设置 (来自 wx.getAppAuthorizeSetting)
  albumAuthorized?: boolean
  cameraAuthorized?: boolean
  locationAuthorized?: boolean
  microphoneAuthorized?: boolean
  notificationAuthorized?: boolean
  notificationAlertAuthorized?: boolean
  notificationBadgeAuthorized?: boolean
  notificationSoundAuthorized?: boolean
  phoneCalendarAuthorized?: boolean
  locationReducedAccuracy?: boolean

  // 兼容字段
  fontSizeSetting?: number
}

/**
 * 系统信息获取工具类
 * 用于替代已废弃的 wx.getSystemInfoSync API
 */
class SystemInfoManager {
  private cachedSystemInfo: SystemInfo | null = null
  private cacheTime: number = 0
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  /**
   * 获取完整的系统信息
   * @param useCache 是否使用缓存，默认true
   * @returns 系统信息对象
   */
  getSystemInfo(useCache: boolean = true): SystemInfo {
    // 检查缓存
    if (useCache && this.cachedSystemInfo && (Date.now() - this.cacheTime) < this.CACHE_DURATION) {
      return this.cachedSystemInfo
    }

    try {
      const systemInfo: SystemInfo = {
        // 默认值
        pixelRatio: 1,
        screenWidth: 375,
        screenHeight: 667,
        windowWidth: 375,
        windowHeight: 667,
        statusBarHeight: 20,
        SDKVersion: '2.0.0',
        version: '1.0.0',
        language: 'zh_CN',
        brand: 'unknown',
        model: 'unknown',
        system: 'unknown',
        platform: 'unknown'
      }

      // 获取窗口信息
      if (wx.getWindowInfo) {
        try {
          const windowInfo = wx.getWindowInfo()
          Object.assign(systemInfo, {
            pixelRatio: windowInfo.pixelRatio,
            screenWidth: windowInfo.screenWidth,
            screenHeight: windowInfo.screenHeight,
            windowWidth: windowInfo.windowWidth,
            windowHeight: windowInfo.windowHeight,
            statusBarHeight: windowInfo.statusBarHeight,
            safeArea: windowInfo.safeArea
          })
        } catch (error) {
          console.warn('获取窗口信息失败:', error)
        }
      }

      // 获取应用基础信息
      if (wx.getAppBaseInfo) {
        try {
          const appBaseInfo = wx.getAppBaseInfo()
          Object.assign(systemInfo, {
            SDKVersion: appBaseInfo.SDKVersion,
            version: appBaseInfo.version,
            language: appBaseInfo.language,
            theme: appBaseInfo.theme,
            host: appBaseInfo.host
          })
        } catch (error) {
          console.warn('获取应用基础信息失败:', error)
        }
      }

      // 获取设备信息
      if (wx.getDeviceInfo) {
        try {
          const deviceInfo = wx.getDeviceInfo()
          Object.assign(systemInfo, {
            brand: deviceInfo.brand,
            model: deviceInfo.model,
            system: deviceInfo.system,
            platform: deviceInfo.platform,
            benchmarkLevel: deviceInfo.benchmarkLevel,
            abi: deviceInfo.abi,
            cpuType: deviceInfo.cpuType,
            memorySize: deviceInfo.memorySize
          })
        } catch (error) {
          console.warn('获取设备信息失败:', error)
        }
      }

      // 获取系统设置
      if (wx.getSystemSetting) {
        try {
          const systemSetting = wx.getSystemSetting()
          Object.assign(systemInfo, {
            bluetoothEnabled: systemSetting.bluetoothEnabled,
            locationEnabled: systemSetting.locationEnabled,
            wifiEnabled: systemSetting.wifiEnabled,
            deviceOrientation: systemSetting.deviceOrientation
          })
        } catch (error) {
          console.warn('获取系统设置失败:', error)
        }
      }

      // 获取授权设置
      if (wx.getAppAuthorizeSetting) {
        try {
          const authSetting = wx.getAppAuthorizeSetting()
          Object.assign(systemInfo, {
            albumAuthorized: authSetting.albumAuthorized,
            cameraAuthorized: authSetting.cameraAuthorized,
            locationAuthorized: authSetting.locationAuthorized,
            microphoneAuthorized: authSetting.microphoneAuthorized,
            notificationAuthorized: authSetting.notificationAuthorized,
            notificationAlertAuthorized: authSetting.notificationAlertAuthorized,
            notificationBadgeAuthorized: authSetting.notificationBadgeAuthorized,
            notificationSoundAuthorized: authSetting.notificationSoundAuthorized,
            phoneCalendarAuthorized: authSetting.phoneCalendarAuthorized,
            locationReducedAccuracy: authSetting.locationReducedAccuracy
          })
        } catch (error) {
          console.warn('获取授权设置失败:', error)
        }
      }

      // 缓存结果
      this.cachedSystemInfo = systemInfo
      this.cacheTime = Date.now()

      return systemInfo
    } catch (error) {
      console.error('获取系统信息失败:', error)
      
      // 降级处理：尝试使用旧API
      return this.fallbackToLegacyAPI()
    }
  }

  /**
   * 降级处理：使用旧的 wx.getSystemInfoSync API
   */
  private fallbackToLegacyAPI(): SystemInfo {
    try {
      console.warn('使用降级方案: wx.getSystemInfoSync (已废弃)')
      const legacyInfo = wx.getSystemInfoSync()
      return legacyInfo as SystemInfo
    } catch (error) {
      console.error('降级方案也失败了:', error)
      // 返回默认值
      return {
        pixelRatio: 1,
        screenWidth: 375,
        screenHeight: 667,
        windowWidth: 375,
        windowHeight: 667,
        statusBarHeight: 20,
        SDKVersion: '2.0.0',
        version: '1.0.0',
        language: 'zh_CN',
        brand: 'unknown',
        model: 'unknown',
        system: 'unknown',
        platform: 'unknown'
      }
    }
  }

  /**
   * 仅获取窗口信息（用于布局计算）
   */
  getWindowInfo() {
    if (wx.getWindowInfo) {
      return wx.getWindowInfo()
    } else {
      // 降级处理
      const systemInfo = this.fallbackToLegacyAPI()
      return {
        pixelRatio: systemInfo.pixelRatio,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight,
        windowWidth: systemInfo.windowWidth,
        windowHeight: systemInfo.windowHeight,
        statusBarHeight: systemInfo.statusBarHeight,
        safeArea: systemInfo.safeArea
      }
    }
  }

  /**
   * 仅获取应用基础信息
   */
  getAppBaseInfo() {
    if (wx.getAppBaseInfo) {
      return wx.getAppBaseInfo()
    } else {
      // 降级处理
      const systemInfo = this.fallbackToLegacyAPI()
      return {
        SDKVersion: systemInfo.SDKVersion,
        version: systemInfo.version,
        language: systemInfo.language,
        theme: systemInfo.theme,
        host: systemInfo.host
      }
    }
  }

  /**
   * 仅获取设备信息
   */
  getDeviceInfo() {
    if (wx.getDeviceInfo) {
      return wx.getDeviceInfo()
    } else {
      // 降级处理
      const systemInfo = this.fallbackToLegacyAPI()
      return {
        brand: systemInfo.brand,
        model: systemInfo.model,
        system: systemInfo.system,
        platform: systemInfo.platform,
        benchmarkLevel: systemInfo.benchmarkLevel
      }
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cachedSystemInfo = null
    this.cacheTime = 0
  }

  /**
   * 检查新功能支持情况
   */
  checkNewAPISupport() {
    return {
      getWindowInfo: typeof wx.getWindowInfo === 'function',
      getAppBaseInfo: typeof wx.getAppBaseInfo === 'function',
      getDeviceInfo: typeof wx.getDeviceInfo === 'function',
      getSystemSetting: typeof wx.getSystemSetting === 'function',
      getAppAuthorizeSetting: typeof wx.getAppAuthorizeSetting === 'function'
    }
  }

  /**
   * 版本比较工具
   */
  compareVersion(version1: string, version2: string): number {
    const v1 = version1.split('.').map(Number)
    const v2 = version2.split('.').map(Number)
    
    for (let i = 0; i < Math.max(v1.length, v2.length); i++) {
      const num1 = v1[i] || 0
      const num2 = v2[i] || 0
      
      if (num1 > num2) return 1
      if (num1 < num2) return -1
    }
    
    return 0
  }
}

// 导出单例
export default new SystemInfoManager()
