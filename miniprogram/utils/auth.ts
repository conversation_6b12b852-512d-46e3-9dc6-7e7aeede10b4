// utils/auth.ts
import CONFIG from './config'
import RequestManager from './request'
import SystemInfoManager from './systemInfo'

interface UserInfo {
  nickName: string
  avatarUrl: string
  gender: number
  country: string
  province: string
  city: string
  language: string
}

interface LoginResponse {
  success: boolean
  data?: {
    token: string
    user: {
      id: number
      openid: string
      nickName: string
      avatarUrl: string
      phone?: string
      isNewUser: boolean
      createdAt: string
      updatedAt: string
    }
    expiresIn: number
  }
  message?: string
}

// 后端返回的数据结构
interface BackendLoginResponse {
  code: string
  message: string
  success: boolean
  data: {
    userId: number
    sessionId: string
    sessionExpireTime: string
    isNewUser: boolean
    userInfo: {
      id: number
      nickname: string | null
      avatarUrl: string | null
      gender: number | null
      phoneNumber: string | null
      hasPhoneNumber: boolean
      createdAt: string
    }
  }
  timestamp: number
}

class AuthManager {
  private baseUrl: string
  private tokenKey: string
  private userInfoKey: string

  constructor() {
    this.baseUrl = CONFIG.API_BASE_URL
    this.tokenKey = CONFIG.STORAGE_KEYS.USER_TOKEN
    this.userInfoKey = CONFIG.STORAGE_KEYS.USER_INFO
  }



  /**
   * 微信基础登录（仅获取openid，不获取用户信息）
   * @returns 登录结果
   */
  async wechatLogin(): Promise<LoginResponse> {
    try {
      // 显示登录中提示
      wx.showLoading({
        title: '登录中...',
        mask: true
      })

      // 1. 获取微信授权码
      const loginRes = await this.wxLogin()

      if (!loginRes.code) {
        const errorMsg = '获取微信授权码失败'
        console.error(errorMsg, loginRes)
        wx.hideLoading()
        wx.showToast({
          title: errorMsg,
          icon: 'error',
          duration: 3000
        })
        throw new Error(errorMsg)
      }

      // 2. 发送登录请求（不传用户信息）
      const backendResult = await this.sendLoginRequest(loginRes.code, null)

      // 检查后端响应结构
      if (!backendResult || !backendResult.success || backendResult.code !== 'SUCCESS') {
        const errorMsg = backendResult?.message || '登录失败'
        console.error('登录失败:', backendResult)
        wx.hideLoading()
        wx.showToast({
          title: errorMsg,
          icon: 'error',
          duration: 3000
        })
        throw new Error(errorMsg)
      }

      if (!backendResult.data || !backendResult.data.sessionId || !backendResult.data.userInfo) {
        const errorMsg = '登录响应数据不完整'
        console.error(errorMsg, backendResult.data)
        wx.hideLoading()
        wx.showToast({
          title: errorMsg,
          icon: 'error',
          duration: 3000
        })
        throw new Error(errorMsg)
      }

      // 处理后端返回的数据结构
      const token = backendResult.data.sessionId
      const userInfoFromServer = backendResult.data.userInfo

      // 优先使用服务端返回的昵称，如果没有则显示"完善信息"
      const nickname = userInfoFromServer.nickname && userInfoFromServer.nickname.trim()
        ? userInfoFromServer.nickname
        : '完善信息'

      // 优先使用本地头像，如果没有本地头像则使用服务端头像
      const localAvatarPath = this.getLocalAvatarPath()
      const avatarUrl = localAvatarPath || userInfoFromServer.avatarUrl || '/images/default-avatar.png'

      console.log('登录成功，头像选择:', {
        localAvatarPath,
        serverAvatarUrl: userInfoFromServer.avatarUrl,
        finalAvatarUrl: avatarUrl
      })

      const user = {
        id: userInfoFromServer.id,
        openid: '', // 后端没有返回openid，使用空字符串
        nickName: nickname,
        avatarUrl: avatarUrl,
        phone: userInfoFromServer.phoneNumber || undefined,
        phoneNumber: userInfoFromServer.phoneNumber || undefined,
        hasPhoneNumber: userInfoFromServer.hasPhoneNumber || false,
        gender: userInfoFromServer.gender || undefined,
        isNewUser: backendResult.data.isNewUser,
        createdAt: userInfoFromServer.createdAt,
        updatedAt: userInfoFromServer.createdAt, // 使用createdAt作为updatedAt
        hasRealInfo: !!(nickname && nickname !== '完善信息') // 如果有真实昵称则标记为已完善信息
      }

      // 保存登录凭证和用户信息
      this.saveToken(token)
      this.saveUserInfo(user)

      wx.hideLoading()
      wx.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 2000
      })

      return {
        success: true,
        data: {
          token: token,
          user: user,
          expiresIn: 7200 // 默认2小时
        },
        message: '登录成功'
      }

    } catch (error: any) {
      console.error('微信登录失败:', error)

      wx.hideLoading()

      // 根据错误类型显示不同的提示
      let errorMessage = '登录失败，请重试'

      if (error.message) {
        if (error.message.includes('网络')) {
          errorMessage = '网络连接失败，请检查网络'
        } else if (error.message.includes('授权')) {
          errorMessage = '微信授权失败，请重试'
        } else if (error.message.includes('服务器')) {
          errorMessage = '服务器连接失败，请稍后重试'
        } else {
          errorMessage = error.message
        }
      }

      wx.showToast({
        title: errorMessage,
        icon: 'error',
        duration: 3000
      })

      return {
        success: false,
        message: errorMessage
      }
    }
  }

  /**
   * 调用wx.login获取code
   */
  private wxLogin(): Promise<WechatMiniprogram.LoginSuccessCallbackResult> {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  }



  /**
   * 更新用户头像（下载并保存到本地）
   * @param avatarUrl 临时头像路径
   */
  async updateUserAvatar(avatarUrl: string): Promise<void> {
    try {
      // 先保存临时URL，立即更新显示
      const currentUser = this.getUserInfo() || {}
      const updatedUser = {
        ...currentUser,
        avatarUrl: avatarUrl,
        tempAvatarUrl: avatarUrl, // 保存原始临时URL作为备份
        hasRealInfo: true
      }
      this.saveUserInfo(updatedUser)

      // 异步下载并保存头像到本地
      this.downloadAvatarToLocal(avatarUrl)

      console.log('头像更新成功，临时路径:', avatarUrl)
    } catch (error) {
      console.error('更新头像失败:', error)
      throw error
    }
  }

  /**
   * 异步下载头像到本地（不阻塞UI）
   * @param avatarUrl 临时头像路径
   */
  private async downloadAvatarToLocal(avatarUrl: string): Promise<void> {
    try {
      console.log('开始后台下载头像:', avatarUrl)

      // 下载临时文件
      const downloadResult = await this.downloadTempFile(avatarUrl)
      console.log('头像下载完成:', downloadResult)

      // 获取文件信息
      const fileInfo = await this.getFileInfo(downloadResult.tempFilePath)
      console.log('头像文件信息:', fileInfo)

      // 生成本地存储路径
      const localPath = this.generateLocalPath(fileInfo.size)

      // 保存到本地存储
      const savedPath = await this.saveToLocalStorage(downloadResult.tempFilePath, localPath)
      console.log('头像保存到本地:', savedPath)

      // 更新头像信息记录
      const avatarInfo = {
        localPath: savedPath,
        originalUrl: avatarUrl,
        downloadTime: Date.now(),
        fileSize: fileInfo.size,
        userId: this.getCurrentUserId(),
        isGlobal: true
      }

      this.saveAvatarInfo(avatarInfo)

      // 更新用户信息，使用本地路径
      const currentUser = this.getUserInfo() || {}
      if (currentUser) {
        currentUser.avatarUrl = savedPath
        this.saveUserInfo(currentUser)
      }

      console.log('头像本地保存完成:', savedPath)
    } catch (error) {
      console.error('后台下载头像失败:', error)
      // 下载失败不影响用户体验，继续使用临时URL
    }
  }

  /**
   * 获取用户头像路径（优先使用本地缓存，不受登录状态影响）
   * @returns 头像路径
   */
  async getUserAvatarPath(): Promise<string> {
    try {
      // 优先尝试获取本地头像路径（不受登录状态影响）
      const localAvatarPath = this.getLocalAvatarPath()

      if (localAvatarPath) {
        console.log('使用本地头像:', localAvatarPath)
        return localAvatarPath
      }

      // 如果没有本地头像，且用户已登录，返回用户信息中的头像URL
      if (this.isLoggedIn()) {
        const userInfo = this.getUserInfo()
        const avatarUrl = userInfo?.avatarUrl || '/images/default-avatar.png'
        console.log('使用用户头像:', avatarUrl)
        return avatarUrl
      }

      // 未登录且没有本地头像，返回默认头像
      console.log('使用默认头像')
      return '/images/default-avatar.png'
    } catch (error) {
      console.error('获取用户头像路径失败:', error)

      // 出错时返回默认头像
      return '/images/default-avatar.png'
    }
  }

  /**
   * 清理用户头像缓存（仅清理，不影响显示）
   */
  async clearUserAvatarCache(): Promise<void> {
    try {
      // 清理本地头像文件和缓存信息
      const avatarInfo = this.getAvatarInfo()

      if (avatarInfo && avatarInfo.localPath) {
        // 删除本地文件
        await this.removeLocalFile(avatarInfo.localPath)
      }

      // 清理存储记录
      this.removeAvatarInfo()
      this.removeGlobalAvatarInfo()

      console.log('用户头像缓存清理完成')
    } catch (error) {
      console.error('清理用户头像缓存失败:', error)
    }
  }

  /**
   * 更新用户昵称（调用服务端接口）
   * @param nickName 用户昵称
   * @returns 更新结果
   */
  async updateUserNickname(nickName: string): Promise<{ success: boolean; message: string }> {
    try {
      // 检查是否已登录
      if (!this.isLoggedIn()) {
        return {
          success: false,
          message: '请先登录'
        }
      }

      // 调用服务端接口更新用户信息
      const result = await this.updateUserProfile({ nickname: nickName })

      if (result.success) {
        // 更新本地存储的用户信息
        const currentUser = this.getUserInfo() || {}
        const updatedUser = {
          ...currentUser,
          nickName: nickName,
          hasRealInfo: true
        }
        this.saveUserInfo(updatedUser)
      }

      return result
    } catch (error: any) {
      console.error('更新昵称失败:', error)
      return {
        success: false,
        message: error.message || '更新昵称失败，请重试'
      }
    }
  }

  /**
   * 更新用户信息（调用服务端接口）
   * @param userInfo 要更新的用户信息
   * @returns 更新结果
   */
  async updateUserProfile(userInfo: {
    nickname?: string;
    avatarUrl?: string;
    gender?: number;
    country?: string;
    province?: string;
    city?: string;
  }): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      // 检查是否已登录
      if (!this.isLoggedIn()) {
        return {
          success: false,
          message: '请先登录'
        }
      }

      // 调用后端接口更新用户信息
      const result = await this.sendUpdateProfileRequest(userInfo)
      console.log('更新用户信息响应:', result)

      if (result.success && result.data) {
        return {
          success: true,
          message: result.data.message || '用户信息更新成功',
          data: result.data
        }
      } else {
        return {
          success: false,
          message: result.message || '更新用户信息失败'
        }
      }
    } catch (error: any) {
      console.error('更新用户信息失败:', error)
      return {
        success: false,
        message: error.message || '更新用户信息失败，请重试'
      }
    }
  }

  /**
   * 发送登录请求到后端
   */
  private async sendLoginRequest(code: string, userInfo: UserInfo | null): Promise<BackendLoginResponse> {
    try {
      const result = await RequestManager.post('/api/auth/wechat/login', {
        code: code,
        userInfo: userInfo
      })
      return result as BackendLoginResponse
    } catch (error: any) {
      console.error('登录请求失败:', error)

      // 处理特定的错误情况
      if (error.message && error.message.includes('401')) {
        throw new Error('微信授权码已过期，请重试')
      } else if (error.message && error.message.includes('500')) {
        throw new Error('服务器内部错误，请稍后重试')
      }

      // 处理网络错误
      let errorMessage = '网络请求失败'
      if (error.errMsg) {
        if (error.errMsg.includes('timeout')) {
          errorMessage = '请求超时，请检查网络连接'
        } else if (error.errMsg.includes('fail')) {
          errorMessage = '网络连接失败，请检查网络设置'
        }
      }

      throw new Error(errorMessage)
    }
  }

  /**
   * 保存登录凭证
   */
  saveToken(token: string): void {
    try {
      wx.setStorageSync(this.tokenKey, token)
    } catch (error) {
      console.error('保存token失败:', error)
    }
  }

  /**
   * 保存用户信息
   */
  saveUserInfo(userInfo: any): void {
    try {
      wx.setStorageSync(this.userInfoKey, userInfo)
    } catch (error) {
      console.error('保存用户信息失败:', error)
    }
  }

  /**
   * 获取登录凭证
   */
  getToken(): string | null {
    try {
      return wx.getStorageSync(this.tokenKey)
    } catch (error) {
      console.error('获取token失败:', error)
      return null
    }
  }

  /**
   * 获取用户信息
   */
  getUserInfo(): any {
    try {
      return wx.getStorageSync(this.userInfoKey)
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  /**
   * 获取小程序AppId（动态获取）
   */
  getAppId(): string {
    return CONFIG.WECHAT.getAppId()
  }

  /**
   * 清除登录凭证（保留头像缓存）
   */
  clearToken(): void {
    try {
      // 在清除用户信息前，先保存头像信息
      const userInfo = this.getUserInfo()
      const avatarUrl = userInfo?.avatarUrl

      // 清除登录凭证和用户信息
      wx.removeStorageSync(this.tokenKey)
      wx.removeStorageSync(this.userInfoKey)

      // 如果有头像URL，保存到临时存储中，以便下次登录时恢复
      if (avatarUrl && avatarUrl !== '/images/default-avatar.png') {
        try {
          wx.setStorageSync(CONFIG.STORAGE_KEYS.LAST_USER_AVATAR, avatarUrl)
          console.log('已保存最后使用的头像:', avatarUrl)
        } catch (error) {
          console.warn('保存最后使用的头像失败:', error)
        }
      }
    } catch (error) {
      console.error('清除token失败:', error)
    }
  }

  // ==================== 头像管理相关方法 ====================

  private readonly AVATAR_STORAGE_KEY = CONFIG.STORAGE_KEYS.USER_AVATAR_INFO
  private readonly GLOBAL_AVATAR_KEY = CONFIG.STORAGE_KEYS.GLOBAL_AVATAR_INFO
  private readonly AVATAR_DIR = 'avatars'
  private readonly CACHE_EXPIRE_TIME = 90 * 24 * 60 * 60 * 1000 // 90天

  /**
   * 获取本地头像路径
   */
  private getLocalAvatarPath(): string | null {
    try {
      // 优先获取全局头像信息
      let avatarInfo = this.getGlobalAvatarInfo()

      // 如果没有全局头像，尝试获取用户头像
      if (!avatarInfo) {
        avatarInfo = this.getAvatarInfo()
      }

      if (!avatarInfo) {
        return null
      }

      // 检查文件是否还存在
      if (this.isFileExists(avatarInfo.localPath)) {
        // 检查是否过期
        if (this.isAvatarExpired(avatarInfo)) {
          console.log('本地头像已过期，需要重新下载')
          this.removeAvatarInfo()
          this.removeGlobalAvatarInfo()
          return null
        }

        console.log('使用本地头像:', avatarInfo.localPath)
        return avatarInfo.localPath
      } else {
        console.log('本地头像文件不存在，清理记录')
        this.removeAvatarInfo()
        this.removeGlobalAvatarInfo()
        return null
      }
    } catch (error) {
      console.error('获取本地头像路径失败:', error)
      return null
    }
  }

  /**
   * 下载临时文件
   */
  private downloadTempFile(url: string): Promise<WechatMiniprogram.DownloadFileSuccessCallbackResult> {
    return new Promise((resolve, reject) => {
      wx.downloadFile({
        url: url,
        success: resolve,
        fail: reject
      })
    })
  }

  /**
   * 获取文件信息
   */
  private getFileInfo(filePath: string): Promise<WechatMiniprogram.GetFileInfoSuccessCallbackResult> {
    return new Promise((resolve, reject) => {
      wx.getFileInfo({
        filePath: filePath,
        success: resolve,
        fail: reject
      })
    })
  }

  /**
   * 生成本地存储路径
   */
  private generateLocalPath(fileSize: number): string {
    const timestamp = Date.now()
    const userId = this.getCurrentUserId() || 'anonymous'
    const fileName = `avatar_${userId}_${timestamp}.jpg`
    return `${wx.env.USER_DATA_PATH}/${this.AVATAR_DIR}/${fileName}`
  }

  /**
   * 保存文件到本地存储
   */
  private saveToLocalStorage(tempFilePath: string, localPath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      // 确保目录存在
      const dirPath = localPath.substring(0, localPath.lastIndexOf('/'))

      wx.getFileSystemManager().mkdir({
        dirPath: dirPath,
        recursive: true,
        success: () => {
          // 复制文件到本地存储
          wx.getFileSystemManager().copyFile({
            srcPath: tempFilePath,
            destPath: localPath,
            success: () => resolve(localPath),
            fail: reject
          })
        },
        fail: (error) => {
          // 目录可能已存在，直接尝试复制文件
          wx.getFileSystemManager().copyFile({
            srcPath: tempFilePath,
            destPath: localPath,
            success: () => resolve(localPath),
            fail: reject
          })
        }
      })
    })
  }

  /**
   * 保存头像信息到本地存储
   */
  private saveAvatarInfo(avatarInfo: any): void {
    try {
      // 保存到用户相关的存储
      wx.setStorageSync(this.AVATAR_STORAGE_KEY, avatarInfo)

      // 如果是全局头像，也保存到全局存储
      if (avatarInfo.isGlobal) {
        wx.setStorageSync(this.GLOBAL_AVATAR_KEY, avatarInfo)
      }
    } catch (error) {
      console.error('保存头像信息失败:', error)
    }
  }

  /**
   * 获取头像信息
   */
  private getAvatarInfo(): any {
    try {
      return wx.getStorageSync(this.AVATAR_STORAGE_KEY)
    } catch (error) {
      console.error('获取头像信息失败:', error)
      return null
    }
  }

  /**
   * 获取全局头像信息
   */
  private getGlobalAvatarInfo(): any {
    try {
      return wx.getStorageSync(this.GLOBAL_AVATAR_KEY)
    } catch (error) {
      console.error('获取全局头像信息失败:', error)
      return null
    }
  }

  /**
   * 删除头像信息记录
   */
  private removeAvatarInfo(): void {
    try {
      wx.removeStorageSync(this.AVATAR_STORAGE_KEY)
    } catch (error) {
      console.error('删除头像信息失败:', error)
    }
  }

  /**
   * 删除全局头像信息记录
   */
  private removeGlobalAvatarInfo(): void {
    try {
      wx.removeStorageSync(this.GLOBAL_AVATAR_KEY)
    } catch (error) {
      console.error('删除全局头像信息失败:', error)
    }
  }

  /**
   * 检查文件是否存在
   */
  private isFileExists(filePath: string): boolean {
    try {
      wx.getFileSystemManager().accessSync(filePath)
      return true
    } catch {
      return false
    }
  }

  /**
   * 检查头像是否过期
   */
  private isAvatarExpired(avatarInfo: any): boolean {
    const now = Date.now()
    return (now - avatarInfo.downloadTime) > this.CACHE_EXPIRE_TIME
  }

  /**
   * 删除本地文件
   */
  private removeLocalFile(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      wx.getFileSystemManager().unlink({
        filePath: filePath,
        success: () => resolve(),
        fail: reject
      })
    })
  }

  /**
   * 获取当前用户ID
   */
  private getCurrentUserId(): string | undefined {
    const userInfo = this.getUserInfo()
    return userInfo?.id?.toString()
  }

  /**
   * 检查登录状态
   */
  isLoggedIn(): boolean {
    return !!this.getToken()
  }

  /**
   * 退出登录
   */
  logout(): void {
    this.clearToken()
    // 不跳转页面，停留在当前页面
    wx.showToast({
      title: '已退出登录',
      icon: 'success',
      duration: 1500
    })
  }

  /**
   * 检查登录状态
   */
  checkLoginStatus(): boolean {
    return this.isLoggedIn()
  }

  /**
   * 检查是否已完善用户信息
   */
  hasCompleteUserInfo(): boolean {
    const userInfo = this.getUserInfo()
    return userInfo && userInfo.hasRealInfo === true
  }

  /**
   * 检查新版头像昵称功能支持情况
   * @returns 功能支持情况
   */
  checkNewProfileCapabilities(): { canChooseAvatar: boolean; canEditNickname: boolean } {
    try {
      // 使用新的系统信息管理器替代已废弃的 wx.getSystemInfoSync
      const appBaseInfo = SystemInfoManager.getAppBaseInfo()
      const SDKVersion = appBaseInfo.SDKVersion

      // 基础库 2.21.2 以上支持新的头像昵称能力
      const supportNewFeatures = SystemInfoManager.compareVersion(SDKVersion, '2.21.2') >= 0

      console.log('头像昵称功能检测:', {
        SDKVersion,
        supportNewFeatures,
        canChooseAvatar: supportNewFeatures,
        canEditNickname: supportNewFeatures
      })

      return {
        canChooseAvatar: supportNewFeatures,
        canEditNickname: supportNewFeatures
      }
    } catch (error) {
      console.error('检查头像昵称功能支持失败:', error)
      return { canChooseAvatar: false, canEditNickname: false }
    }
  }

  /**
   * 获取用户手机号
   * @param code 微信返回的手机号授权码
   * @returns 获取结果
   */
  async getPhoneNumber(code: string): Promise<{ success: boolean; message: string; phoneNumber?: string }> {
    try {
      // 检查是否已登录
      if (!this.isLoggedIn()) {
        return {
          success: false,
          message: '请先登录'
        }
      }

      // 调用后端接口获取手机号
      const result = await this.sendPhoneRequest(code)
      console.log('获取手机号响应:', result)

      if (result.success && result.data) {
        // 更新本地存储的用户信息
        const currentUser = this.getUserInfo() || {}
        const updatedUser = {
          ...currentUser,
          phone: result.data.purePhoneNumber,
          phoneNumber: result.data.purePhoneNumber,
          hasPhoneNumber: true
        }

        this.saveUserInfo(updatedUser)

        return {
          success: true,
          message: result.data.message || '手机号获取成功',
          phoneNumber: result.data.purePhoneNumber
        }
      } else {
        return {
          success: false,
          message: result.message || '获取手机号失败'
        }
      }
    } catch (error: any) {
      console.error('获取手机号失败:', error)
      return {
        success: false,
        message: error.message || '获取手机号失败，请重试'
      }
    }
  }

  /**
   * 发送获取手机号请求到后端
   */
  private async sendPhoneRequest(code: string): Promise<any> {
    try {
      const result = await RequestManager.post('/api/auth/phone/get', {
        code: code
      })
      console.log('手机号接口响应:', result)
      return result
    } catch (error) {
      console.error('手机号请求失败:', error)
      throw error
    }
  }

  /**
   * 发送更新用户信息请求到后端
   */
  private async sendUpdateProfileRequest(userInfo: {
    nickname?: string;
    avatarUrl?: string;
    gender?: number;
    country?: string;
    province?: string;
    city?: string;
  }): Promise<any> {
    try {
      const result = await RequestManager.put('/api/auth/profile', userInfo)
      console.log('更新用户信息接口响应:', result)
      return result
    } catch (error) {
      console.error('更新用户信息请求失败:', error)
      throw error
    }
  }
}

// 导出单例
export default new AuthManager()
