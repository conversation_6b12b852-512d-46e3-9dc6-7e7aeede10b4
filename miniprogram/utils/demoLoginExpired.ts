// utils/demoLoginExpired.ts
// 演示登录态失效拦截功能

import RequestManager from './request'
import { LOGIN_CONSTANTS } from './constants'

/**
 * 演示登录态失效拦截功能
 */
export class LoginExpiredDemo {
  
  /**
   * 模拟一个会返回登录过期的API请求
   */
  static async simulateLoginExpiredRequest() {
    console.log('开始模拟登录过期请求...')
    
    // 模拟原始的微信请求，返回登录过期响应
    const originalRequest = wx.request
    
    // 临时替换 wx.request 来模拟服务端返回登录过期
    wx.request = function(options: any) {
      console.log('拦截到请求:', options.url)
      
      // 模拟服务端返回登录过期响应
      setTimeout(() => {
        if (options.success) {
          options.success({
            statusCode: 200,
            data: {
              code: LOGIN_CONSTANTS.LOGIN_EXPIRED_CODE,
              message: '登录态已过期，请重新登录',
              data: null,
              timestamp: Date.now(),
              success: false
            },
            header: {},
            cookies: []
          })
        }
      }, 500) // 模拟网络延迟
      
      return {} as any
    }
    
    try {
      // 发送一个会触发登录过期的请求
      const result = await RequestManager.get('/api/user/profile')
      console.log('请求结果:', result)
      
      // 恢复原始的 wx.request
      wx.request = originalRequest
      
      return result
    } catch (error) {
      console.error('请求失败:', error)
      
      // 恢复原始的 wx.request
      wx.request = originalRequest
      
      throw error
    }
  }

  /**
   * 模拟一个会返回未登录的API请求
   */
  static async simulateNotLoginRequest() {
    console.log('开始模拟未登录请求...')
    
    // 模拟原始的微信请求，返回未登录响应
    const originalRequest = wx.request
    
    // 临时替换 wx.request 来模拟服务端返回未登录
    wx.request = function(options: any) {
      console.log('拦截到请求:', options.url)
      
      // 模拟服务端返回未登录响应
      setTimeout(() => {
        if (options.success) {
          options.success({
            statusCode: 200,
            data: {
              code: LOGIN_CONSTANTS.NOT_LOGIN_CODE,
              message: '用户未登录，请先登录',
              data: null,
              timestamp: Date.now(),
              success: false
            },
            header: {},
            cookies: []
          })
        }
      }, 500) // 模拟网络延迟
      
      return {} as any
    }
    
    try {
      // 发送一个会触发未登录的请求
      const result = await RequestManager.post('/api/orders/create', {
        productId: 123,
        quantity: 1
      })
      console.log('请求结果:', result)
      
      // 恢复原始的 wx.request
      wx.request = originalRequest
      
      return result
    } catch (error) {
      console.error('请求失败:', error)
      
      // 恢复原始的 wx.request
      wx.request = originalRequest
      
      throw error
    }
  }

  /**
   * 模拟HTTP 401状态码
   */
  static async simulate401Request() {
    console.log('开始模拟401状态码请求...')
    
    // 模拟原始的微信请求，返回401状态码
    const originalRequest = wx.request
    
    // 临时替换 wx.request 来模拟服务端返回401
    wx.request = function(options: any) {
      console.log('拦截到请求:', options.url)
      
      // 模拟服务端返回401状态码
      setTimeout(() => {
        if (options.success) {
          options.success({
            statusCode: 401,
            data: {
              error: 'Unauthorized',
              message: 'Token expired'
            },
            header: {},
            cookies: []
          })
        }
      }, 500) // 模拟网络延迟
      
      return {} as any
    }
    
    try {
      // 发送一个会触发401的请求
      const result = await RequestManager.get('/api/protected/data')
      console.log('请求结果:', result)
      
      // 恢复原始的 wx.request
      wx.request = originalRequest
      
      return result
    } catch (error) {
      console.error('请求失败:', error)
      
      // 恢复原始的 wx.request
      wx.request = originalRequest
      
      throw error
    }
  }

  /**
   * 运行完整的演示
   */
  static async runFullDemo() {
    console.log('=== 开始登录拦截功能演示 ===')
    
    try {
      console.log('\n1. 演示业务层面的登录过期拦截...')
      await this.simulateLoginExpiredRequest()
      
      console.log('\n2. 演示业务层面的未登录拦截...')
      await this.simulateNotLoginRequest()
      
      console.log('\n3. 演示HTTP 401状态码拦截...')
      await this.simulate401Request()
      
      console.log('\n=== 登录拦截功能演示完成 ===')
      
    } catch (error) {
      console.error('演示过程中出现错误:', error)
    }
  }
}

export default LoginExpiredDemo
