// utils/imageHelper.ts
// 图片处理相关的工具函数

/**
 * 图片处理工具类
 */
export class ImageHelper {
  
  /**
   * 处理商品图片URL，确保能正确显示
   * @param imageUrl 原始图片URL
   * @param defaultImage 默认图片路径
   * @returns 处理后的图片URL
   */
  static processProductImageUrl(imageUrl: string | null | undefined, defaultImage: string = '/images/default-avatar.png'): string {
    // 如果没有图片URL，返回默认图片
    if (!imageUrl || typeof imageUrl !== 'string' || imageUrl.trim() === '') {
      console.log('图片URL为空，使用默认图片:', defaultImage)
      return defaultImage
    }

    let processedUrl = imageUrl.trim()

    try {
      // 处理阿里云OSS URL
      if (processedUrl.includes('aliyuncs.com')) {
        // 确保使用HTTPS协议
        if (processedUrl.startsWith('http://')) {
          processedUrl = processedUrl.replace('http://', 'https://')
        } else if (!processedUrl.startsWith('https://')) {
          processedUrl = 'https://' + processedUrl
        }

        // 检查OSS URL的有效性
        const url = new URL(processedUrl)
        
        // 检查是否包含必要的OSS参数
        if (url.searchParams.has('Expires')) {
          const expires = parseInt(url.searchParams.get('Expires') || '0')
          const now = Math.floor(Date.now() / 1000)
          
          if (expires < now) {
            console.warn('OSS签名URL已过期:', processedUrl)
            return defaultImage
          }
        }

        console.log('处理后的OSS图片URL:', processedUrl)
        return processedUrl
      }

      // 处理其他类型的URL
      if (processedUrl.startsWith('//')) {
        processedUrl = 'https:' + processedUrl
      } else if (!processedUrl.startsWith('http://') && !processedUrl.startsWith('https://') && !processedUrl.startsWith('/')) {
        processedUrl = 'https://' + processedUrl
      }

      // 验证URL格式
      new URL(processedUrl)
      
      console.log('处理后的图片URL:', processedUrl)
      return processedUrl

    } catch (error) {
      console.error('处理图片URL失败:', error, '原始URL:', imageUrl)
      return defaultImage
    }
  }

  /**
   * 检查图片URL是否有效
   * @param imageUrl 图片URL
   * @returns 是否有效
   */
  static isValidImageUrl(imageUrl: string): boolean {
    if (!imageUrl || typeof imageUrl !== 'string') {
      return false
    }

    try {
      const url = new URL(imageUrl)
      return url.protocol === 'http:' || url.protocol === 'https:'
    } catch {
      return false
    }
  }

  /**
   * 获取图片的缩略图URL（针对阿里云OSS）
   * @param originalUrl 原始图片URL
   * @param width 宽度
   * @param height 高度
   * @returns 缩略图URL
   */
  static getThumbnailUrl(originalUrl: string, width: number = 150, height: number = 150): string {
    if (!originalUrl || !this.isValidImageUrl(originalUrl)) {
      return '/images/default-avatar.png'
    }

    try {
      const url = new URL(originalUrl)
      
      // 如果是阿里云OSS，添加图片处理参数
      if (url.hostname.includes('aliyuncs.com')) {
        // 移除现有的图片处理参数
        url.searchParams.delete('x-oss-process')
        
        // 添加新的缩略图参数
        const processParam = `image/resize,w_${width},h_${height},m_fill/format,webp`
        url.searchParams.set('x-oss-process', processParam)
        
        return url.toString()
      }

      return originalUrl
    } catch (error) {
      console.error('生成缩略图URL失败:', error)
      return originalUrl
    }
  }

  /**
   * 预加载图片
   * @param imageUrl 图片URL
   * @returns Promise<boolean> 是否加载成功
   */
  static preloadImage(imageUrl: string): Promise<boolean> {
    return new Promise((resolve) => {
      if (!imageUrl || !this.isValidImageUrl(imageUrl)) {
        resolve(false)
        return
      }

      // 微信小程序中预加载图片
      wx.getImageInfo({
        src: imageUrl,
        success: () => {
          console.log('图片预加载成功:', imageUrl)
          resolve(true)
        },
        fail: (error) => {
          console.error('图片预加载失败:', error, imageUrl)
          resolve(false)
        }
      })
    })
  }

  /**
   * 批量预加载图片
   * @param imageUrls 图片URL数组
   * @returns Promise<boolean[]> 加载结果数组
   */
  static async batchPreloadImages(imageUrls: string[]): Promise<boolean[]> {
    const promises = imageUrls.map(url => this.preloadImage(url))
    return Promise.all(promises)
  }
}

export default ImageHelper
