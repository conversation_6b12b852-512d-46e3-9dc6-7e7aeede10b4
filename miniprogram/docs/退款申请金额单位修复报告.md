# 退款申请金额单位修复报告

## 🐛 问题描述

**错误**: `/api/refund/create` 接口调用失败

**根本原因**: 前端提交的退款金额单位不正确
- **前端提交**: 以分为单位（1990）
- **后端期望**: 以元为单位（19.90）

## 🔍 问题分析

### 数据流分析

#### 问题流程（修复前）
```
接口返回 → 前端转换 → 页面显示 → 提交给后端
19.90元  → 1990分   → ¥19.90   → 1990（错误：分为单位）
```

#### 正确流程（修复后）
```
接口返回 → 前端转换 → 页面显示 → 提交给后端
19.90元  → 1990分   → ¥19.90   → 19.90（正确：元为单位）
```

### 代码问题定位

#### 1. 数据转换阶段（正确）
```typescript
// 将接口返回的元转换为分，用于页面内部计算
refundAmount: Math.round(item.unitPrice * item.availableQuantity * 100) // 元转分
```

#### 2. 页面显示阶段（正确）
```xml
<!-- WXS 格式化：分转元显示 -->
<text class="amount-value">¥{{utils.formatAmount(refundAmount)}}</text>
```

#### 3. 提交阶段（问题所在）
```typescript
// 修复前：直接提交分为单位的金额
const request: CreateRefundRequest = {
  refundAmount, // 1990（分为单位）❌
  refundItems: selectedItems // 商品金额也是分为单位 ❌
}

// 修复后：转换为元为单位再提交
const request: CreateRefundRequest = {
  refundAmount: refundAmount / 100, // 19.90（元为单位）✅
  refundItems: refundItemsForSubmit // 商品金额转换为元为单位 ✅
}
```

## ✅ 修复方案

### 1. 新增类型定义

为了区分页面内部使用的数据和提交给服务端的数据，新增了专门的类型：

```typescript
// 页面内部使用（金额以分为单位）
export interface RefundItem {
  orderItemId: number
  refundQuantity: number
  refundAmount: number // 分为单位
}

// 提交给服务端（金额以元为单位）
export interface RefundItemForSubmit {
  orderItemId: number
  refundQuantity: number
  refundAmount: number // 元为单位
}

// 创建退款申请请求
export interface CreateRefundRequest {
  orderId: number
  refundType: RefundType
  refundReason: RefundReason
  refundDescription?: string
  refundAmount: number // 元为单位
  refundItems: RefundItemForSubmit[]
  evidenceImages?: string[]
}
```

### 2. 修复提交逻辑

```typescript
async submitRefundRequest() {
  // ... 其他逻辑

  // 转换退款商品明细（分转元）
  const refundItemsForSubmit: RefundItemForSubmit[] = selectedItems
    .filter(item => item.refundQuantity > 0)
    .map(item => ({
      orderItemId: item.orderItemId,
      refundQuantity: item.refundQuantity,
      refundAmount: item.refundAmount / 100 // 分转元
    }))

  const request: CreateRefundRequest = {
    orderId: orderInfo.orderId,
    refundType,
    refundReason: mappedRefundReason as RefundReason,
    refundDescription,
    refundAmount: refundAmount / 100, // 分转元
    refundItems: refundItemsForSubmit,
    evidenceImages
  }

  const result = await RefundService.createRefundRequest(request)
}
```

## 🔧 关键修复点

### 1. 总退款金额转换
```typescript
// 修复前
refundAmount: refundAmount // 1990（分）

// 修复后  
refundAmount: refundAmount / 100 // 19.90（元）
```

### 2. 退款商品明细转换
```typescript
// 修复前
refundItems: selectedItems.filter(item => item.refundQuantity > 0)
// 商品的 refundAmount 是分为单位

// 修复后
refundItems: selectedItems
  .filter(item => item.refundQuantity > 0)
  .map(item => ({
    orderItemId: item.orderItemId,
    refundQuantity: item.refundQuantity,
    refundAmount: item.refundAmount / 100 // 分转元
  }))
```

### 3. 类型安全
```typescript
// 明确类型注解，避免混淆
const refundItemsForSubmit: RefundItemForSubmit[] = ...
```

## 🎯 修复效果

### 修复前的提交数据
```json
{
  "orderId": 65,
  "refundType": "refund_only",
  "refundReason": "size_mismatch",
  "refundAmount": 1990,  // ❌ 分为单位
  "refundItems": [{
    "orderItemId": 73,
    "refundQuantity": 1,
    "refundAmount": 1990  // ❌ 分为单位
  }]
}
```

### 修复后的提交数据
```json
{
  "orderId": 65,
  "refundType": "refund_only", 
  "refundReason": "size_mismatch",
  "refundAmount": 19.90,  // ✅ 元为单位
  "refundItems": [{
    "orderItemId": 73,
    "refundQuantity": 1,
    "refundAmount": 19.90  // ✅ 元为单位
  }]
}
```

## 🧪 验证方法

### 1. 检查提交数据
在浏览器开发者工具的网络面板中查看 `/api/refund/create` 请求：
- `refundAmount` 应该是 19.90（而不是 1990）
- `refundItems[0].refundAmount` 应该是 19.90（而不是 1990）

### 2. 检查控制台日志
```javascript
console.log('准备提交的退款申请数据:', request)
// 应该显示元为单位的金额
```

### 3. 验证接口响应
修复后，接口应该返回成功响应，而不是参数错误。

## 📋 总结

通过明确区分页面内部数据格式和服务端数据格式，成功解决了退款申请提交失败的问题：

**关键改进**：
1. ✅ **类型安全** - 新增专门的提交类型定义
2. ✅ **单位转换** - 提交前正确转换金额单位
3. ✅ **数据一致性** - 确保前后端金额单位一致
4. ✅ **代码清晰** - 明确标注不同阶段的数据格式

**设计原则**：
- **页面内部**: 使用分为单位，便于精确计算和显示
- **服务端交互**: 使用元为单位，符合业务逻辑和后端期望
- **转换边界**: 在数据提交时进行单位转换

现在退款申请功能应该可以正常提交了！
