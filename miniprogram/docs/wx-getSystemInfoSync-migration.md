# wx.getSystemInfoSync 迁移指南

## 🎯 迁移目标

解决微信小程序中 `wx.getSystemInfoSync` API 的废弃警告：
```
wx.getSystemInfoSync is deprecated. Please use wx.getSystemSetting/wx.getAppAuthorizeSetting/wx.getDeviceInfo/wx.getWindowInfo/wx.getAppBaseInfo instead.
```

## 📋 迁移内容

### 1. 创建系统信息工具类 (`utils/systemInfo.ts`)

**功能特性：**
- 统一封装新的系统信息API
- 提供向后兼容的接口
- 支持缓存机制，提升性能
- 自动降级处理，确保兼容性
- 完整的错误处理

**API映射关系：**
| 原属性 | 新API | 说明 |
|--------|-------|------|
| `pixelRatio`, `screenWidth`, `screenHeight`, `windowWidth`, `windowHeight`, `statusBarHeight`, `safeArea` | `wx.getWindowInfo()` | 窗口和屏幕相关信息 |
| `SDKVersion`, `version`, `language`, `theme`, `host` | `wx.getAppBaseInfo()` | 应用基础信息 |
| `brand`, `model`, `system`, `platform`, `benchmarkLevel`, `abi`, `cpuType`, `memorySize` | `wx.getDeviceInfo()` | 设备基础信息 |
| `bluetoothEnabled`, `locationEnabled`, `wifiEnabled`, `deviceOrientation` | `wx.getSystemSetting()` | 系统设置信息 |
| `albumAuthorized`, `cameraAuthorized`, `locationAuthorized`, `microphoneAuthorized`, `notificationAuthorized`, 等 | `wx.getAppAuthorizeSetting()` | 应用授权设置 |

### 2. 修改的文件

#### 2.1 `pages/category/category.ts`
**修改内容：**
- 导入 `SystemInfoManager`
- 将 `wx.getSystemInfoSync()` 替换为 `SystemInfoManager.getSystemInfo()`

**用途：** 计算页面布局的安全区域和滚动区域高度

#### 2.2 `services/homepageService.ts`
**修改内容：**
- 导入 `SystemInfoManager`
- 将错误上报中的 `wx.getSystemInfoSync()` 替换为 `SystemInfoManager.getSystemInfo()`

**用途：** 错误上报时附带设备信息

#### 2.3 `utils/auth.ts`
**修改内容：**
- 导入 `SystemInfoManager`
- 新增 `checkNewProfileCapabilities()` 方法
- 使用 `SystemInfoManager.getAppBaseInfo()` 获取 SDK 版本

**用途：** 检查是否支持新的头像昵称功能

### 3. 新增的功能

#### 3.1 系统信息管理器 (`SystemInfoManager`)
```typescript
// 获取完整系统信息
const systemInfo = SystemInfoManager.getSystemInfo()

// 仅获取窗口信息
const windowInfo = SystemInfoManager.getWindowInfo()

// 仅获取应用基础信息
const appBaseInfo = SystemInfoManager.getAppBaseInfo()

// 仅获取设备信息
const deviceInfo = SystemInfoManager.getDeviceInfo()

// 检查新API支持情况
const support = SystemInfoManager.checkNewAPISupport()

// 版本比较
const result = SystemInfoManager.compareVersion('2.21.2', '2.21.1')

// 清除缓存
SystemInfoManager.clearCache()
```

#### 3.2 头像昵称功能检测
```typescript
// 检查新版头像昵称功能支持情况
const capabilities = AuthManager.checkNewProfileCapabilities()
console.log('头像选择支持:', capabilities.canChooseAvatar)
console.log('昵称编辑支持:', capabilities.canEditNickname)
```

### 4. 兼容性处理

#### 4.1 自动降级
当新API不可用时，自动降级到 `wx.getSystemInfoSync`：
```typescript
private fallbackToLegacyAPI(): SystemInfo {
  try {
    console.warn('使用降级方案: wx.getSystemInfoSync (已废弃)')
    const legacyInfo = wx.getSystemInfoSync()
    return legacyInfo as SystemInfo
  } catch (error) {
    // 返回默认值
    return defaultSystemInfo
  }
}
```

#### 4.2 错误处理
- 每个API调用都有独立的错误处理
- 失败时不影响其他信息的获取
- 提供合理的默认值

### 5. 性能优化

#### 5.1 缓存机制
- 5分钟缓存时间
- 避免重复API调用
- 可手动清除缓存

#### 5.2 按需获取
- 提供专门的方法获取特定类型信息
- 减少不必要的API调用

## 🧪 测试验证

### 测试文件
- `utils/systemInfoTest.ts` - 测试工具类
- `pages/test/test.*` - 测试页面

### 测试内容
1. **API支持检测** - 检查新API是否可用
2. **系统信息获取** - 验证信息获取是否正常
3. **窗口信息获取** - 验证布局相关信息
4. **应用基础信息获取** - 验证SDK版本等信息
5. **设备信息获取** - 验证设备相关信息
6. **版本比较功能** - 验证版本比较逻辑
7. **缓存功能** - 验证缓存机制

### 运行测试
```typescript
// 在测试页面或控制台运行
import SystemInfoTest from './utils/systemInfoTest'
SystemInfoTest.runAllTests()
```

## ✅ 迁移效果

### 解决的问题
1. ✅ 消除 `wx.getSystemInfoSync is deprecated` 警告
2. ✅ 使用官方推荐的新API
3. ✅ 保持原有功能完全兼容
4. ✅ 提升代码可维护性

### 性能提升
1. ✅ 缓存机制减少API调用
2. ✅ 按需获取减少不必要的信息
3. ✅ 更好的错误处理机制

### 兼容性保证
1. ✅ 自动降级到旧API
2. ✅ 完整的错误处理
3. ✅ 合理的默认值

## 🔧 使用建议

### 1. 推荐用法
```typescript
// ✅ 推荐：使用新的系统信息管理器
import SystemInfoManager from './utils/systemInfo'
const systemInfo = SystemInfoManager.getSystemInfo()

// ❌ 不推荐：直接使用已废弃的API
const systemInfo = wx.getSystemInfoSync()
```

### 2. 按需获取
```typescript
// 只需要窗口信息时
const windowInfo = SystemInfoManager.getWindowInfo()

// 只需要SDK版本时
const appBaseInfo = SystemInfoManager.getAppBaseInfo()
```

### 3. 错误处理
```typescript
try {
  const systemInfo = SystemInfoManager.getSystemInfo()
  // 使用系统信息
} catch (error) {
  console.error('获取系统信息失败:', error)
  // 使用默认值或显示错误提示
}
```

## 📝 注意事项

1. **基础库版本要求**
   - 新API从基础库 2.20.1 开始支持
   - 低版本会自动降级到旧API

2. **缓存策略**
   - 默认缓存5分钟
   - 可根据需要调整缓存时间
   - 重要操作前建议清除缓存

3. **错误处理**
   - 所有API调用都有错误处理
   - 失败时会尝试降级方案
   - 最终会提供默认值

4. **性能考虑**
   - 优先使用缓存的信息
   - 按需获取特定类型信息
   - 避免频繁调用API

## 🎉 总结

通过这次迁移，我们成功：
- 解决了废弃API的警告问题
- 提升了代码的现代化程度
- 保持了完全的向后兼容性
- 增强了错误处理和性能

所有原有功能保持不变，同时获得了更好的性能和可维护性。
