# 头像本地存储功能说明

## 功能概述

本功能实现了将微信小程序中获取的临时头像链接下载并存储到本地，以后直接从本地获取头像，避免临时链接过期导致头像无法显示的问题。

## 核心特性

### 1. 自动下载存储
- 用户选择头像后，自动下载临时头像文件
- 将头像保存到小程序本地存储空间
- 生成唯一的本地文件路径

### 2. 智能缓存管理
- 自动检查本地头像文件是否存在
- 支持缓存过期时间管理（默认30天）
- 提供缓存清理功能

### 3. 优雅降级
- 优先使用本地缓存头像
- 本地文件不存在时回退到临时URL
- 出错时使用默认头像

### 4. 用户友好
- 显示下载进度提示
- 提供头像缓存信息查看
- 支持手动清理缓存

## 核心组件

### 1. AvatarManager (utils/avatarManager.ts)
头像本地存储管理器，负责：
- 下载临时头像文件
- 保存到本地存储
- 管理头像缓存信息
- 清理过期缓存

### 2. AuthManager 增强
在原有的 AuthManager 基础上增加：
- `updateUserAvatar()` - 更新头像（集成本地存储）
- `getUserAvatarPath()` - 获取头像路径（优先本地）
- `clearUserAvatarCache()` - 清理头像缓存

### 3. Profile 页面增强
在我的页面中增加：
- 头像选择自动保存到本地
- 头像缓存信息查看
- 头像缓存清理功能

## 使用方法

### 1. 选择头像（自动保存）
```typescript
// 用户点击头像选择按钮
async onChooseAvatar(e: any) {
  const { avatarUrl } = e.detail
  if (avatarUrl) {
    // 自动下载并保存到本地
    await AuthManager.updateUserAvatar(avatarUrl)
  }
}
```

### 2. 获取头像路径
```typescript
// 获取头像路径（优先使用本地缓存）
const avatarPath = await AuthManager.getUserAvatarPath()
```

### 3. 清理头像缓存
```typescript
// 清理本地头像缓存
await AuthManager.clearUserAvatarCache()
```

### 4. 查看缓存信息
```typescript
import AvatarManager from '../../utils/avatarManager'

// 获取头像缓存信息
const cacheInfo = AvatarManager.getAvatarCacheInfo()
```

## 存储结构

### 1. 文件存储
```
{USER_DATA_PATH}/avatars/
├── avatar_user123_1752567890123.jpg
├── avatar_user456_1752567890456.jpg
└── ...
```

### 2. 缓存信息存储
```json
{
  "localPath": "{USER_DATA_PATH}/avatars/avatar_user123_1752567890123.jpg",
  "originalUrl": "http://tmp/wx123456789.jpg",
  "downloadTime": 1752567890123,
  "fileSize": 45678,
  "userId": "123"
}
```

## 配置参数

### AvatarManager 配置
```typescript
private readonly STORAGE_KEY = 'user_avatar_info'        // 缓存信息存储键
private readonly AVATAR_DIR = 'avatars'                  // 头像目录名
private readonly MAX_CACHE_SIZE = 50 * 1024 * 1024      // 最大缓存大小 50MB
private readonly CACHE_EXPIRE_TIME = 30 * 24 * 60 * 60 * 1000  // 缓存过期时间 30天
```

## 工作流程

### 头像选择和保存流程
1. **用户选择头像** → 触发 `onChooseAvatar` 事件
2. **临时显示** → 立即更新页面显示新头像
3. **下载文件** → 调用 `wx.downloadFile` 下载临时文件
4. **保存本地** → 将文件复制到本地存储目录
5. **更新记录** → 保存头像缓存信息到本地存储
6. **更新用户信息** → 更新用户信息中的头像路径

### 头像获取流程
1. **检查本地缓存** → 查看是否有本地头像文件
2. **验证文件存在** → 检查本地文件是否还存在
3. **检查过期时间** → 验证缓存是否过期
4. **返回路径** → 返回本地路径或回退到远程URL

## 错误处理

### 1. 下载失败
- 显示错误提示
- 保留临时URL作为备用
- 不影响正常使用

### 2. 存储失败
- 记录错误日志
- 回退到临时URL
- 提示用户重试

### 3. 文件丢失
- 自动清理无效记录
- 回退到备用URL
- 静默处理，不影响用户体验

## 性能优化

### 1. 异步处理
- 头像下载不阻塞UI
- 使用 Promise 处理异步操作
- 提供加载状态反馈

### 2. 缓存管理
- 自动清理过期缓存
- 限制最大缓存大小
- 避免重复下载

### 3. 错误恢复
- 多级回退机制
- 静默错误处理
- 保证功能可用性

## 测试功能

在我的页面提供了测试功能：
- 📷 **头像信息** - 查看当前头像缓存信息
- 🗑️ **清理缓存** - 手动清理头像缓存

## 注意事项

1. **存储空间**：头像文件占用本地存储空间，建议定期清理
2. **网络环境**：下载头像需要网络连接，离线时无法更新
3. **文件格式**：目前支持微信返回的图片格式（通常为JPG）
4. **用户隐私**：头像文件仅存储在用户设备本地，不会上传到服务器

## 最佳实践

1. **及时保存**：用户选择头像后立即下载保存
2. **优雅降级**：始终提供备用方案，确保头像能正常显示
3. **用户反馈**：提供清晰的状态提示和错误信息
4. **定期清理**：建议用户定期清理过期的头像缓存

这个功能大大提升了用户体验，避免了临时头像链接过期导致的显示问题，同时提供了完善的缓存管理机制。
