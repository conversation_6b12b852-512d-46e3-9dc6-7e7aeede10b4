# SKU属性选择功能实现说明

## 功能概述

实现了商品详情页面的SKU属性选择功能，支持根据服务端返回的SKU attributes字段进行属性分组展示，用户可以通过选择不同属性组合来选择对应的SKU。

## 接口数据格式

### 请求接口
```
GET /api/public/products/{productId}/with-skus
```

### 响应数据结构
```json
{
  "code": "SUCCESS",
  "message": "操作成功", 
  "data": {
    "product": { ... },
    "skus": [
      {
        "id": "101",
        "productId": "42",
        "skuCode": "JEH2HBWW84YT",
        "nameExtension": "1.56 × 钻立方铂金膜",
        "price": 100.00,
        "stockQuantity": 9,
        "attributes": {
          "膜层": "钻立方铂金膜",
          "折射率": "1.56"
        },
        "isActive": true,
        ...
      }
    ],
    "statistics": { ... }
  }
}
```

## 核心功能实现

### 1. 数据结构定义

```typescript
// SKU属性选项
interface SkuAttributeOption {
  value: string
  available: boolean // 是否有库存可选
  selected: boolean  // 是否被选中
}

// SKU属性组
interface SkuAttributeGroup {
  name: string
  options: SkuAttributeOption[]
}
```

### 2. 属性分组处理

- **processSkuAttributes()**: 将SKU的attributes字段解析为属性分组
- **processSkuAttributesWithSelection()**: 根据当前选择更新属性可用性
- **findMatchingSku()**: 根据选中的属性组合查找匹配的SKU

### 3. 用户交互

- **onAttributeSelect()**: 处理用户点击属性选项
- **onQuantityIncrease/Decrease()**: 数量选择
- **onQuantityInput/Blur()**: 数量输入

### 4. UI组件

#### 属性选择器
```xml
<view class="sku-attributes">
  <view wx:for="{{skuAttributeGroups}}" class="attribute-group">
    <view class="attribute-title">{{group.name}}</view>
    <view class="attribute-options">
      <view 
        wx:for="{{group.options}}" 
        class="attribute-option {{option.selected ? 'selected' : ''}} {{!option.available ? 'disabled' : ''}}"
        bindtap="onAttributeSelect"
        data-attribute-name="{{group.name}}"
        data-value="{{option.value}}"
      >
        <text>{{option.value}}</text>
        <view wx:if="{{!option.available}}" class="unavailable-mark">×</view>
      </view>
    </view>
  </view>
</view>
```

#### 数量选择器
```xml
<view class="quantity-selector">
  <view class="quantity-title">数量</view>
  <view class="quantity-controls">
    <view class="quantity-btn" bindtap="onQuantityDecrease">-</view>
    <input class="quantity-input" type="number" value="{{quantity}}" bindinput="onQuantityInput" />
    <view class="quantity-btn" bindtap="onQuantityIncrease">+</view>
  </view>
</view>
```

## 样式设计

### 属性选项样式
- 默认状态：浅灰色背景，灰色边框
- 选中状态：蓝色背景，蓝色边框，蓝色文字
- 禁用状态：灰色背景，降低透明度，显示"×"标记

### 数量选择器样式
- 按钮：60rpx × 60rpx，圆角边框
- 输入框：120rpx宽度，居中显示
- 禁用状态：降低透明度，灰色背景

## 测试方法

### 1. 直接访问商品详情页
```
/pages/product-detail/product-detail?productId=42&productName=测试SKU信息
```

### 2. 测试场景
- 选择不同属性组合
- 验证库存状态显示
- 测试数量选择功能
- 确认SKU切换正确性

### 3. 预期行为
- 属性按名称分组显示（如：折射率、膜层）
- 选择属性后自动匹配对应SKU
- 无库存的属性组合显示为禁用状态
- 价格和库存信息实时更新

## 兼容性说明

- 保留了原有的SKU选择方法以确保向后兼容
- 支持没有attributes字段的SKU（降级为原有显示方式）
- 自动处理属性名称排序和选项值排序

## 注意事项

1. 属性名称和值的排序可根据业务需求调整
2. 库存检查逻辑确保用户只能选择有效的SKU组合
3. 数量选择器会根据当前SKU的库存限制最大数量
4. 所有用户操作都有相应的错误提示和状态反馈
