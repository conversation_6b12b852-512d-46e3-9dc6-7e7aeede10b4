# 优惠券功能测试指南

## 功能概述

微信小程序下单页面现已支持选择多个优惠券，特别是邀请奖励的优惠券支持全部显示并多选（全部选上）。使用优惠券后最低为0元，不会为负数。

## 新增功能

### 1. 优惠券选择页面
- 路径：`pages/coupon-select/coupon-select`
- 功能：展示可用优惠券列表，支持多选
- 特色：邀请奖励优惠券支持一键全选

### 2. 下单页面优惠券集成
- 路径：`pages/checkout/checkout`
- 功能：显示优惠券选择入口，展示已选优惠券，计算优惠后价格

## 测试步骤

### 测试前准备
1. 确保后端优惠券接口正常工作
2. 确保用户有可用的优惠券（包括邀请奖励优惠券）
3. 准备测试商品和收货地址

### 测试用例

#### 用例1：优惠券选择页面基本功能
1. 进入下单页面
2. 点击"优惠券"选择区域
3. 验证优惠券列表正确显示
4. 验证邀请奖励优惠券和普通优惠券分类显示
5. 验证优惠券信息显示完整（金额、名称、使用条件、到期时间）

#### 用例2：邀请奖励优惠券全选功能
1. 在优惠券选择页面
2. 点击邀请奖励优惠券区域的"全选"按钮
3. 验证所有邀请奖励优惠券被选中
4. 再次点击验证取消全选功能
5. 验证选择状态正确更新

#### 用例3：多选优惠券功能
1. 选择多张不同类型的优惠券
2. 验证选择状态正确显示
3. 验证底部显示已选优惠券数量和预计优惠金额
4. 点击确定返回下单页面

#### 用例4：价格计算功能
1. 在下单页面选择优惠券后
2. 验证费用明细正确显示：
   - 商品金额（原价）
   - 优惠券优惠（负数，红色显示）
   - 实付款（最终金额）
3. 验证底部提交栏显示正确的实付金额
4. 验证优惠后金额不为负数（最低0元）

#### 用例5：优惠券移除功能
1. 在下单页面已选优惠券列表中
2. 点击优惠券右侧的"×"按钮
3. 验证优惠券被移除
4. 验证价格重新计算正确

#### 用例6：订单提交功能
1. 选择优惠券后提交订单
2. 验证订单创建请求包含优惠券信息
3. 验证支付金额为优惠后的最终金额
4. 验证订单创建成功

### 边界测试

#### 测试1：优惠券金额大于订单金额
1. 选择优惠金额大于订单金额的优惠券
2. 验证最终金额为0元，不为负数
3. 验证可以正常提交订单

#### 测试2：多张优惠券总优惠超过订单金额
1. 选择多张优惠券，总优惠金额超过订单金额
2. 验证系统正确计算，最终金额为0元
3. 验证优惠券使用顺序合理（按金额从大到小）

#### 测试3：不满足使用条件的优惠券
1. 验证不满足最低订单金额要求的优惠券显示为禁用状态
2. 验证过期优惠券不在可用列表中
3. 验证已使用的优惠券不在可用列表中

### 异常测试

#### 测试1：网络异常
1. 断网状态下进入优惠券选择页面
2. 验证显示加载失败提示
3. 恢复网络后验证可以重新加载

#### 测试2：数据异常
1. 模拟后端返回空数据
2. 验证显示"暂无可用优惠券"提示
3. 验证页面不会崩溃

## 预期结果

### 功能正常
- 优惠券选择页面正常显示和操作
- 邀请奖励优惠券全选功能正常
- 价格计算准确，不出现负数
- 订单提交成功，优惠券信息正确传递

### 用户体验良好
- 界面美观，操作流畅
- 提示信息清晰明确
- 加载状态和错误处理完善

## 注意事项

1. 测试时确保后端优惠券相关接口已实现
2. 注意测试不同类型优惠券的组合使用
3. 重点测试价格计算的准确性
4. 验证优惠券使用后的订单状态更新

## 相关文件

- 优惠券类型定义：`types/coupon.ts`
- 优惠券服务：`services/couponService.ts`
- 优惠券选择页面：`pages/coupon-select/`
- 下单页面：`pages/checkout/`
- 支付类型定义：`types/payment.ts`
