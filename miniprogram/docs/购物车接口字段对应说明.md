# 购物车接口字段对应说明

## 接口响应数据结构

根据 `/api/user/cart/items` 接口的实际响应数据：

```json
{
    "code": "SUCCESS",
    "message": "操作成功",
    "data": [
        {
            "id": 38,
            "userId": 42,
            "skuId": 91,
            "skuCode": "CDEZEM676WHR",
            "productId": 37,
            "productName": "测试0.01元",
            "productImageUrl": "https://cdn.seekeyes.cn/images/1753758556070_wy9ziqrbt.png?x-oss-process=image%2Fresize%2Cw_150%2Ch_150%2Cm_fill%2Fformat%2Cwebp",
            "skuAttributes": "颜色:超值",
            "unitPrice": 0.01,
            "quantity": 1,
            "subtotal": 0.01,
            "stockQuantity": 99,
            "inStock": true,
            "addedAt": "2025-08-06T15:08:13",
            "updatedAt": "2025-08-06T15:08:13"
        }
    ],
    "timestamp": 1754468559361,
    "success": true
}
```

## 字段对应关系

| 接口字段 | 类型 | 说明 | 前端使用字段 |
|---------|------|------|-------------|
| `id` | number | 购物车项ID | `CartItem.id` |
| `userId` | number | 用户ID | `CartItemDTO.userId` |
| `skuId` | number | SKU ID | `CartItem.skuId` |
| `skuCode` | string | SKU编码 | `CartItemDTO.skuCode` |
| `productId` | number | 商品ID | `CartItem.productId` |
| `productName` | string | 商品名称 | `CartItem.name` |
| `productImageUrl` | string | 商品图片URL | `CartItem.image` |
| `skuAttributes` | string | SKU属性描述 | `CartItem.sku` |
| `unitPrice` | number | 单价 | `CartItem.price` |
| `quantity` | number | 购买数量 | `CartItem.quantity` |
| `subtotal` | number | 小计金额 | `CartItem.subtotal` |
| `stockQuantity` | number | 库存数量 | `CartItem.stockQuantity` |
| `inStock` | boolean | 是否有库存 | `CartItem.inStock` |
| `addedAt` | string | 加入购物车时间 | `CartItemDTO.addedAt` |
| `updatedAt` | string | 最后更新时间 | `CartItemDTO.updatedAt` |

## 图片URL处理

### 问题
之前使用 `ImageHelper.processProductImageUrl()` 处理图片URL，但接口返回的已经是完整的CDN URL。

### 解决方案
直接使用接口返回的 `productImageUrl` 字段，无需额外处理：

```typescript
// 修改前
const imageUrl = ImageHelper.processProductImageUrl(dto.productImageUrl)

// 修改后
const imageUrl = dto.productImageUrl || '/images/default-avatar.png'
```

### 示例URL
```
https://cdn.seekeyes.cn/images/1753758556070_wy9ziqrbt.png?x-oss-process=image%2Fresize%2Cw_150%2Ch_150%2Cm_fill%2Fformat%2Cwebp
```

这个URL已经包含了：
- 完整的CDN域名
- 图片处理参数（缩放、格式转换等）
- 可以直接在前端使用

## 数据转换逻辑

在 `CartService.convertDTOToCartItem()` 方法中：

```typescript
return {
  id: dto.id || 0,
  name: dto.productName || '未知商品',
  sku: dto.skuAttributes || '',
  price: dto.unitPrice || 0,
  quantity: dto.quantity || 1,
  selected: true, // 默认选中，页面逻辑控制
  image: dto.productImageUrl || '/images/default-avatar.png',
  skuId: dto.skuId || 0,
  productId: dto.productId || 0,
  subtotal: dto.subtotal || 0,
  inStock: dto.inStock !== undefined ? dto.inStock : true,
  stockQuantity: dto.stockQuantity || 0,
  needPrescription: dto.productName ? (
    dto.productName.includes('镜片') || 
    dto.productName.includes('近视') || 
    dto.productName.includes('远视')
  ) : false
}
```

## 注意事项

1. **图片URL**：直接使用接口返回的URL，已经是完整的CDN地址
2. **价格格式**：接口返回的是数字类型，前端显示时需要格式化
3. **库存状态**：`inStock` 字段直接表示是否有库存
4. **时间格式**：`addedAt` 和 `updatedAt` 是ISO格式字符串
5. **默认选中**：新加载的购物车项默认为选中状态
