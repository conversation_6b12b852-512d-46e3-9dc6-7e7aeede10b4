# 首页配置接口文档

## 📋 **API概述**

提供首页配置的查询功能，支持获取所有首页配置项以及特定配置项的查询，供小程序/APP使用。接口自动支持租户隔离，无需权限验证。

## 🔗 **接口列表**

### 1. 获取首页配置

#### 基本信息
- **接口地址**: `GET /api/public/configs/homepage`
- **接口描述**: 通过租户ID+category查询所有的首页配置项
- **权限要求**: 无需权限，公开接口
- **请求方式**: GET

#### 请求参数
无需请求参数，租户ID通过请求头或上下文自动获取。

#### 响应参数
```typescript
interface SystemConfigDTO {
  id: number;                    // 配置ID
  tenantId: number;             // 租户ID
  configKey: string;            // 配置键名
  configValue: string;          // 配置值（JSON字符串或普通文本）
  configType: string;           // 配置类型：json/text/number/boolean
  category: string;             // 配置分类：homepage
  description: string;          // 配置描述
  createdAt: string;           // 创建时间
  updatedAt: string;           // 更新时间
}

interface ApiResponse {
  code: string;                 // 响应码：SUCCESS/ERROR
  message: string;              // 响应消息
  data: SystemConfigDTO[];      // 配置列表
  timestamp: string;            // 响应时间戳
}
```

#### 请求示例
```javascript
// 使用 fetch
fetch('/api/public/configs/homepage', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
    'X-Tenant-Id': '1'  // 租户ID（可选，通常由系统自动处理）
  }
})
.then(response => response.json())
.then(data => {
  if (data.code === 'SUCCESS') {
    console.log('首页配置:', data.data);
  } else {
    console.error('获取失败:', data.message);
  }
});

// 使用 axios
import axios from 'axios';

const getHomepageConfigs = async () => {
  try {
    const response = await axios.get('/api/public/configs/homepage');
    if (response.data.code === 'SUCCESS') {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('获取首页配置失败:', error);
    throw error;
  }
};
```

#### 响应示例
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "tenantId": 1,
      "configKey": "homepage.banner.config",
      "configValue": "{\"banners\":[{\"title\":\"春季新品上市\",\"imageUrl\":\"https://example.com/banner1.jpg\",\"sortOrder\":1}]}",
      "configType": "json",
      "category": "homepage",
      "description": "首页轮播图配置",
      "createdAt": "2024-01-01T10:00:00",
      "updatedAt": "2024-01-01T10:00:00"
    },
    {
      "id": 2,
      "tenantId": 1,
      "configKey": "announcement.information.config",
      "configValue": "欢迎来到我们的眼镜商城！新用户注册即享受9折优惠。",
      "configType": "text",
      "category": "homepage",
      "description": "首页公告信息配置",
      "createdAt": "2024-01-01T10:00:00",
      "updatedAt": "2024-01-01T10:00:00"
    },
    {
      "id": 3,
      "tenantId": 1,
      "configKey": "import.statement.config",
      "configValue": "{\"statements\":[{\"title\":\"质量保证\",\"ossUrl\":\"https://example.com/statement1.pdf\"}]}",
      "configType": "json",
      "category": "homepage",
      "description": "重点说明配置",
      "createdAt": "2024-01-01T10:00:00",
      "updatedAt": "2024-01-01T10:00:00"
    }
  ],
  "timestamp": "2024-01-01T10:00:00"
}
```

## 🎨 **前端组件实现**

### Vue 3 + TypeScript 示例

#### 1. API服务
```typescript
// services/homepageConfigService.ts
import { http } from '@/utils/http';

export interface SystemConfigDTO {
  id: number;
  tenantId: number;
  configKey: string;
  configValue: string;
  configType: string;
  category: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export class HomepageConfigService {
  // 获取所有首页配置
  static async getHomepageConfigs(): Promise<SystemConfigDTO[]> {
    const response = await http.get('/api/public/configs/homepage');
    return response.data || [];
  }

  // 解析JSON配置
  static parseJsonConfig(configValue: string): any {
    try {
      return JSON.parse(configValue);
    } catch (error) {
      console.error('解析配置JSON失败:', error);
      return null;
    }
  }

  // 根据配置键名查找配置
  static findConfigByKey(configs: SystemConfigDTO[], key: string): SystemConfigDTO | null {
    return configs.find(config => config.configKey === key) || null;
  }
}
```

#### 2. 首页组件
```vue
<template>
  <div class="homepage">
    <!-- 轮播图 -->
    <div v-if="bannerConfig" class="banner-section">
      <swiper :banners="bannerConfig.banners" />
    </div>

    <!-- 公告信息 -->
    <div v-if="announcementText" class="announcement-section">
      <div class="announcement-text">{{ announcementText }}</div>
    </div>

    <!-- 重点说明 -->
    <div v-if="statementConfig" class="statement-section">
      <statement-list :statements="statementConfig.statements" />
    </div>

    <!-- 分类展示 -->
    <div v-if="categoryConfig" class="category-section">
      <category-showcase :categories="categoryConfig.categories" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { HomepageConfigService, SystemConfigDTO } from '@/services/homepageConfigService';

// 响应式数据
const configs = ref<SystemConfigDTO[]>([]);
const bannerConfig = ref<any>(null);
const announcementText = ref<string>('');
const statementConfig = ref<any>(null);
const categoryConfig = ref<any>(null);

// 加载首页配置
const loadHomepageConfigs = async () => {
  try {
    const configList = await HomepageConfigService.getHomepageConfigs();
    configs.value = configList;
    
    // 解析各种配置
    parseConfigs(configList);
  } catch (error) {
    console.error('加载首页配置失败:', error);
  }
};

// 解析配置数据
const parseConfigs = (configList: SystemConfigDTO[]) => {
  // 轮播图配置
  const bannerConfigItem = HomepageConfigService.findConfigByKey(configList, 'homepage.banner.config');
  if (bannerConfigItem && bannerConfigItem.configType === 'json') {
    bannerConfig.value = HomepageConfigService.parseJsonConfig(bannerConfigItem.configValue);
  }

  // 公告信息
  const announcementConfigItem = HomepageConfigService.findConfigByKey(configList, 'announcement.information.config');
  if (announcementConfigItem && announcementConfigItem.configType === 'text') {
    announcementText.value = announcementConfigItem.configValue;
  }

  // 重点说明配置
  const statementConfigItem = HomepageConfigService.findConfigByKey(configList, 'import.statement.config');
  if (statementConfigItem && statementConfigItem.configType === 'json') {
    statementConfig.value = HomepageConfigService.parseJsonConfig(statementConfigItem.configValue);
  }

  // 分类展示配置
  const categoryConfigItem = HomepageConfigService.findConfigByKey(configList, 'homepage.categories.showcase');
  if (categoryConfigItem && categoryConfigItem.configType === 'json') {
    categoryConfig.value = HomepageConfigService.parseJsonConfig(categoryConfigItem.configValue);
  }
};

// 组件挂载时加载配置
onMounted(() => {
  loadHomepageConfigs();
});
</script>

<style scoped>
.homepage {
  padding: 0;
}

.banner-section {
  margin-bottom: 20px;
}

.announcement-section {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 10px 15px;
  margin: 10px 15px;
}

.announcement-text {
  color: #856404;
  font-size: 14px;
  line-height: 1.5;
}

.statement-section,
.category-section {
  margin: 20px 0;
}
</style>
```

### React + TypeScript 示例

#### 1. API服务
```typescript
// services/homepageConfigService.ts
import { request } from '@/utils/request';

export interface SystemConfigDTO {
  id: number;
  tenantId: number;
  configKey: string;
  configValue: string;
  configType: string;
  category: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export const homepageConfigService = {
  // 获取所有首页配置
  async getHomepageConfigs(): Promise<SystemConfigDTO[]> {
    const response = await request.get('/api/public/configs/homepage');
    return response.data || [];
  },

  // 解析JSON配置
  parseJsonConfig(configValue: string): any {
    try {
      return JSON.parse(configValue);
    } catch (error) {
      console.error('解析配置JSON失败:', error);
      return null;
    }
  },

  // 根据配置键名查找配置
  findConfigByKey(configs: SystemConfigDTO[], key: string): SystemConfigDTO | null {
    return configs.find(config => config.configKey === key) || null;
  }
};
```

#### 2. 首页组件
```tsx
// components/Homepage.tsx
import React, { useState, useEffect } from 'react';
import { homepageConfigService, SystemConfigDTO } from '@/services/homepageConfigService';

const Homepage: React.FC = () => {
  const [configs, setConfigs] = useState<SystemConfigDTO[]>([]);
  const [bannerConfig, setBannerConfig] = useState<any>(null);
  const [announcementText, setAnnouncementText] = useState<string>('');
  const [statementConfig, setStatementConfig] = useState<any>(null);
  const [categoryConfig, setCategoryConfig] = useState<any>(null);

  // 加载首页配置
  const loadHomepageConfigs = async () => {
    try {
      const configList = await homepageConfigService.getHomepageConfigs();
      setConfigs(configList);
      parseConfigs(configList);
    } catch (error) {
      console.error('加载首页配置失败:', error);
    }
  };

  // 解析配置数据
  const parseConfigs = (configList: SystemConfigDTO[]) => {
    // 轮播图配置
    const bannerConfigItem = homepageConfigService.findConfigByKey(configList, 'homepage.banner.config');
    if (bannerConfigItem && bannerConfigItem.configType === 'json') {
      setBannerConfig(homepageConfigService.parseJsonConfig(bannerConfigItem.configValue));
    }

    // 公告信息
    const announcementConfigItem = homepageConfigService.findConfigByKey(configList, 'announcement.information.config');
    if (announcementConfigItem && announcementConfigItem.configType === 'text') {
      setAnnouncementText(announcementConfigItem.configValue);
    }

    // 重点说明配置
    const statementConfigItem = homepageConfigService.findConfigByKey(configList, 'import.statement.config');
    if (statementConfigItem && statementConfigItem.configType === 'json') {
      setStatementConfig(homepageConfigService.parseJsonConfig(statementConfigItem.configValue));
    }

    // 分类展示配置
    const categoryConfigItem = homepageConfigService.findConfigByKey(configList, 'homepage.categories.showcase');
    if (categoryConfigItem && categoryConfigItem.configType === 'json') {
      setCategoryConfig(homepageConfigService.parseJsonConfig(categoryConfigItem.configValue));
    }
  };

  useEffect(() => {
    loadHomepageConfigs();
  }, []);

  return (
    <div className="homepage">
      {/* 轮播图 */}
      {bannerConfig && (
        <div className="banner-section">
          <Swiper banners={bannerConfig.banners} />
        </div>
      )}

      {/* 公告信息 */}
      {announcementText && (
        <div className="announcement-section">
          <div className="announcement-text">{announcementText}</div>
        </div>
      )}

      {/* 重点说明 */}
      {statementConfig && (
        <div className="statement-section">
          <StatementList statements={statementConfig.statements} />
        </div>
      )}

      {/* 分类展示 */}
      {categoryConfig && (
        <div className="category-section">
          <CategoryShowcase categories={categoryConfig.categories} />
        </div>
      )}
    </div>
  );
};

export default Homepage;
```

## 🎯 **最佳实践**

### 1. 错误处理
```typescript
// 统一错误处理
export const handleApiError = (error: any) => {
  console.error('API Error:', error);
  
  if (error.response?.status === 500) {
    // 服务器错误
    message.error('服务器错误，请稍后重试');
  } else {
    // 其他错误
    message.error(error.message || '操作失败');
  }
};

// 在组件中使用
const loadHomepageConfigs = async () => {
  try {
    const configList = await HomepageConfigService.getHomepageConfigs();
    // 处理成功逻辑
  } catch (error) {
    handleApiError(error);
  }
};
```

### 2. 缓存策略
```typescript
// 配置缓存服务
class ConfigCacheService {
  private static cache = new Map<string, { data: any; timestamp: number }>();
  private static CACHE_DURATION = 5 * 60 * 1000; // 5分钟

  static async getHomepageConfigs(): Promise<SystemConfigDTO[]> {
    const cacheKey = 'homepage_configs';
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }

    const data = await HomepageConfigService.getHomepageConfigs();
    this.cache.set(cacheKey, { data, timestamp: Date.now() });
    return data;
  }

  static clearCache() {
    this.cache.clear();
  }
}
```

### 3. 类型安全
```typescript
// 定义配置类型
export interface BannerConfig {
  banners: Array<{
    title: string;
    imageUrl: string;
    sortOrder: number;
  }>;
}

export interface StatementConfig {
  statements: Array<{
    title: string;
    ossUrl: string;
    url?: string;
    sortOrder: number;
  }>;
}

// 类型安全的配置解析
export const parseTypedConfig = <T>(configValue: string, defaultValue: T): T => {
  try {
    return JSON.parse(configValue) as T;
  } catch (error) {
    console.error('解析配置失败:', error);
    return defaultValue;
  }
};
```

## 🔧 **调试和测试**

### 1. 接口测试
```bash
# 使用 curl 测试
curl -X GET "http://localhost:8080/api/public/configs/homepage" \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: 1"

# 使用 Postman 或其他API测试工具
GET http://localhost:8080/api/public/configs/homepage
Headers:
  Content-Type: application/json
  X-Tenant-Id: 1
```

### 2. 前端调试
```typescript
// 开发环境调试
if (process.env.NODE_ENV === 'development') {
  console.log('首页配置数据:', configs);
  console.log('轮播图配置:', bannerConfig);
  console.log('公告信息:', announcementText);
}
```

## 📝 **注意事项**

1. **租户隔离**: 接口自动支持租户隔离，确保只能获取当前租户的配置
2. **配置类型**: 注意区分 `json` 和 `text` 类型的配置，需要相应的解析处理
3. **错误处理**: 建议实现统一的错误处理机制
4. **缓存策略**: 配置数据变化不频繁，建议实现适当的缓存
5. **性能优化**: 大量配置时考虑分页或按需加载
6. **安全性**: 虽然是公开接口，但仍需注意数据安全和访问控制

## 🔗 **相关接口**

- [首页轮播图配置API](./首页轮播图配置API接入文档.md)
- [首页公告信息接口](./首页公告信息接口文档.md)
- [重点说明配置API](./重点说明配置API接入文档.md)
