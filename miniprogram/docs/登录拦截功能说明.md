# 登录拦截功能说明

## 功能概述

本功能实现了统一拦截登录态失效的错误码（`LOGIN_EXPIRED` 和 `NOT_LOGIN`），当检测到这些错误码时，会自动弹出登录界面让用户重新登录，登录成功后会自动刷新当前页面并重新发起原始请求。

## 支持的错误码

- `LOGIN_EXPIRED`: 登录态已过期
- `NOT_LOGIN`: 用户未登录

## 服务端响应格式

服务端需要返回如下格式的响应：

```json
{
  "code": "LOGIN_EXPIRED",
  "message": "登录态已过期，请重新登录",
  "data": null,
  "timestamp": 1751949677973,
  "success": false
}
```

或

```json
{
  "code": "NOT_LOGIN",
  "message": "用户未登录，请先登录",
  "data": null,
  "timestamp": 1751949677973,
  "success": false
}
```

## 功能特性

### 1. 自动拦截
- 所有通过 `RequestManager` 发送的请求都会自动检测登录态失效
- 支持 HTTP 状态码 401 和业务层面的错误码拦截

### 2. 智能弹窗
- 避免重复弹窗：如果已经有登录弹窗在显示，后续的请求会等待当前登录完成
- 用户友好：提供"立即登录"和"稍后再说"两个选项

### 3. 自动重试
- 登录成功后会自动重新发起原始请求
- 无需用户手动刷新页面或重新操作

### 4. 页面刷新
- 登录成功后会自动刷新当前页面的数据
- 支持调用页面的 `onShow`、`refreshData`、`refreshLoginStatus` 方法

## 核心组件

### 1. LoginManager (utils/loginManager.ts)
全局登录状态管理器，负责：
- 检测登录态失效
- 处理登录弹窗逻辑
- 管理待处理的请求队列
- 刷新页面数据

### 2. RequestManager (utils/request.ts)
请求管理器，已集成登录拦截功能：
- 自动检测响应中的登录态失效
- 调用 LoginManager 处理登录流程
- 自动重试失败的请求

### 3. 登录页面 (pages/login/login)
专门的登录弹窗页面，提供：
- 微信快速登录
- 用户友好的界面
- 登录状态反馈

## 使用方法

### 1. 正常使用 RequestManager
```typescript
import RequestManager from '../../utils/request'

// 发送请求，登录拦截会自动处理
const result = await RequestManager.get('/api/user/profile')
```

### 2. 页面支持自动刷新
在页面中添加刷新方法（可选）：

```typescript
Page({
  // 页面显示时会被自动调用
  onShow() {
    this.loadData()
  },

  // 自定义刷新方法，登录成功后会被自动调用
  refreshData() {
    this.loadUserInfo()
    this.loadOrders()
  },

  // 登录状态刷新方法，登录成功后会被自动调用
  refreshLoginStatus() {
    this.setData({ isLoggedIn: AuthManager.isLoggedIn() })
  }
})
```

## 测试功能

在开发环境中，可以使用测试功能验证登录拦截是否正常工作：

### 1. 在我的页面测试
进入"我的"页面，可以看到以下测试按钮：
- 🧪 测试登录拦截：运行完整的测试套件
- ⏰ 测试登录过期：模拟登录过期场景
- 🚫 测试未登录：模拟未登录场景

### 2. 编程方式测试
```typescript
import LoginInterceptorTester from '../../utils/testLoginInterceptor'

// 运行所有测试
await LoginInterceptorTester.runAllTests()

// 测试特定场景
await LoginInterceptorTester.testLoginExpiredInterception()
await LoginInterceptorTester.testNotLoginInterception()
```

## 工作流程

1. **发送请求** → RequestManager 发送 API 请求
2. **检测响应** → 检查响应中是否包含登录态失效错误码
3. **触发拦截** → 如果检测到失效，调用 LoginManager 处理
4. **显示登录弹窗** → 提示用户重新登录
5. **用户登录** → 用户选择微信快速登录
6. **登录成功** → 保存新的登录凭证
7. **重试请求** → 自动重新发起原始请求
8. **刷新页面** → 调用页面刷新方法更新数据

## 注意事项

1. **服务端配置**：确保服务端返回正确的错误码格式
2. **网络异常**：网络错误不会触发登录拦截，只处理业务层面的登录态失效
3. **并发请求**：多个并发请求遇到登录失效时，只会显示一个登录弹窗
4. **用户取消**：如果用户选择"稍后再说"，原始请求会被拒绝

## 自定义配置

如果需要自定义错误码或行为，可以修改 `LoginManager` 中的相关方法：

```typescript
// 自定义错误码检测
isLoginExpired(response: any): boolean {
  const code = response.code
  return code === 'LOGIN_EXPIRED' || 
         code === 'NOT_LOGIN' || 
         code === 'CUSTOM_LOGIN_ERROR' // 添加自定义错误码
}
```
