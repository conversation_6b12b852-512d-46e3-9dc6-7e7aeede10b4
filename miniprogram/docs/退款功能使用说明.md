# 退款功能使用说明

## 快速开始

### 1. 测试退款功能
1. 打开小程序，进入"我的"页面
2. 点击"测试退款功能"按钮
3. 在测试页面可以测试各个退款功能

### 2. 正常使用流程

#### 申请退款
1. 进入"我的订单"页面
2. 找到需要退款的订单
3. 点击"申请退款"按钮
4. 按照页面提示填写退款信息
5. 提交申请

#### 查看退款状态
1. 进入"我的"页面
2. 点击"退款/售后"
3. 查看所有退款申请的状态

#### 填写物流信息（退货退款）
1. 在退款详情页面
2. 当状态为"已同意"时
3. 填写退货物流信息
4. 提交物流单号

## 功能说明

### 退款类型
- **仅退款**：不需要退货，直接退款
- **退货退款**：需要将商品寄回后退款

### 退款状态
- **待审核**：等待商家审核
- **已同意**：商家同意退款申请
- **已拒绝**：商家拒绝退款申请
- **用户寄回中**：用户已寄出退货
- **商家已收货**：商家确认收到退货
- **已退款**：退款已完成
- **已取消**：用户取消申请

### 可用操作
- **取消申请**：在待审核状态下可以取消
- **填写物流**：在已同意状态下填写退货物流
- **查看详情**：查看退款申请的详细信息

## 注意事项

1. **退款资格**：只有符合条件的订单才能申请退款
2. **退款金额**：不能超过订单实际支付金额
3. **退款数量**：不能超过订单中商品的可退数量
4. **证据图片**：最多可上传9张图片作为退款凭证
5. **物流信息**：退货退款必须填写真实的物流信息

## 常见问题

### Q: 为什么我的订单不能申请退款？
A: 可能的原因：
- 订单状态不支持退款
- 商品不支持退款
- 已超过退款期限

### Q: 退款多久能到账？
A: 退款处理时间取决于：
- 商家审核时间
- 退货物流时间（如适用）
- 支付平台处理时间

### Q: 如何查看退款进度？
A: 在退款详情页面可以查看当前状态和处理进度

## 开发者说明

### 接口依赖
确保以下接口正常工作：
- `/api/refund/check-eligibility/{orderId}` - 检查退款资格
- `/api/refund/reasons` - 获取退款原因
- `/api/refund/create` - 创建退款申请
- `/api/refund/list` - 获取退款列表
- `/api/refund/detail/{refundId}` - 获取退款详情
- `/api/refund/shipping` - 提交物流信息
- `/api/refund/cancel/{refundId}` - 取消退款申请
- `/api/refund/return-address` - 获取退货地址

### 页面路径
- 退款申请：`/pages/refund-apply/refund-apply?orderId={订单ID}`
- 退款列表：`/pages/orders/orders?status=refund_afterSale`（已集成到订单页面）
- 退款详情：`/pages/refund-detail/refund-detail?refundId={退款ID}`
- 功能测试：`/pages/refund-test/refund-test`

### 数据格式
所有金额以分为单位存储和传输，页面显示时自动转换为元。
