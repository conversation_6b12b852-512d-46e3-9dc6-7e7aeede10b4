<!--components/product-item/product-item.wxml-->
<view class="product-item">
  <image class="product-image" src="{{product.productImageUrl}}" mode="aspectFill"></image>
  <view class="product-info">
    <text class="product-name">{{product.productName}}</text>
    <view class="product-spec-container">
      <text
        wx:for="{{attributesList}}"
        wx:key="*this"
        class="product-spec"
      >
        {{item}}
      </text>
    </view>
  </view>
  <view class="product-right">
    <text class="product-price">¥{{displayPrice}}</text>
    <text class="product-qty">×{{product.quantity}}</text>
  </view>
</view>
