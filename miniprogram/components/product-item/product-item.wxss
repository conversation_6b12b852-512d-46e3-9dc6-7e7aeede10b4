/* components/product-item/product-item.wxss */

.product-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 120rpx;
}

.product-name {
  font-size: 28rpx;
  color: #1f2937;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-spec-container {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  margin-bottom: 8rpx;
}

.product-spec {
  font-size: 24rpx;
  color: #999;
  line-height: 1.3;
}

.product-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  min-height: 120rpx;
  margin-left: 16rpx;
  flex-shrink: 0;
}

.product-price {
  font-size: 28rpx;
  font-weight: 500;
  color: #ff3b30;
  margin-bottom: 8rpx;
}

.product-qty {
  font-size: 24rpx;
  color: #9ca3af;
}
