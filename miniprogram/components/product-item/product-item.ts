// components/product-item/product-item.ts

/**
 * 商品项组件 - 可复用的商品显示组件
 * 用于订单页面、确认订单页面等场景
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 商品数据
    product: {
      type: Object,
      value: {}
    },
    // 显示模式：'order' | 'checkout'
    mode: {
      type: String,
      value: 'order'
    },
    // 是否显示总价（订单模式下显示订单总价，结算模式下显示单价）
    showTotalPrice: {
      type: Boolean,
      value: false
    },
    // 订单总金额（仅在订单模式下使用）
    orderTotalAmount: {
      type: Number,
      value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    attributesList: [] as string[],
    displayPrice: '0.00'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 分割属性字符串为数组 - 与WXS版本保持一致
     * @param attributes 属性字符串，如 "折射率：1.56，膜层：舒缓系列"
     */
    splitAttributes(attributes: string): string[] {
      if (!attributes || typeof attributes !== 'string' || attributes.trim() === '') {
        return []
      }

      const trimmed = attributes.trim()

      // 首先尝试中文逗号分割
      if (trimmed.indexOf('，') > -1) {
        return trimmed.split('，').filter(attr => attr && attr.trim()).map(attr => attr.trim())
      }

      // 然后尝试英文逗号分割
      if (trimmed.indexOf(',') > -1) {
        return trimmed.split(',').filter(attr => attr && attr.trim()).map(attr => attr.trim())
      }

      // 尝试分号分割
      if (trimmed.indexOf(';') > -1 || trimmed.indexOf('；') > -1) {
        const sep = trimmed.indexOf(';') > -1 ? ';' : '；'
        return trimmed.split(sep).filter(attr => attr && attr.trim()).map(attr => attr.trim())
      }

      // 尝试 × 符号分割（用于 skuNameExtension 格式）
      if (trimmed.indexOf('×') > -1) {
        return trimmed.split('×').filter(attr => attr && attr.trim()).map(attr => attr.trim())
      }

      // 尝试英文 x 符号分割
      if (trimmed.indexOf(' x ') > -1) {
        return trimmed.split(' x ').filter(attr => attr && attr.trim()).map(attr => attr.trim())
      }

      // 如果没有找到分隔符，返回单个元素数组
      return [trimmed]
    },

    /**
     * 格式化金额显示
     * @param amount 金额（分为单位）
     */
    formatAmount(amount: number): string {
      if (typeof amount !== 'number') return '0.00'
      return (amount / 100).toFixed(2)
    },

    /**
     * 获取显示的价格
     */
    getDisplayPrice(): string {
      const { product, mode, showTotalPrice, orderTotalAmount } = this.properties

      if (mode === 'order' && showTotalPrice) {
        // 订单模式显示订单总价
        return this.formatAmount(orderTotalAmount)
      } else if (mode === 'checkout') {
        // 结算模式显示单价
        return this.formatAmount(product.unitPrice || 0)
      } else {
        // 默认显示商品单价
        return this.formatAmount(product.unitPrice || 0)
      }
    },

    /**
     * 更新组件数据
     */
    updateComponentData() {
      const { product } = this.properties

      // 处理属性列表
      const attributesList = this.splitAttributes(product.skuAttributes || '')

      // 处理显示价格
      const displayPrice = this.getDisplayPrice()

      this.setData({
        attributesList,
        displayPrice
      })
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放入页面节点树后执行
      this.updateComponentData()
    },

    detached() {
      // 组件实例被从页面节点树移除后执行
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'product, mode, showTotalPrice, orderTotalAmount': function() {
      // 当属性发生变化时更新组件数据
      this.updateComponentData()
    }
  }
})
