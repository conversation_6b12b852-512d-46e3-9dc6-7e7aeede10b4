Component({
  properties: {
    // 样式类名，用于不同页面的样式定制
    styleClass: {
      type: String,
      value: ''
    }
  },
  data: {},
  methods: {
    onSearchInput(e: any) {
      const value = e.detail.value
      console.log('Search input:', value)
      // 暂时不在输入时立即搜索，等用户确认后再搜索
    },

    onSearchConfirm(e: any) {
      const value = e.detail.value
      console.log('Search confirm:', value)

      // 实现搜索功能
      if (value.trim()) {
        this.performSearch(value.trim())
      }
    },

    /**
     * 执行商品搜索
     */
    async performSearch(keyword: string) {
      try {
        console.log('开始搜索商品:', keyword)

        // 将关键词存储到全局数据中，避免URL编码问题
        getApp().globalData.searchKeyword = keyword

        // 直接跳转到商品列表页面，让商品列表页面处理搜索逻辑
        // 避免重复调用搜索API
        wx.navigateTo({
          url: `/pages/products/products?searchResults=true&timestamp=${Date.now()}`
        })
      } catch (error) {
        console.error('搜索跳转失败:', error)
        wx.showToast({
          title: '搜索失败，请重试',
          icon: 'none',
          duration: 2000
        })
      }
    },
  }
})
