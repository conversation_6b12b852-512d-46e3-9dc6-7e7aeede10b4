
/* components/invitation-rewards/invitation-rewards.wxss */

/* 邀请奖励横幅总容器 */
.invitation-banner {
  background-color: #e60012; /* 主题红色背景 */
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  margin: 0; /* 移除内层边距，由外层invitation-section统一控制 */
  width: 100%; /* 占满外层容器宽度，与其他组件保持一致 */
  box-sizing: border-box;
}

/* 顶部标题栏 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  color: white;
}

.header .title {
  font-size: 44rpx;
  font-weight: bold;
  margin: 0;
}

.header .share-link {
  font-size: 30rpx;
  color: white;
  transition: opacity 0.3s;
}

.header .share-link:active {
  opacity: 0.8;
}

/* 白色背景容器，用于包裹三个奖励卡片 */
.rewards-wrapper {
  background-color: #ffffff; /* 设置为白色背景 */
  margin: 0 32rpx 32rpx 32rpx; /* 与红色背景的边距显著增大 */
  border-radius: 12rpx;      /* 圆角 */
  padding: 32rpx;            /* 内部留白显著增大 */
  box-sizing: border-box;
}

/* 奖励卡片区域容器 */
.rewards-container {
  display: flex;
  justify-content: space-between;
  gap: 24rpx; /* 卡片之间的间距显著加大 */
  flex-wrap: nowrap; /* 保持横向排列 */
  width: 100%;
  box-sizing: border-box;
}

/* 单个奖励卡片 */
.reward-card {
  flex: 1;
  min-width: 0; /* 允许卡片收缩 */
  background-color: #fff9f0; /* 卡片的米白色背景 */
  border-radius: 12rpx;
  padding: 28rpx 20rpx; /* 内边距显著加大 */
  text-align: center; /* 卡片内容居中显示 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.reward-card:active {
  transform: scale(0.98);
}

/* 奖励内容区域 */
.reward-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 16rpx;
}

/* 卡片标题 */
.reward-title {
  font-size: 22rpx;
  font-weight: 600;
  color: #333;
  margin: 0 0 8rpx 0;
  display: block;
  line-height: 1.2;
  word-break: break-all;
}

/* 奖励金额/内容 */
.reward-amount {
  font-size: 28rpx;
  font-weight: bold;
  color: #e60012;
  margin: 0 0 6rpx 0;
  display: block;
  line-height: 1.2;
  word-break: break-all;
}

/* 奖励描述 */
.reward-description {
  font-size: 22rpx; /* 增大描述字号 */
  color: #888;
  margin: 0 0 12rpx 0;
  min-height: 1.2em;
  display: block;
  line-height: 1.4; /* 提升可读性 */
  word-break: break-all;
}

/* 奖励项：一张卡片 + 底部按钮的纵向组合 */
.reward-item {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  align-items: center; /* 让下方按钮在自身列中居中 */
}

/* 自定义按钮样式 */
.custom-button {
  background-color: #e60012;
  color: #ffffff;
  border-radius: 20rpx;
  padding: 12rpx 16rpx;
  font-size: 22rpx;
  font-weight: bold;
  text-align: center;
  width: 80%; /* 按钮宽度小于卡片 */
  margin: 12rpx auto 0; /* 居中并与卡片保持间距 */
  transition: all 0.3s ease;
  box-sizing: border-box;
  cursor: pointer;
}

.custom-button:active {
  opacity: 0.85;
  transform: scale(0.98);
}

.button-text {
  color: #ffffff;
  font-size: 22rpx;
  font-weight: bold;
}


/* 响应式设计：保持横向排列，只调整字体大小 */
@media (max-width: 600px) {
  .invitation-banner {
    margin: 0; /* 小屏幕下也移除内层边距 */
  }

  .header {
    padding: 20rpx 24rpx;
  }

  .header .title {
    font-size: 38rpx;
  }
  .header .share-link {
    font-size: 30rpx;
  }

  .rewards-wrapper {
    margin: 0 20rpx 24rpx 20rpx;
    padding: 20rpx;
  }

  /* 在小屏幕上进一步缩小字体和间距 */
  .reward-title {
    font-size: 28rpx;
  }
  .reward-amount {
    font-size: 30rpx;
  }
  .reward-description {
    font-size: 20rpx;
  }

  /* 小屏幕下调整间距 */
  .rewards-container {
    gap: 16rpx;
  }
  .reward-card {
    padding: 20rpx 12rpx;
  }
  .custom-button {
    font-size: 20rpx;
    padding: 10rpx 12rpx;
  }
}
