<!--components/invitation-rewards/invitation-rewards.wxml-->
<view class="invitation-banner">
  <!-- 顶部标题栏 -->
  <view class="header">
    <text class="title">邀请有礼</text>
    <view class="share-link" bindtap="onShareReward">
      <text>分享赢好礼 ></text>
    </view>
  </view>

  <!-- 白色背景容器，包裹三个奖励卡片 -->
  <view class="rewards-wrapper">
    <view class="rewards-container">
      <!-- 奖励一：填写邀请码奖励 -->
      <view class="reward-item">
        <view class="reward-card">
          <view class="reward-content">
            <text class="reward-title">{{rewardData.fillCodeReward.title}}</text>
            <text class="reward-amount">{{rewardData.fillCodeReward.amount}}</text>
            <text class="reward-description">{{rewardData.fillCodeReward.description}}</text>
          </view>
        </view>
        <view class="custom-button" bindtap="onFillCodeReward">
          <text class="button-text">{{rewardData.fillCodeReward.buttonText}}</text>
        </view>
      </view>

      <!-- 奖励二：邀请奖励 -->
      <view class="reward-item">
        <view class="reward-card">
          <view class="reward-content">
            <text class="reward-title">{{rewardData.inviteReward.title}}</text>
            <text class="reward-amount">{{rewardData.inviteReward.amount}}</text>
            <text class="reward-description">{{rewardData.inviteReward.description}}</text>
          </view>
        </view>
        <view class="custom-button" bindtap="onInviteReward">
          <text class="button-text">{{rewardData.inviteReward.buttonText}}</text>
        </view>
      </view>

      <!-- 奖励三：邀请注册奖励 -->
      <view class="reward-item">
        <view class="reward-card">
          <view class="reward-content">
            <text class="reward-title">{{rewardData.registerReward.title}}</text>
            <text class="reward-amount">{{rewardData.registerReward.amount}}</text>
            <text class="reward-description">{{rewardData.registerReward.description}}</text>
          </view>
        </view>
        <view class="custom-button" bindtap="onRegisterReward">
          <text class="button-text">{{rewardData.registerReward.buttonText}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
