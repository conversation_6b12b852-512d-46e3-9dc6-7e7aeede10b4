// components/invitation-rewards/invitation-rewards.ts
import InvitationService, { InvitationReward } from '../../services/invitationService'

/**
 * 奖励状态枚举
 */
const REWARD_STATUS = {
  AVAILABLE: 'available',    // 可领取
  USED: 'used',             // 已使用
  VIP: 'vip',               // VIP专享
  DISABLED: 'disabled'      // 不可用
} as const

/**
 * 按钮状态枚举
 */
const BUTTON_STATUS = {
  AVAILABLE: 'available',   // 可点击
  USED: 'used',            // 已使用
  VIP: 'vip',              // VIP专享
  DISABLED: 'disabled'     // 不可用
} as const

/**
 * 奖励类型常量
 */
const REWARD_TYPES = {
  FILL_CODE: 'FILL_CODE',           // 填写邀请码奖励
  INVITE_REGISTER: 'REGISTER',      // 邀请注册奖励
  INVITE_ORDER: 'FIRST_ORDER'       // 邀请下单奖励
} as const

/**
 * 奖励金额常量（分为单位）
 */
const REWARD_AMOUNTS = {
  FILL_CODE: 2000,        // 填写邀请码奖励：20元
  INVITE_REGISTER: 7000,  // 邀请注册奖励：70元
  INVITE_ORDER: 4500,     // 邀请下单奖励：45元
  VIP_SPECIAL: 5000       // VIP专享：50元
} as const

/**
 * 奖励数据接口
 */
interface RewardItem {
  title: string
  amount: string
  description: string
  buttonText: string
  rewardType?: string
}

interface RewardData {
  fillCodeReward: RewardItem    // 填写邀请码奖励
  inviteReward: RewardItem      // 邀请奖励
  registerReward: RewardItem    // 邀请注册奖励
}

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 用户是否已填写邀请码
    hasUsedInvitationCode: {
      type: Boolean,
      value: false
    },
    // 总奖励金额
    totalRewardAmount: {
      type: Number,
      value: 0
    },
    // 邀请统计数据
    invitationStats: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    rewardData: {
      fillCodeReward: {
        title: '注册奖励',
        amount: '20～100元',
        description: '新用户专享',
        buttonText: '立即查看'
      },
      inviteReward: {
        title: '邀请奖励',
        amount: '10～100元',
        description: '分享得奖励',
        buttonText: '立即查看'
      },
      registerReward: {
        title: '下单奖励',
        amount: '20～100元',
        description: '邀请下单的奖励',
        buttonText: '去查看'
      }
    } as RewardData
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化奖励数据
     */
    initRewardData() {
      const { hasUsedInvitationCode } = this.properties

      // 根据用户状态更新奖励显示
      const rewardData: RewardData = {
        fillCodeReward: {
          title: '注册奖励',
          amount: '20～100元',
          description: hasUsedInvitationCode ? '已使用' : '新用户专享',
          buttonText: hasUsedInvitationCode ? '已领取' : '立即领取'
        },
        inviteReward: {
          title: '邀请奖励',
          amount: '10～100元',
          description: '分享得奖励',
          buttonText: '去分享'
        },
        registerReward: {
          title: '下单奖励',
          amount: '20～100元',
          description: '上不封顶',
          buttonText: '去查看'
        }
      }

      this.setData({ rewardData })
    },

    /**
     * 分享奖励事件处理
     */
    onShareReward() {
      this.triggerEvent('shareReward', {})
    },

    /**
     * 填写邀请码奖励点击事件
     */
    onFillCodeReward() {
      this.triggerEvent('fillCodeReward', {})
    },

    /**
     * 邀请奖励点击事件
     */
    onInviteReward() {
      this.triggerEvent('inviteReward', {})
    },

    /**
     * 邀请注册奖励点击事件
     */
    onRegisterReward() {
      this.triggerEvent('registerReward', {})
    },


  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initRewardData()
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'hasUsedInvitationCode, totalRewardAmount, invitationStats': function() {
      this.initRewardData()
    }
  }
})
