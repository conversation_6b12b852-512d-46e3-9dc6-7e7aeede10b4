// app.ts
import { CONFIG } from './utils/config'

App<IAppOption>({
  globalData: {
    cartSelectedMap: {},
    searchKeyword: '' // 搜索关键词临时存储
  },
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync(CONFIG.STORAGE_KEYS.LOGS) || []
    logs.unshift(Date.now())
    wx.setStorageSync(CONFIG.STORAGE_KEYS.LOGS, logs)

    // 登录
    wx.login({
      success: res => {
        console.log(res.code)
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      },
    })
  },
})