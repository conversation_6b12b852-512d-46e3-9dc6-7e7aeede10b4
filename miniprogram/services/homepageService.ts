// services/homepageService.ts
// 首页动态配置服务

import RequestManager from '../utils/request'
import { HomePageConfig, HomePageConfigResponse } from '../types/homepage'
import { CONFIG } from '../utils/config'
import SystemInfoManager from '../utils/systemInfo'

class HomePageService {
  private cacheKey = CONFIG.STORAGE_KEYS.HOMEPAGE_CONFIG
  private cacheExpiryKey = CONFIG.STORAGE_KEYS.HOMEPAGE_CONFIG_EXPIRY

  /**
   * 获取首页配置
   */
  async getHomePageConfig(forceRefresh = false): Promise<HomePageConfig> {
    try {
      // 检查缓存
      if (!forceRefresh) {
        const cachedConfig = this.getCachedConfig()
        if (cachedConfig) {
          console.log('使用缓存的首页配置')
          return cachedConfig
        }
      }

      console.log('从服务器获取首页配置')
      const response: HomePageConfigResponse = await RequestManager.get('/api/homepage/config')

      if (response.success && response.data) {
        // 缓存配置
        this.cacheConfig(response.data)
        return response.data
      } else {
        throw new Error(response.message || '获取首页配置失败')
      }
    } catch (error) {
      console.error('获取首页配置失败:', error)
      
      // 尝试使用缓存的配置
      const cachedConfig = this.getCachedConfig()
      if (cachedConfig) {
        console.log('使用缓存配置作为降级方案')
        return cachedConfig
      }

      // 返回默认配置
      return this.getDefaultConfig()
    }
  }

  /**
   * 获取缓存的配置
   */
  private getCachedConfig(): HomePageConfig | null {
    try {
      const expiry = wx.getStorageSync(this.cacheExpiryKey)
      if (expiry && Date.now() > expiry) {
        // 缓存已过期
        this.clearCache()
        return null
      }

      const config = wx.getStorageSync(this.cacheKey)
      return config || null
    } catch (error) {
      console.error('读取缓存配置失败:', error)
      return null
    }
  }

  /**
   * 缓存配置
   */
  private cacheConfig(config: HomePageConfig): void {
    try {
      const expiryTime = Date.now() + (config.cacheExpiry || 30 * 60 * 1000) // 默认30分钟
      
      wx.setStorageSync(this.cacheKey, config)
      wx.setStorageSync(this.cacheExpiryKey, expiryTime)
      
      console.log('首页配置已缓存')
    } catch (error) {
      console.error('缓存配置失败:', error)
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    try {
      wx.removeStorageSync(this.cacheKey)
      wx.removeStorageSync(this.cacheExpiryKey)
    } catch (error) {
      console.error('清除缓存失败:', error)
    }
  }

  /**
   * 获取默认配置（降级方案）
   */
  private getDefaultConfig(): HomePageConfig {
    return {
      pageName: "HomePage",
      version: "1.0.0",
      components: [
        {
          componentType: "carousel",
          componentId: "default-carousel",
          order: 1,
          data: {
            items: [
              {
                imageUrl: "/images/default-banner.jpg",
                order: 1,
                targetUrl: "/pages/products/products"
              }
            ]
          }
        },
        {
          componentType: "statement",
          componentId: "default-statement",
          order: 2,
          data: {
            items: [
              { content: "正品保障" },
              { content: "急速发货" },
              { content: "售后无忧" },
              { content: "联系客服" }
            ]
          }
        }
      ]
    }
  }

  /**
   * 预热配置（后台预加载）
   */
  async preloadConfig(): Promise<void> {
    try {
      await this.getHomePageConfig(false)
    } catch (error) {
      console.error('预热首页配置失败:', error)
    }
  }

  /**
   * 验证配置版本
   */
  async checkConfigVersion(): Promise<boolean> {
    try {
      const response = await RequestManager.get('/api/homepage/version')
      const cachedConfig = this.getCachedConfig()
      
      if (cachedConfig && response.data.version !== cachedConfig.version) {
        console.log('检测到新版本配置，清除缓存')
        this.clearCache()
        return true // 需要更新
      }
      
      return false // 不需要更新
    } catch (error) {
      console.error('检查配置版本失败:', error)
      return false
    }
  }

  /**
   * 上报组件渲染错误
   */
  async reportRenderError(componentType: string, componentId: string, error: string): Promise<void> {
    try {
      // 使用新的系统信息管理器替代已废弃的 wx.getSystemInfoSync
      const systemInfo = SystemInfoManager.getSystemInfo()

      await RequestManager.post('/api/homepage/error-report', {
        componentType,
        componentId,
        error,
        timestamp: Date.now(),
        userAgent: systemInfo
      })
    } catch (reportError) {
      console.error('上报渲染错误失败:', reportError)
    }
  }
}

export default new HomePageService()
