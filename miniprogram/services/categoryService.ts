// services/categoryService.ts
// 分类相关的API服务

import RequestManager from '../utils/request'
import { CategoryTreeResponse, CategoryDTO } from '../types/category'

class CategoryService {
  /**
   * 获取活跃分类树（公开接口）
   * @returns Promise<CategoryDTO[]>
   */
  async getActiveCategoryTree(): Promise<CategoryDTO[]> {
    try {
      const response: CategoryTreeResponse = await RequestManager.get('/api/public/categories/tree')

      if (response.success && response.data) {
        return response.data
      } else {
        console.error('获取分类树失败:', response.message)
        throw new Error(response.message || '获取分类数据失败')
      }
    } catch (error) {
      console.error('获取分类树异常:', error)
      throw error
    }
  }

  /**
   * 获取指定父分类下的子分类
   * @param parentId 父分类ID
   * @returns Promise<CategoryDTO[]>
   */
  async getChildrenByParentId(parentId: number): Promise<CategoryDTO[]> {
    try {
      const response: CategoryTreeResponse = await RequestManager.get(`/api/categories/parent/${parentId}`)
      
      if (response.success && response.data) {
        return response.data
      } else {
        console.error('获取子分类失败:', response.message)
        throw new Error(response.message || '获取子分类数据失败')
      }
    } catch (error) {
      console.error('获取子分类异常:', error)
      throw error
    }
  }

  /**
   * 根据分类ID获取分类详情
   * @param categoryId 分类ID
   * @returns Promise<CategoryDTO>
   */
  async getCategoryById(categoryId: number): Promise<CategoryDTO> {
    try {
      const response = await RequestManager.get(`/api/categories/${categoryId}`)
      
      if (response.success && response.data) {
        return response.data
      } else {
        console.error('获取分类详情失败:', response.message)
        throw new Error(response.message || '获取分类详情失败')
      }
    } catch (error) {
      console.error('获取分类详情异常:', error)
      throw error
    }
  }
}

export default new CategoryService()
