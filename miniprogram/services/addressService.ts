// services/addressService.ts
// 地址管理服务（服务端存储）

import {
  Address,
  AddressFormData,
  AddressOperationResult,
  AddressValidation,
  CreateAddressRequest,
  UpdateAddressRequest,
  AddressListResponse,
  AddressResponse
} from '../types/address'
import RequestManager from '../utils/request'

class AddressService {
  /**
   * 获取所有地址
   */
  async getAllAddresses(): Promise<Address[]> {
    try {
      const response: AddressListResponse = await RequestManager.get('/api/user/addresses')
      if (response.success) {
        return response.data || []
      } else {
        console.error('获取地址列表失败:', response.message)
        return []
      }
    } catch (error) {
      console.error('获取地址列表失败:', error)
      return []
    }
  }

  /**
   * 获取排序后的地址列表（默认地址在最前面）
   */
  async getSortedAddresses(): Promise<Address[]> {
    const addresses = await this.getAllAddresses()

    // 按默认地址排序：默认地址在前，非默认地址在后
    // 同类型内按更新时间倒序排列（最近更新的在前）
    return addresses.sort((a, b) => {
      // 首先按是否默认地址排序
      if (a.isDefault && !b.isDefault) {
        return -1 // a 是默认地址，排在前面
      }
      if (!a.isDefault && b.isDefault) {
        return 1  // b 是默认地址，排在前面
      }

      // 如果都是默认或都不是默认，按更新时间倒序
      const timeA = new Date(a.updatedAt).getTime()
      const timeB = new Date(b.updatedAt).getTime()
      return timeB - timeA // 最近更新的在前
    })
  }

  /**
   * 根据ID获取地址
   */
  async getAddressById(id: number): Promise<Address | null> {
    try {
      const response: AddressResponse = await RequestManager.get(`/api/user/addresses/${id}`)
      if (response.success) {
        return response.data
      } else {
        console.error('获取地址详情失败:', response.message)
        return null
      }
    } catch (error) {
      console.error('获取地址详情失败:', error)
      return null
    }
  }

  /**
   * 获取默认地址
   */
  async getDefaultAddress(): Promise<Address | null> {
    try {
      const response: AddressResponse = await RequestManager.get('/api/user/addresses/default')
      if (response.success) {
        return response.data
      } else {
        console.error('获取默认地址失败:', response.message)
        return null
      }
    } catch (error) {
      console.error('获取默认地址失败:', error)
      return null
    }
  }

  /**
   * 新增地址
   */
  async addAddress(formData: AddressFormData): Promise<AddressOperationResult> {
    try {
      // 验证数据
      const validation = this.validateAddress(formData)
      if (!validation.isValid) {
        return {
          success: false,
          message: validation.errors.join(', ')
        }
      }

      // 构建创建地址请求
      const createRequest: CreateAddressRequest = {
        recipientName: formData.recipientName.trim(),
        phoneNumber: formData.phoneNumber.trim(),
        regionProvince: formData.regionProvince,
        regionCity: formData.regionCity,
        regionDistrict: formData.regionDistrict,
        streetAddress: formData.streetAddress.trim(),
        postalCode: formData.postalCode ? formData.postalCode.trim() : '',
        isDefault: formData.isDefault
      }

      const response: AddressResponse = await RequestManager.post('/api/user/addresses', createRequest)

      if (response.success) {
        return {
          success: true,
          message: '地址添加成功',
          data: response.data
        }
      } else {
        return {
          success: false,
          message: response.message || '地址添加失败'
        }
      }
    } catch (error) {
      console.error('添加地址失败:', error)
      return {
        success: false,
        message: '地址添加失败，请重试'
      }
    }
  }

  /**
   * 更新地址
   */
  async updateAddress(id: number, formData: AddressFormData): Promise<AddressOperationResult> {
    try {
      // 验证数据
      const validation = this.validateAddress(formData)
      if (!validation.isValid) {
        return {
          success: false,
          message: validation.errors.join(', ')
        }
      }

      // 构建更新地址请求
      const updateRequest: UpdateAddressRequest = {
        recipientName: formData.recipientName.trim(),
        phoneNumber: formData.phoneNumber.trim(),
        regionProvince: formData.regionProvince,
        regionCity: formData.regionCity,
        regionDistrict: formData.regionDistrict,
        streetAddress: formData.streetAddress.trim(),
        postalCode: formData.postalCode ? formData.postalCode.trim() : '',
        isDefault: formData.isDefault
      }

      const response: AddressResponse = await RequestManager.put(`/api/user/addresses/${id}`, updateRequest)

      if (response.success) {
        return {
          success: true,
          message: '地址更新成功',
          data: response.data
        }
      } else {
        return {
          success: false,
          message: response.message || '地址更新失败'
        }
      }
    } catch (error) {
      console.error('更新地址失败:', error)
      return {
        success: false,
        message: '地址更新失败，请重试'
      }
    }
  }

  /**
   * 删除地址
   */
  async deleteAddress(id: number): Promise<AddressOperationResult> {
    try {
      const response = await RequestManager.delete(`/api/user/addresses/${id}`)

      if (response.success) {
        return {
          success: true,
          message: '地址删除成功'
        }
      } else {
        return {
          success: false,
          message: response.message || '地址删除失败'
        }
      }
    } catch (error) {
      console.error('删除地址失败:', error)
      return {
        success: false,
        message: '地址删除失败，请重试'
      }
    }
  }

  /**
   * 设置默认地址
   */
  async setDefaultAddress(id: number): Promise<AddressOperationResult> {
    try {
      const response = await RequestManager.post(`/api/user/addresses/${id}/set-default`)

      if (response.success) {
        return {
          success: true,
          message: '默认地址设置成功'
        }
      } else {
        return {
          success: false,
          message: response.message || '设置默认地址失败'
        }
      }
    } catch (error) {
      console.error('设置默认地址失败:', error)
      return {
        success: false,
        message: '设置默认地址失败，请重试'
      }
    }
  }

  /**
   * 验证地址数据
   */
  private validateAddress(formData: AddressFormData): AddressValidation {
    const errors: string[] = []

    if (!formData.recipientName || !formData.recipientName.trim()) {
      errors.push('收货人姓名不能为空')
    }

    if (!formData.phoneNumber || !formData.phoneNumber.trim()) {
      errors.push('手机号不能为空')
    } else if (!this.isValidPhone(formData.phoneNumber.trim())) {
      errors.push('手机号格式不正确')
    }

    if (!formData.regionProvince) {
      errors.push('请选择省份')
    }

    if (!formData.regionCity) {
      errors.push('请选择城市')
    }

    if (!formData.regionDistrict) {
      errors.push('请选择区县')
    }

    if (!formData.streetAddress || !formData.streetAddress.trim()) {
      errors.push('详细地址不能为空')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证手机号格式
   */
  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }
}

export default new AddressService()
