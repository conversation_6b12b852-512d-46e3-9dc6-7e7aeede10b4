// services/couponService.ts
// 优惠券相关的API服务

import RequestManager from '../utils/request'
import {
  Coupon,
  CouponListResponse,
  CouponValidation,
  CouponValidationResponse,
  PriceCalculation,
  CouponUsage,
  CouponType,
  CouponStatus
} from '../types/coupon'

class CouponService {
  /**
   * 将后端返回的优惠券（金额单位：元）转换为前端内部单位（分）
   */
  private normalizeCouponFromServer(server: any): Coupon {
    return {
      ...server,
      amount: Math.round((server.amount || 0) * 100),
      minOrderAmount: server.minOrderAmount != null ? Math.round(server.minOrderAmount * 100) : undefined
    }
  }

  /**
   * 获取用户所有可用优惠券
   * @returns Promise<Coupon[]>
   */
  async getAvailableCoupons(): Promise<Coupon[]> {
    try {
      const response: CouponListResponse = await RequestManager.get('/api/coupons/available')

      if (response.success && response.data) {
        return response.data.map(c => this.normalizeCouponFromServer(c))
      } else {
        console.error('获取可用优惠券失败:', response.message)
        return []
      }
    } catch (error) {
      console.error('获取可用优惠券异常:', error)
      return []
    }
  }

  /**
   * 获取订单可用的优惠券
   * @param orderAmount 订单金额（分）
   * @returns Promise<Coupon[]>
   */
  async getCouponsForOrder(orderAmountInFen: number): Promise<Coupon[]> {
    try {
      // 后端使用元单位，这里转换分->元
      const orderAmountYuan = (orderAmountInFen || 0) / 100
      const response: CouponListResponse = await RequestManager.get(
        `/api/coupons/for-order?orderAmount=${orderAmountYuan}`
      )

      if (response.success && response.data) {
        return response.data.map(c => this.normalizeCouponFromServer(c))
      } else {
        console.error('获取订单可用优惠券失败:', response.message)
        return []
      }
    } catch (error) {
      console.error('获取订单可用优惠券异常:', error)
      return []
    }
  }

  /**
   * 验证优惠券是否可用
   * @param couponCode 优惠券编码
   * @param orderAmount 订单金额（分）
   * @returns Promise<CouponValidation>
   */
  async validateCoupon(couponCode: string, orderAmountInFen: number): Promise<CouponValidation> {
    try {
      const orderAmountYuan = (orderAmountInFen || 0) / 100
      const response: CouponValidationResponse = await RequestManager.get(
        `/api/coupons/validate/${couponCode}?orderAmount=${orderAmountYuan}`
      )

      if (response.success && response.data) {
        return response.data
      } else {
        return {
          isValid: false,
          reason: response.message || '优惠券验证失败'
        }
      }
    } catch (error) {
      console.error('验证优惠券异常:', error)
      return {
        isValid: false,
        reason: '验证优惠券时发生错误'
      }
    }
  }

  /**
   * 计算使用优惠券后的价格
   * @param originalAmount 原始金额（分）
   * @param coupons 要使用的优惠券列表
   * @returns PriceCalculation
   */
  calculatePriceWithCoupons(originalAmount: number, coupons: Coupon[]): PriceCalculation {
    let totalDiscount = 0
    const usedCoupons: CouponUsage[] = []

    // 按优惠金额从大到小排序，优先使用优惠金额大的券
    const sortedCoupons = [...coupons].sort((a, b) => b.amount - a.amount)

    for (const coupon of sortedCoupons) {
      // 检查最低订单金额要求
      if (coupon.minOrderAmount && originalAmount < coupon.minOrderAmount) {
        continue
      }

      // 计算实际可优惠金额
      const remainingAmount = originalAmount - totalDiscount
      const discountAmount = Math.min(coupon.amount, remainingAmount)

      if (discountAmount > 0) {
        totalDiscount += discountAmount
        usedCoupons.push({
          couponId: coupon.id,
          couponCode: coupon.couponCode,
          discountAmount
        })
      }

      // 如果已经优惠到0元，停止计算
      if (totalDiscount >= originalAmount) {
        break
      }
    }

    // 确保最终金额不为负数
    const finalAmount = Math.max(0, originalAmount - totalDiscount)

    return {
      originalAmount,
      totalDiscount,
      finalAmount,
      usedCoupons
    }
  }

  /**
   * 分类优惠券（邀请奖励 vs 普通优惠券）
   * @param coupons 优惠券列表
   * @returns { invitationCoupons: Coupon[], regularCoupons: Coupon[] }
   */
  categorizeCoupons(coupons: Coupon[]): { invitationCoupons: Coupon[], regularCoupons: Coupon[] } {
    const invitationCoupons: Coupon[] = []
    const regularCoupons: Coupon[] = []

    coupons.forEach(coupon => {
      if (coupon.couponType === CouponType.INVITATION_REGISTER ||
          coupon.couponType === CouponType.INVITATION_ORDER) {
        invitationCoupons.push(coupon)
      } else {
        regularCoupons.push(coupon)
      }
    })

    return { invitationCoupons, regularCoupons }
  }

  /**
   * 检查优惠券是否已过期
   * @param coupon 优惠券
   * @returns boolean
   */
  isCouponExpired(coupon: Coupon): boolean {
    const now = new Date()
    const expiresAt = new Date(coupon.expiresAt)
    return now > expiresAt
  }

  /**
   * 检查优惠券是否可用
   * @param coupon 优惠券
   * @param orderAmount 订单金额（分）
   * @returns boolean
   */
  isCouponUsable(coupon: Coupon, orderAmount: number): boolean {
    // 检查状态
    if (coupon.status !== CouponStatus.AVAILABLE) {
      return false
    }

    // 检查是否过期
    if (this.isCouponExpired(coupon)) {
      return false
    }

    // 检查最低订单金额要求
    if (coupon.minOrderAmount && orderAmount < coupon.minOrderAmount) {
      return false
    }

    return true
  }

  /**
   * 格式化优惠券金额显示
   * @param amount 金额（分）
   * @returns string
   */
  formatCouponAmount(amount: number): string {
    return (amount / 100).toFixed(2)
  }

  /**
   * 获取优惠券类型显示名称
   * @param couponType 优惠券类型
   * @returns string
   */
  getCouponTypeDisplayName(couponType: string): string {
    const typeMap: { [key: string]: string } = {
      [CouponType.INVITATION_REGISTER]: '邀请注册奖励',
      [CouponType.INVITATION_ORDER]: '邀请订单奖励',
      [CouponType.REGULAR]: '优惠券'
    }

    return typeMap[couponType] || '优惠券'
  }
}

export default new CouponService()
