// services/reviewService.ts
// 评论相关的API服务

import RequestManager from '../utils/request'
import { 
  ReviewDTO, 
  ReviewStatsDTO, 
  ReviewListResponse, 
  ReviewStatsResponse, 
  ReviewQueryParams,
  PageResult
} from '../types/review'

class ReviewService {
  /**
   * 获取商品评论列表
   * @param productId 商品ID
   * @param params 查询参数
   * @returns Promise<PageResult<ReviewDTO>>
   */
  async getProductReviews(productId: number, params?: ReviewQueryParams): Promise<PageResult<ReviewDTO>> {
    try {
      // 构建查询参数
      const queryParts: string[] = []
      if (params?.page) queryParts.push(`page=${params.page}`)
      if (params?.size) queryParts.push(`size=${params.size}`)
      if (params?.sortBy) queryParts.push(`sortBy=${params.sortBy}`)

      const queryString = queryParts.join('&')
      const url = `/api/products/${productId}/reviews${queryString ? '?' + queryString : ''}`

      console.log('调用评论列表接口:', url)

      const response: ReviewListResponse = await RequestManager.get(url)

      if (response.success && response.data) {
        console.log('获取评论列表成功:', response.data)
        return response.data
      } else {
        console.error('获取评论列表失败:', response.message)
        throw new Error(response.message || '获取评论数据失败')
      }
    } catch (error) {
      console.error('获取评论列表异常:', error)
      throw error
    }
  }

  /**
   * 获取商品评论统计
   * @param productId 商品ID
   * @returns Promise<ReviewStatsDTO>
   */
  async getProductReviewStats(productId: number): Promise<ReviewStatsDTO> {
    try {
      const url = `/api/products/${productId}/reviews/stats`
      console.log('调用评论统计接口:', url)

      const response: ReviewStatsResponse = await RequestManager.get(url)

      if (response.success && response.data) {
        console.log('获取评论统计成功:', response.data)
        return response.data
      } else {
        console.error('获取评论统计失败:', response.message)
        throw new Error(response.message || '获取评论统计失败')
      }
    } catch (error) {
      console.error('获取评论统计异常:', error)
      throw error
    }
  }

  /**
   * 格式化评分显示
   * @param rating 评分（1-5）
   * @returns 星星字符串
   */
  formatRating(rating: number): string {
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 >= 0.5
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0)
    
    return '★'.repeat(fullStars) + 
           (hasHalfStar ? '☆' : '') + 
           '☆'.repeat(emptyStars)
  }

  /**
   * 格式化时间显示
   * @param dateString 时间字符串
   * @returns 格式化后的时间
   */
  formatTime(dateString: string): string {
    const date = new Date(dateString)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (minutes < 60) {
      return `${minutes}分钟前`
    } else if (hours < 24) {
      return `${hours}小时前`
    } else if (days < 30) {
      return `${days}天前`
    } else {
      return date.toLocaleDateString('zh-CN')
    }
  }

  /**
   * 获取默认头像
   * @param userName 用户名
   * @returns 默认头像URL
   */
  getDefaultAvatar(userName: string): string {
    // 可以根据用户名生成默认头像，或返回统一的默认头像
    return '/images/default-avatar.png'
  }

  /**
   * 处理匿名用户显示
   * @param review 评论数据
   * @returns 处理后的用户名和头像
   */
  processAnonymousUser(review: ReviewDTO): { displayName: string, displayAvatar: string } {
    if (review.isAnonymous) {
      return {
        displayName: '匿名用户',
        displayAvatar: this.getDefaultAvatar('anonymous')
      }
    }
    
    return {
      displayName: review.userName,
      displayAvatar: review.userAvatar || this.getDefaultAvatar(review.userName)
    }
  }
}

export default new ReviewService()
