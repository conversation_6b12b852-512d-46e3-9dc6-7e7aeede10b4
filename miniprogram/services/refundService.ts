// services/refundService.ts
// 退款相关的API服务

import RequestManager from '../utils/request'
import { DateFormatter } from '../utils/dateFormatter'
import {
  RefundEligibilityApiResponse,
  RefundReasonsApiResponse,
  CreateRefundApiResponse,
  RefundListApiResponse,
  ServerRefundListData,
  RefundDetailApiResponse,
  ServerRefundDetailData,
  ReturnAddressApiResponse,
  RefundEligibilityResponse,
  RefundReasonOption,
  CreateRefundRequest,
  RefundInfo,
  RefundDetail,
  RefundShippingRequest,
  ReturnAddress,
  RefundStatus,
  RefundType,
  RefundReason,
  RefundAction
} from '../types/refund'

class RefundService {
  /**
   * 检查退款资格
   * @param orderId 订单ID
   * @returns Promise<RefundEligibilityResponse>
   */
  async checkRefundEligibility(orderId: number): Promise<RefundEligibilityResponse> {
    try {
      console.log('检查退款资格，订单ID:', orderId)
      
      const response: RefundEligibilityApiResponse = await RequestManager.get(`/api/refund/check-eligibility/${orderId}`)
      
      if (response.success && response.data) {
        console.log('退款资格检查成功:', response.data)
        return response.data
      } else {
        console.error('检查退款资格失败:', response.message)
        throw new Error(response.message || '检查退款资格失败')
      }
    } catch (error) {
      console.error('检查退款资格异常:', error)
      throw error
    }
  }

  /**
   * 获取退款原因列表
   * @returns Promise<RefundReasonOption[]>
   */
  async getRefundReasons(): Promise<RefundReasonOption[]> {
    try {
      console.log('获取退款原因列表')
      
      const response: RefundReasonsApiResponse = await RequestManager.get('/api/refund/reasons')
      
      if (response.success && response.data) {
        console.log('获取退款原因成功:', response.data.reasons)
        return response.data.reasons
      } else {
        console.error('获取退款原因失败:', response.message)
        throw new Error(response.message || '获取退款原因失败')
      }
    } catch (error) {
      console.error('获取退款原因异常:', error)
      throw error
    }
  }

  /**
   * 创建退款申请
   * @param request 退款申请数据
   * @returns Promise<{id: number, refundNumber: string, status: RefundStatus, statusDesc: string, createdAt: string}>
   */
  async createRefundRequest(request: CreateRefundRequest) {
    try {
      console.log('创建退款申请:', request)
      console.log('退款原因值:', request.refundReason)
      console.log('退款原因类型:', typeof request.refundReason)
      
      // 验证必要参数
      if (!request.orderId) {
        throw new Error('订单ID不能为空')
      }
      if (!request.refundType) {
        throw new Error('退款类型不能为空')
      }
      if (!request.refundReason) {
        throw new Error('退款原因不能为空')
      }
      if (!request.refundAmount || request.refundAmount <= 0) {
        throw new Error('退款金额必须大于0')
      }
      if (!request.refundItems || request.refundItems.length === 0) {
        throw new Error('退款商品不能为空')
      }

      const response: CreateRefundApiResponse = await RequestManager.post('/api/refund/create', request)
      
      if (response.success && response.data) {
        console.log('创建退款申请成功:', response.data)
        return response.data
      } else {
        console.error('创建退款申请失败:', response.message)
        throw new Error(response.message || '创建退款申请失败')
      }
    } catch (error) {
      console.error('创建退款申请异常:', error)
      throw error
    }
  }

  /**
   * 获取我的退款申请列表
   * @param params 查询参数
   * @returns Promise<{records: RefundInfo[], total: number, current: number, pages: number}>
   */
  async getRefundList(params: {
    pageNum?: number
    pageSize?: number
    status?: RefundStatus
  } = {}) {
    try {
      const { pageNum = 1, pageSize = 20, status } = params
      
      // 构建查询参数
      const queryParts: string[] = []
      queryParts.push(`pageNum=${pageNum}`)
      queryParts.push(`pageSize=${pageSize}`)
      if (status) queryParts.push(`status=${status}`)
      
      const queryString = queryParts.join('&')
      const url = `/api/refund/list?${queryString}`
      
      console.log('获取退款申请列表:', url)
      
      const response: RefundListApiResponse = await RequestManager.get(url)
      
      if (response.success && response.data) {
        console.log('获取退款申请列表成功:', response.data)

        // 转换服务端数据为前端格式
        const serverData = response.data
        const convertedData = this.convertServerRefundListToRefundList(serverData)

        console.log('转换后的退款列表:', convertedData)
        return convertedData
      } else {
        console.error('获取退款申请列表失败:', response.message)
        throw new Error(response.message || '获取退款申请列表失败')
      }
    } catch (error) {
      console.error('获取退款申请列表异常:', error)
      throw error
    }
  }

  /**
   * 转换服务端退款列表数据为前端格式
   * @param serverData 服务端返回的原始数据
   * @returns 转换后的数据
   */
  convertServerRefundListToRefundList(serverData: any): {records: RefundInfo[], total: number, current: number, pages: number} {
    console.log('开始转换退款列表数据:', serverData)

    try {
      const convertedRecords: RefundInfo[] = serverData.records.map((item: any) => {
        // 处理产品详情
        const productDetails = (item.productDetails || []).map((product: any) => {
          // 解析 SKU 属性
          let skuAttributes: { [key: string]: string } = {}
          try {
            skuAttributes = JSON.parse(product.skuAttributes || '{}')
          } catch (error) {
            console.warn('解析 SKU 属性失败:', error)
            skuAttributes = {}
          }

          return {
            orderItemId: product.orderItemId,
            productId: product.productId,
            productName: product.productName,
            productMainImageUrl: product.productMainImageUrl || '',
            skuNameExtension: product.skuNameExtension || '',
            skuPrice: Math.round(product.skuPrice * 100), // 元转分
            skuAttributes: skuAttributes,
            quantity: product.quantity,
            unitPriceAtPurchase: Math.round(product.unitPriceAtPurchase * 100), // 元转分
            totalPrice: Math.round(product.totalPrice * 100), // 元转分
            refundQuantity: product.refundQuantity,
            refundAmount: Math.round(product.refundAmount) // 已经是分为单位
          }
        })

        // 计算可用操作
        const availableActions = this.getAvailableActions(item.status as RefundStatus)

        const refundInfo: RefundInfo = {
          id: item.id,
          refundNumber: item.refund_number,
          orderNumber: item.order_number,
          refundType: item.refund_type as any,
          refundTypeDesc: this.getRefundTypeDescription(item.refund_type),
          refundReasonDesc: this.getRefundReasonDescription(item.refund_reason),
          refundAmount: Math.round(item.refund_amount * 100), // 元转分
          status: item.status as any,
          statusDesc: this.getStatusDescription(item.status),
          createdAt: DateFormatter.formatDateTime(item.created_at),
          updatedAt: DateFormatter.formatDateTime(item.updated_at),
          availableActions: availableActions,
          productDetails: productDetails
        }
        return refundInfo
      })

      const result = {
        records: convertedRecords,
        total: serverData.total || 0,
        current: serverData.current || 1,
        pages: serverData.pages || 0
      }

      console.log('转换后的退款列表数据:', result)
      return result
    } catch (error) {
      console.error('转换退款列表数据失败:', error)
      throw new Error('数据格式转换失败')
    }
  }

  /**
   * 获取退款类型描述
   */
  getRefundTypeDescription(refundType: string): string {
    const typeMap: Record<string, string> = {
      'refund_only': '仅退款',
      'return_refund': '退货退款'
    }
    return typeMap[refundType] || '未知类型'
  }

  /**
   * 获取退款原因描述
   */
  getRefundReasonDescription(refundReason: string): string {
    const reasonMap: Record<string, string> = {
      'price_or_promotion': '买贵了/少用优惠',
      'size_mismatch': '尺寸不符',
      'seven_days_no_reason': '7天无理由',
      'not_as_described': '与描述不符',
      'change_mind': '不想要了',
      'quality_issue': '质量问题',
      'wrong_item': '发错商品',
      'other': '其他原因'
    }
    return reasonMap[refundReason] || '未知原因'
  }

  /**
   * 获取退款申请详情
   * @param refundId 退款申请ID
   * @returns Promise<RefundDetail>
   */
  async getRefundDetail(refundId: number): Promise<RefundDetail> {
    try {
      console.log('获取退款申请详情，ID:', refundId)

      const response: RefundDetailApiResponse = await RequestManager.get(`/api/refund/detail/${refundId}`)

      if (response.success && response.data) {
        console.log('获取退款申请详情成功:', response.data)

        // 转换服务端数据为前端格式
        const serverData = response.data
        const refundDetail: RefundDetail = this.convertServerRefundDetailToRefundDetail(serverData)

        console.log('转换后的退款详情:', refundDetail)
        return refundDetail
      } else {
        console.error('获取退款申请详情失败:', response.message)
        throw new Error(response.message || '获取退款申请详情失败')
      }
    } catch (error) {
      console.error('获取退款申请详情异常:', error)
      throw error
    }
  }

  /**
   * 转换服务端退款详情数据为前端格式
   * @param serverData 服务端返回的原始数据
   * @returns RefundDetail
   */
  convertServerRefundDetailToRefundDetail(serverData: any): RefundDetail {
    console.log('开始转换退款详情数据:', serverData)

    try {
      // 解析 JSON 字符串字段
      let evidenceImages: string[] = []
      try {
        evidenceImages = JSON.parse(serverData.evidence_images || '[]')
      } catch (error) {
        console.warn('解析证据图片失败:', error)
        evidenceImages = []
      }

      let refundItems: any[] = []
      try {
        refundItems = JSON.parse(serverData.refund_items || '[]')
      } catch (error) {
        console.warn('解析退款商品失败:', error)
        refundItems = []
      }

      // 转换退款商品格式
      const formattedRefundItems = refundItems.map(item => ({
        orderItemId: item.orderItemId || 0,
        productName: item.productName || '未知商品',
        refundQuantity: item.refundQuantity || 0,
        refundAmount: item.refundAmount || 0,
        productImageUrl: item.productImageUrl || ''
      }))

      // 使用服务端返回的可用操作，如果没有则使用本地计算
      let availableActions: string[] = []
      if (serverData.availableActions && Array.isArray(serverData.availableActions)) {
        availableActions = serverData.availableActions
        console.log('使用服务端返回的availableActions:', availableActions)
      } else {
        // 兜底逻辑：如果服务端没有返回，则本地计算
        if (serverData.status === 'pending_review') {
          availableActions.push(RefundAction.CANCEL)
        } else if (serverData.status === 'approved' && serverData.refund_type === 'return_refund') {
          availableActions.push(RefundAction.SUBMIT_SHIPPING)
        }
        console.log('使用本地计算的availableActions:', availableActions)
      }

      // 处理商品详情数据
      const productDetails = serverData.productDetails || []
      const formattedProductDetails = productDetails.map((item: any) => ({
        orderItemId: item.orderItemId || 0,
        productId: item.productId || 0,
        productName: item.productName || '未知商品',
        productMainImageUrl: item.productMainImageUrl || item.skuImageUrl || '',
        productImageUrl: item.productMainImageUrl || item.skuImageUrl || '',
        skuNameExtension: item.skuNameExtension || '',
        skuPrice: item.skuPrice || 0,
        skuAttributes: item.skuAttributes || '{}',
        quantity: item.quantity || 0,
        unitPriceAtPurchase: item.unitPriceAtPurchase || 0,
        totalPrice: item.totalPrice || 0,
        refundQuantity: item.refundQuantity || 0,
        refundAmount: Math.round((item.refundAmount || 0) * 100) // 元转分
      }))

      const result: RefundDetail = {
        // 基础信息
        id: serverData.id,
        refundNumber: serverData.refund_number,
        orderNumber: serverData.order_number, // 添加订单号字段
        refundType: serverData.refund_type as any,
        status: serverData.status as any,
        statusDesc: this.getStatusDescription(serverData.status),
        refundAmount: Math.round(serverData.refund_amount * 100), // 元转分
        createdAt: DateFormatter.formatDateTime(serverData.created_at),
        updatedAt: DateFormatter.formatDateTime(serverData.updated_at),

        // 订单信息
        orderId: serverData.order_id,

        // 退款详情
        refundReason: serverData.refund_reason as any,
        refundReasonDesc: this.getRefundReasonDescription(serverData.refund_reason),
        refundTypeDesc: this.getRefundTypeDescription(serverData.refund_type),
        refundDescription: serverData.refund_description || '',
        refundItems: formattedRefundItems,
        evidenceImages: evidenceImages,

        // 商品详情信息
        productDetails: formattedProductDetails,

        // 审核信息
        adminReviewNotes: serverData.admin_review_notes || '',
        reviewedAt: serverData.reviewed_at ? DateFormatter.formatDateTime(serverData.reviewed_at) : '',

        // 可用操作
        availableActions: availableActions,

        // 物流信息 - 处理 shippingList 字段
        shippingInfo: this.extractShippingInfo(serverData.shippingList)
      }

      console.log('转换后的退款详情数据:', result)
      return result
    } catch (error) {
      console.error('转换退款详情数据失败:', error)
      throw new Error('数据格式转换失败')
    }
  }

  /**
   * 从 shippingList 中提取物流信息
   * @param shippingList 服务端返回的物流信息列表
   * @returns 格式化后的物流信息
   */
  extractShippingInfo(shippingList: any[]): any {
    if (!shippingList || !Array.isArray(shippingList) || shippingList.length === 0) {
      return undefined
    }

    // 取最新的物流信息（按创建时间排序，取第一个）
    const latestShipping = shippingList.sort((a, b) => {
      const timeA = new Date(a.createdAt || a.created_at || 0).getTime()
      const timeB = new Date(b.createdAt || b.created_at || 0).getTime()
      return timeB - timeA // 降序排列，最新的在前
    })[0]

    return {
      id: latestShipping.id,
      shippingCompany: latestShipping.shippingCompany || latestShipping.shipping_company || '',
      trackingNumber: latestShipping.trackingNumber || latestShipping.tracking_number || '',
      shippingNotes: latestShipping.shippingNotes || latestShipping.shipping_notes || '',
      shippedAt: (latestShipping.shippedAt || latestShipping.shipped_at) ? DateFormatter.formatDateTime(latestShipping.shippedAt || latestShipping.shipped_at) : '',
      shippingStatus: latestShipping.shippingStatus || latestShipping.shipping_status || '',
      senderName: latestShipping.senderName || latestShipping.sender_name || '',
      senderPhone: latestShipping.senderPhone || latestShipping.sender_phone || '',
      senderAddress: latestShipping.senderAddress || latestShipping.sender_address || '',
      receiverName: latestShipping.receiverName || latestShipping.receiver_name || '',
      receiverPhone: latestShipping.receiverPhone || latestShipping.receiver_phone || '',
      receiverAddress: latestShipping.receiverAddress || latestShipping.receiver_address || ''
    }
  }

  /**
   * 获取状态描述
   */
  getStatusDescription(status: string): string {
    const statusMap: Record<string, string> = {
      'pending_review': '待审核',
      'approved': '待退货',
      'pending_refund': '待退款',
      'rejected': '已拒绝',
      'user_shipping': '用户寄回中',
      'merchant_received': '待退款', 
      'refunded': '已退款',
      'cancelled': '已取消',
      'completed': '已完成'
    }
    return statusMap[status] || '未知状态'
  }

  /**
   * 提交退货物流信息
   * @param request 物流信息
   * @returns Promise<void>
   */
  async submitShippingInfo(request: RefundShippingRequest): Promise<void> {
    try {
      console.log('提交退货物流信息:', request)

      // 验证必要参数
      if (!request.refundRequestId) {
        throw new Error('退款申请ID不能为空')
      }
      if (!request.shippingCompany) {
        throw new Error('物流公司不能为空')
      }
      if (!request.trackingNumber) {
        throw new Error('物流单号不能为空')
      }

      const response = await RequestManager.post('/api/refund/shipping', request)

      if (response.success) {
        console.log('提交退货物流信息成功')
      } else {
        console.error('提交退货物流信息失败:', response.message)
        throw new Error(response.message || '提交退货物流信息失败')
      }
    } catch (error) {
      console.error('提交退货物流信息异常:', error)
      throw error
    }
  }

  /**
   * 修改退货物流信息
   * @param shippingId 物流记录ID
   * @param request 修改的物流信息
   * @returns Promise<void>
   */
  async updateShippingInfo(shippingId: number, request: UpdateRefundShippingRequest): Promise<void> {
    try {
      console.log('修改退货物流信息:', { shippingId, request })

      // 验证必要参数
      if (!shippingId) {
        throw new Error('物流记录ID不能为空')
      }
      if (!request.shippingCompany) {
        throw new Error('物流公司不能为空')
      }
      if (!request.trackingNumber) {
        throw new Error('物流单号不能为空')
      }

      const response = await RequestManager.put(`/api/refund/shipping/${shippingId}`, request)

      if (response.success) {
        console.log('修改退货物流信息成功')
      } else {
        console.error('修改退货物流信息失败:', response.message)
        throw new Error(response.message || '修改退货物流信息失败')
      }
    } catch (error) {
      console.error('修改退货物流信息异常:', error)
      throw error
    }
  }

  /**
   * 取消退款申请
   * @param refundId 退款申请ID
   * @returns Promise<void>
   */
  async cancelRefundRequest(refundId: number): Promise<void> {
    try {
      console.log('取消退款申请，ID:', refundId)
      
      const response = await RequestManager.post(`/api/refund/cancel/${refundId}`)
      
      if (response.success) {
        console.log('取消退款申请成功')
      } else {
        console.error('取消退款申请失败:', response.message)
        throw new Error(response.message || '取消退款申请失败')
      }
    } catch (error) {
      console.error('取消退款申请异常:', error)
      throw error
    }
  }

  /**
   * 获取退货收货地址
   * @returns Promise<ReturnAddress>
   */
  async getReturnAddress(): Promise<ReturnAddress> {
    try {
      console.log('获取退货收货地址')
      
      const response: ReturnAddressApiResponse = await RequestManager.get('/api/refund/return-address')
      
      if (response.success && response.data) {
        console.log('获取退货收货地址成功:', response.data)
        return response.data
      } else {
        console.error('获取退货收货地址失败:', response.message)
        throw new Error(response.message || '获取退货收货地址失败')
      }
    } catch (error) {
      console.error('获取退货收货地址异常:', error)
      throw error
    }
  }

  /**
   * 格式化退款状态显示文本
   * @param status 退款状态
   * @returns string
   */
  formatRefundStatus(status: RefundStatus): string {
    const statusMap: Record<RefundStatus, string> = {
      [RefundStatus.PENDING_REVIEW]: '待审核',
      [RefundStatus.APPROVED]: '已同意',
      [RefundStatus.PENDING_REFUND]: '等待退款',
      [RefundStatus.REJECTED]: '已拒绝',
      [RefundStatus.USER_SHIPPING]: '寄回中',
      [RefundStatus.MERCHANT_RECEIVED]: '待退款', // 修改：显示为"待退款"而不是"商家已收货"
      [RefundStatus.REFUNDED]: '已退款',
      [RefundStatus.CANCELLED]: '已取消'
    }
    return statusMap[status] || status
  }

  /**
   * 格式化退款类型显示文本
   * @param type 退款类型
   * @returns string
   */
  formatRefundType(type: RefundType): string {
    const typeMap: Record<RefundType, string> = {
      [RefundType.REFUND_ONLY]: '仅退款',
      [RefundType.RETURN_REFUND]: '退货退款'
    }
    return typeMap[type] || type
  }

  /**
   * 获取退款状态对应的可用操作
   * @param status 退款状态
   * @returns Array<string>
   */
  getAvailableActions(status: RefundStatus): string[] {
    switch (status) {
      case RefundStatus.PENDING_REVIEW:
        return [RefundAction.CANCEL] // 可取消
      case RefundStatus.APPROVED:
        return [RefundAction.SUBMIT_SHIPPING, RefundAction.CANCEL] // 可提交物流或取消
      case RefundStatus.PENDING_REFUND:
        return [] // 等待退款处理
      case RefundStatus.USER_SHIPPING:
        return [RefundAction.EDIT_SHIPPING] // 可修改物流信息
      case RefundStatus.MERCHANT_RECEIVED:
        return [] // 等待退款处理（与PENDING_REFUND相同）
      case RefundStatus.REFUNDED:
      case RefundStatus.REJECTED:
      case RefundStatus.CANCELLED:
        return [] // 已完成状态，无操作
      default:
        return []
    }
  }
}

export default new RefundService()
