// services/configService.ts
// 配置管理服务



declare const wx: any

// 配置项接口
export interface ConfigItem {
  configKey: string
  configValue: string
  configType: 'text' | 'json' | 'number' | 'boolean'
  category: string
  description: string
  createdAt: string | null
  updatedAt: string | null
}

// 配置响应接口
export interface ConfigResponse {
  data: ConfigItem[]
}

// 轮播图配置
export interface BannerConfig {
  banners: Array<{
    title: string
    imageUrl: string
    sortOrder: number
    url?: string
    productId?: number
    linkType?: 'URL' | 'PRODUCT' | 'IMAGE' | 'NONE'
    largeImageUrl?: string
  }>
}

// 重要说明配置
export interface StatementConfig {
  statements: Array<{
    title: string
    ossUrl: string
    url: string
    sortOrder: number
    updateTime: string
  }>
  config: {
    max_items: number
  }
}

// 分类配置（适配当前接口结构）
export interface CategoryConfig {
  categoryId: number
  categoryName: string
  description: string
  imageUrl: string
  maxProducts: number
  products?: any[] // 动态加载的商品数据
}

// 原始分类配置结构（从接口返回）
export interface OriginalCategoryConfig {
  categories: Array<{
    id: number
    name: string
    slug: string
    description: string
    imageUrl: string
    sortOrder: number
    isActive: boolean
  }>
}

// 首页配置汇总
export interface HomePageConfigs {
  announcement: string
  banner: BannerConfig
  statement: StatementConfig
  categories: CategoryConfig[]
}

class ConfigService {
  // 配置服务类，目前暂无实现
}

export default new ConfigService()
