// services/orderService.ts
// 订单相关的API服务

import RequestManager from '../utils/request'
import { DateFormatter } from '../utils/dateFormatter'
import { OrderInfo, OrderItem } from '../types/payment'

// 订单状态枚举（与服务端保持一致）
export enum OrderStatus {
  PENDING_PAYMENT = 'pending_payment',    // 待支付
  PAID_SHIPPED = 'paid_shipped',          // 已支付+待发货
  SHIPPED = 'shipped',                    // 已发货
  COMPLETED = 'completed',                // 已完成
  CANCELLED = 'cancelled',                // 已取消
  REFUNDED = 'refunded'                   // 已退款
}

// 订单列表查询参数
export interface OrderQueryParams {
  status?: string
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 订单列表接口返回的简化订单数据结构
interface ServerOrderListItem {
  id: number
  userId: number
  orderNumber: string
  status: string
  statusDescription: string
  totalAmount: number
  itemsTotalAmount: number
  discountAmount: number
  shippingFee: number
  itemCount?: number
  firstProductName?: string
  firstProductImageUrl?: string | null
  firstProductSkuAttributes?: string
  recipientName?: string
  recipientPhone?: string
  placedAt: string
  paidAt: string | null
  createdAt: string
  updatedAt: string
}

// 服务器返回的完整订单数据结构（订单详情接口）
interface ServerOrderData {
  id: number
  userId: number
  orderNumber: string
  status: string
  statusDescription: string
  totalAmount: number
  itemsTotalAmount: number
  discountAmount: number
  shippingFee: number
  shippingAddress: {
    recipientName: string
    phoneNumber: string
    regionProvince: string
    regionCity: string
    regionDistrict: string
    streetAddress: string
    postalCode: string
    fullAddress: string
  }
  billingAddress: any
  paymentMethod: string
  shippingCompany: string | null
  trackingNumber: string | null
  notesToSeller: string | null
  adminNotes: string | null
  items: Array<{
    id: number
    orderId: number
    skuId: number
    productId: number
    productNameSnapshot: string
    skuAttributesSnapshot: {
      skuCode: string
      skuName: string
      price: number
      marketPrice: number | null
      costPrice: number | null
      stockQuantity: number
      weight: number | null
      imageUrl: string
      attributes: Record<string, string>
      brandName: string | null
      categoryName: string | null
    }
    quantity: number
    unitPriceAtPurchase: number
    totalPrice: number
    customizationType: string | null
    customizationId: number | null
  }>
  placedAt: string
  paidAt: string | null
  shippedAt: string | null
  deliveredAt: string | null
  cancelledAt: string | null
  completedAt: string | null
  createdAt: string
  updatedAt: string
}

// 分页响应数据结构
interface PaginatedOrderData {
  records: ServerOrderListItem[]
  total: number
  pageNum: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrevious: boolean
  empty: boolean
  size: number
}

// API响应类型
interface OrderListResponse {
  code: string
  message: string
  data: PaginatedOrderData
  timestamp: number
  success: boolean
}

interface OrderResponse {
  code: string
  message: string
  data: ServerOrderData
  timestamp: number
  success: boolean
}

interface OrderDetailResponse {
  code: string
  message: string
  data: ServerOrderData
  timestamp: number
  success: boolean
}

interface OrderCountResponse {
  code: string
  message: string
  data: {
    pending_payment: number    // 待支付
    paid_shipped: number       // 已支付+待发货
    shipped: number           // 已发货
    completed: number         // 已完成
    cancelled: number         // 已取消
    refunded: number          // 已退款
    total: number
  }
  timestamp: number
  success: boolean
}

class OrderService {
  /**
   * 获取用户订单列表
   * @param params 查询参数
   * @returns Promise<OrderInfo[]>
   */
  async getUserOrders(params?: OrderQueryParams): Promise<OrderInfo[]> {
    try {
      // 构建查询参数
      const queryParts: string[] = []
      if (params && params.status) queryParts.push(`status=${params.status}`)
      if (params && params.page) queryParts.push(`page=${params.page}`)
      if (params && params.pageSize) queryParts.push(`pageSize=${params.pageSize}`)
      if (params && params.sortBy) queryParts.push(`sortBy=${params.sortBy}`)
      if (params && params.sortOrder) queryParts.push(`sortOrder=${params.sortOrder}`)

      const queryString = queryParts.join('&')
      const url = `/api/user/orders${queryString ? '?' + queryString : ''}`

      console.log('调用订单列表接口:', url)

      const response: OrderListResponse = await RequestManager.get(url)

      if (response.success && response.data && response.data.records) {
        // 转换服务器数据为前端期望的格式
        const orders = response.data.records.map(serverOrder => this.convertServerOrderListItemToOrderInfo(serverOrder))
        return orders
      } else {
        console.error('获取订单列表失败:', response.message)
        return []
      }
    } catch (error) {
      console.error('获取订单列表异常:', error)
      return []
    }
  }

  /**
   * 根据订单ID获取订单详情
   * @param orderId 订单ID
   * @returns Promise<OrderInfo | null>
   */
  async getOrderById(orderId: number): Promise<OrderInfo | null> {
    try {
      console.log('获取订单详情，ID:', orderId)

      const response: OrderDetailResponse = await RequestManager.get(`/api/user/orders/${orderId}`)

      if (response.success && response.data) {
        console.log('获取订单详情成功:', response.data)
        // 转换服务器数据为前端期望的格式
        return this.convertServerOrderToOrderInfo(response.data)
      } else {
        console.error('获取订单详情失败:', response.message)
        return null
      }
    } catch (error) {
      console.error('获取订单详情异常:', error)
      return null
    }
  }

  /**
   * 根据订单号获取订单详情
   * @param orderNumber 订单号
   * @returns Promise<OrderInfo | null>
   */
  async getOrderByNumber(orderNumber: string): Promise<OrderInfo | null> {
    try {
      const response: OrderResponse = await RequestManager.get(`/api/user/orders/number/${orderNumber}`)

      if (response.success && response.data) {
        // 转换服务器数据为前端期望的格式
        return this.convertServerOrderToOrderInfo(response.data)
      } else {
        console.error('获取订单详情失败:', response.message)
        return null
      }
    } catch (error) {
      console.error('获取订单详情异常:', error)
      return null
    }
  }

  /**
   * 转换服务器订单列表项为前端OrderInfo格式
   * @param serverOrder 服务器返回的订单列表项数据
   * @returns OrderInfo
   */
  convertServerOrderListItemToOrderInfo(serverOrder: ServerOrderListItem): OrderInfo {
    console.log('开始转换订单列表项数据:', serverOrder)

    // 服务器返回的金额是以元为单位，需要转换为分
    const totalAmountInFen = Math.round(serverOrder.totalAmount * 100)

    // 创建一个基础的订单商品项（订单列表只有基础信息）
    const orderItem: OrderItem = {
      skuId: 0, // 订单列表没有详细的SKU信息
      skuCode: '',
      productName: serverOrder.firstProductName || '未知商品',
      skuAttributes: serverOrder.firstProductSkuAttributes || '',
      unitPrice: serverOrder.itemCount ? Math.round(totalAmountInFen / serverOrder.itemCount) : totalAmountInFen,
      quantity: serverOrder.itemCount || 1,
      subtotal: totalAmountInFen,
      productImageUrl: serverOrder.firstProductImageUrl || ''
    }

    const result = {
      id: serverOrder.id,
      orderNumber: serverOrder.orderNumber,
      status: serverOrder.status,
      statusDescription: serverOrder.statusDescription,
      totalAmount: totalAmountInFen,
      createTime: DateFormatter.formatDateTime(serverOrder.createdAt),
      items: [orderItem], // 订单列表只显示第一个商品
      shippingAddress: {
        recipientName: serverOrder.recipientName || '',
        phoneNumber: serverOrder.recipientPhone || '',
        fullAddress: ''
      }
    }

    console.log('转换后的订单列表项数据:', result)
    return result
  }

  /**
   * 转换服务器订单详情数据为前端OrderInfo格式
   * @param serverOrder 服务器返回的完整订单数据
   * @returns OrderInfo
   */
  convertServerOrderToOrderInfo(serverOrder: ServerOrderData): OrderInfo {
    console.log('开始转换订单数据:', serverOrder)

    // 服务器返回的金额是以元为单位，需要转换为分
    const totalAmountInFen = Math.round(serverOrder.totalAmount * 100)

    // 转换订单商品项 - 添加安全检查
    const orderItems: OrderItem[] = (serverOrder.items || []).map(item => {
      const unitPriceInFen = Math.round(item.unitPriceAtPurchase * 100)
      const subtotalInFen = Math.round(item.totalPrice * 100)

      // 构建SKU属性字符串
      const attributes = item.skuAttributesSnapshot?.attributes || {}
      const skuAttributes = Object.entries(attributes)
        .map(([key, value]) => `${key}：${value}`)
        .join('，')

      return {
        skuId: item.skuId,
        skuCode: item.skuAttributesSnapshot?.skuCode || '',
        productName: item.productNameSnapshot || '未知商品',
        skuAttributes: skuAttributes || item.skuAttributesSnapshot?.skuName || '',
        unitPrice: unitPriceInFen,
        quantity: item.quantity || 1,
        subtotal: subtotalInFen,
        productImageUrl: item.skuAttributesSnapshot?.imageUrl || ''
      }
    })

    const result = {
      id: serverOrder.id,
      orderNumber: serverOrder.orderNumber,
      status: serverOrder.status,
      statusDescription: serverOrder.statusDescription,
      totalAmount: totalAmountInFen,
      createTime: DateFormatter.formatDateTime(serverOrder.createdAt),
      items: orderItems,
      shippingAddress: {
        recipientName: serverOrder.shippingAddress?.recipientName || '',
        phoneNumber: serverOrder.shippingAddress?.phoneNumber || '',
        fullAddress: serverOrder.shippingAddress?.fullAddress || ''
      },
      // 发货信息
      shippingCompany: serverOrder.shippingCompany || undefined,
      trackingNumber: serverOrder.trackingNumber || undefined,
      shippedAt: serverOrder.shippedAt ? DateFormatter.formatDateTime(serverOrder.shippedAt) : undefined
    }

    console.log('转换后的订单数据:', result)
    return result
  }

  /**
   * 获取各状态订单数量统计
   * @returns Promise<any>
   */
  async getOrderCounts(): Promise<any> {
    try {
      console.log('调用订单统计接口: /api/user/orders/count/all-status')
      const response: OrderCountResponse = await RequestManager.get('/api/user/orders/count/all-status')

      if (response.success && response.data) {
        console.log('获取订单统计成功:', response.data)
        return response.data
      } else {
        console.error('获取订单统计失败:', response.message)
        return {
          pending_payment: 0,
          paid_shipped: 0,
          shipped: 0,
          completed: 0,
          cancelled: 0,
          refunded: 0,
          total: 0
        }
      }
    } catch (error) {
      console.error('获取订单统计异常:', error)
      return {
        pending_payment: 0,
        paid_shipped: 0,
        shipped: 0,
        completed: 0,
        cancelled: 0,
        refunded: 0,
        total: 0
      }
    }
  }

  /**
   * 取消订单
   * @param orderId 订单ID
   * @returns Promise<boolean>
   */
  async cancelOrder(orderId: number): Promise<boolean> {
    try {
      const response = await RequestManager.post(`/api/user/orders/${orderId}/cancel`)

      if (response.success) {
        return true
      } else {
        console.error('取消订单失败:', response.message)
        return false
      }
    } catch (error) {
      console.error('取消订单异常:', error)
      return false
    }
  }

  /**
   * 确认收货
   * @param orderId 订单ID
   * @returns Promise<boolean>
   */
  async confirmReceived(orderId: number): Promise<boolean> {
    try {
      const response = await RequestManager.post(`/api/user/orders/${orderId}/confirm-receipt`)

      if (response.success) {
        return true
      } else {
        console.error('确认收货失败:', response.message)
        return false
      }
    } catch (error) {
      console.error('确认收货异常:', error)
      return false
    }
  }

  /**
   * 删除订单
   * @param orderId 订单ID
   * @returns Promise<boolean>
   */
  async deleteOrder(orderId: number): Promise<boolean> {
    try {
      const response = await RequestManager.delete(`/api/user/orders/${orderId}`)

      if (response.success) {
        return true
      } else {
        console.error('删除订单失败:', response.message)
        return false
      }
    } catch (error) {
      console.error('删除订单异常:', error)
      return false
    }
  }

  /**
   * 格式化订单状态显示文本
   * @param status 订单状态
   * @returns string
   */
  formatOrderStatus(status: string): string {
    const statusMap: Record<string, string> = {
      [OrderStatus.PENDING_PAYMENT]: '待支付',
      [OrderStatus.PAID_SHIPPED]: '待发货',
      [OrderStatus.SHIPPED]: '待收货'
    }
    return statusMap[status] || status
  }

  /**
   * 获取订单状态对应的操作按钮
   * @param status 订单状态
   * @param orderInfo 订单信息（可选，用于退款时效判断）
   * @returns Array<{text: string, action: string}>
   */
  getOrderActions(status: string, orderInfo?: any): Array<{text: string, action: string, type?: string, disabled?: boolean}> {
    switch (status) {
      case OrderStatus.PENDING_PAYMENT:
        return [
          { text: '取消订单', action: 'cancel', type: 'secondary' },
          { text: '立即支付', action: 'pay', type: 'primary' }
        ]
      case OrderStatus.PAID_SHIPPED:
        return [
          { text: '申请退款', action: 'refund', type: 'secondary' }
        ]
      case OrderStatus.SHIPPED:
        return [
          { text: '申请退款', action: 'refund', type: 'secondary' },
          { text: '确认收货', action: 'confirm', type: 'primary' }
        ]
      default:
        return []
    }
  }
}

export default new OrderService()
