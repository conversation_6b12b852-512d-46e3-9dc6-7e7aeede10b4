// services/categoryProductService.ts
// 分类商品服务

import RequestManager from '../utils/request'

// 分类商品查询参数
export interface CategoryProductParams {
  page?: number
  size?: number
  sortBy?: 'created_at' | 'price' | 'sales' | 'rating'
  sortOrder?: 'asc' | 'desc'
}

// 商品基础信息（匹配实际接口返回）
export interface Product {
  id: number
  name: string
  slug: string
  brandId?: number
  brandName?: string
  shortDescription?: string
  fullDescription?: string
  mainImageUrl: string
  productImages: any[]
  detailImages: any[]
  isActive: boolean
  isFeatured: boolean
  publishedAt?: string
  createdAt: string
  updatedAt: string
  categories?: any
  skus?: any[]
  minPrice?: string  // 服务端返回的是字符串格式
  maxPrice?: string  // 服务端返回的是字符串格式
  totalStock?: number
  priceRange?: string
  published: boolean
}

// 分页数据结构
export interface PaginatedProductData {
  records: Product[]
  total: number
  pageNum: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrevious: boolean
  empty: boolean
  size: number
}

// 分类商品响应（实际接口结构）
export interface CategoryProductResponse {
  code: string
  message: string
  data: PaginatedProductData // 分页对象
  success: boolean
  timestamp: number
}

class CategoryProductService {
  /**
   * 获取分类下的商品列表
   */
  async getCategoryProducts(categoryId: number, params: CategoryProductParams = {}): Promise<Product[]> {
    try {
      // 设置默认参数
      const {
        page = 1,
        size = 10,
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = params

      // 手动构建查询参数（小程序不支持URLSearchParams）
      const queryString = [
        `page=${page}`,
        `size=${size}`,
        `sortBy=${sortBy}`,
        `sortOrder=${sortOrder}`
      ].join('&')

      const url = `/api/public/categories/${categoryId}/products?${queryString}`
      console.log(`调用分类商品接口: ${url}`)

      const response: CategoryProductResponse = await RequestManager.get(url)

      if (response.success && response.data) {
        console.log(`获取分类${categoryId}商品成功:`, response.data)
        // 从分页对象中提取商品数组
        return response.data.records || []
      } else {
        console.error(`获取分类${categoryId}商品失败:`, response.message)
        throw new Error(response.message || '获取分类商品失败')
      }
    } catch (error) {
      console.error(`获取分类${categoryId}商品失败:`, error)
      throw error
    }
  }

  /**
   * 获取分类商品预览（首页用）
   */
  async getCategoryProductsPreview(categoryId: number, maxProducts: number = 6): Promise<Product[]> {
    try {
      const result = await this.getCategoryProducts(categoryId, {
        page: 1,
        size: maxProducts,
        sortBy: 'createdAt', // 根据实际接口调整排序字段
        sortOrder: 'desc'
      })

      return result || []
    } catch (error) {
      console.error(`获取分类${categoryId}商品预览失败:`, error)
      return []
    }
  }

  /**
   * 批量获取多个分类的商品预览
   */
  async getMultipleCategoryProductsPreview(categories: Array<{categoryId: number, maxProducts: number}>): Promise<Record<number, Product[]>> {
    const results: Record<number, Product[]> = {}

    const promises = categories.map(async (category) => {
      try {
        const products = await this.getCategoryProductsPreview(category.categoryId, category.maxProducts)
        results[category.categoryId] = products
      } catch (error) {
        console.error(`获取分类${category.categoryId}商品预览失败:`, error)
        results[category.categoryId] = []
      }
    })

    await Promise.all(promises)
    return results
  }
}

export default new CategoryProductService()
