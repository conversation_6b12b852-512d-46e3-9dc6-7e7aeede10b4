// services/invitationService.ts
import RequestManager from '../utils/request'

export interface InvitationCode {
  id: number
  userId: number
  invitationCode: string
  isActive: boolean
  expiresAt?: string
  createdAt: string
  hasUsedInvitationCode?: boolean
}

export interface InvitationRecord {
  id: number
  inviterUserId: number
  inviteeUserId: number
  invitationCode: string
  invitationSource: string
  invitationSourceDesc: string
  invitationStatus: string
  invitationStatusDesc: string
  activatedAt?: string
  firstOrderAt?: string
  firstOrderId?: number
  createdAt: string
  inviteeUsername?: string
  inviteePhone?: string
  inviteeNickname?: string
  inviteeOpenId?: string
  inviteeHasOrdered: boolean
  hasOrdered?: boolean
  // 兼容旧字段名
  invitedUserNickname?: string
}

export interface InvitationReward {
  id: number
  userId: number
  userNickname?: string
  userPhone?: string
  configName?: string
  rewardType: 'REGISTER' | 'FIRST_ORDER'
  rewardTypeDesc: string
  rewardTarget: 'INVITER' | 'INVITEE'
  rewardTargetDesc: string
  rewardForm: 'COUPON' | 'CASH' | 'POINTS'
  rewardFormDesc: string
  rewardAmount: number
  rewardCurrency: string
  status: 'PENDING' | 'ISSUED' | 'EXPIRED'
  statusDesc: string
  description?: string
  issuedAt?: string
  expiresAt?: string
  createdAt: string
  invitationRecordId?: number
  orderId?: number
}

export interface InvitationStats {
  totalInvitations: number
  activatedInvitations: number
  orderedInvitations: number
  totalRewards: number
  availableRewards: number
  // 新增字段以支持新页面
  totalRewardAmount?: number  // 总奖励金额（元为单位）
  totalInvitedUsers?: number  // 总邀请用户数
  totalOrderedUsers?: number  // 已下单用户数
}

export interface UserCoupon {
  id: number
  userId: number
  couponCode: string
  couponType: string
  discountAmount: number
  minimumAmount: number
  status: string
  statusDesc: string
  expiresAt: string
  createdAt: string
  usedAt?: string
  orderId?: number
}

class InvitationService {
  
  /**
   * 获取我的邀请码
   */
  async getMyInvitationCode(): Promise<InvitationCode> {
    const response = await RequestManager.get('/api/invitation/my-code')
    return response.data !== undefined ? response.data : response
  }

  /**
   * 生成邀请码
   */
  async generateInvitationCode(): Promise<InvitationCode> {
    const response = await RequestManager.post('/api/invitation/generate-code')
    return response.data !== undefined ? response.data : response
  }

  /**
   * 使用邀请码
   */
  async useInvitationCode(invitationCode: string): Promise<any> {
    const response = await RequestManager.post('/api/invitation/fill-code', {
      invitationCode,
      invitationSource: 'MINIPROGRAM'
    })
    return response
  }

  /**
   * 获取我的邀请记录
   */
  async getMyInvitationRecords(page: number = 1, size: number = 20): Promise<{
    records: InvitationRecord[]
    total: number
    hasMore: boolean
  }> {
    const recordsResponse = await RequestManager.get(`/api/invitation/my-records?page=${page}&size=${size}`)
    const totalResponse = await RequestManager.get('/api/invitation/my-records/count')

    const records = recordsResponse.data !== undefined ? recordsResponse.data : (recordsResponse || [])
    const total = totalResponse.data !== undefined ? totalResponse.data : (totalResponse || 0)

    return {
      records,
      total,
      hasMore: records && records.length === size
    }
  }

  /**
   * 获取邀请统计
   */
  async getInvitationStats(): Promise<InvitationStats> {
    const response = await RequestManager.get('/api/invitation/stats')
    return response.data !== undefined ? response.data : response
  }

  /**
   * 获取我的邀请奖励记录
   */
  async getMyInvitationRewards(page: number = 1, size: number = 20): Promise<{
    rewards: InvitationReward[]
    total: number
    hasMore: boolean
    totalRewardAmount: number
  }> {
    const response = await RequestManager.get(`/api/invitation/my-rewards?page=${page}&size=${size}`)
    const data = response.data || {}

    // 新的响应格式包含 totalRewardAmount 和 rewards 字段
    const rewards = data.rewards || []
    const totalRewardAmount = data.totalRewardAmount || 0

    return {
      rewards,
      total: data.totalCount || rewards.length,
      hasMore: data.hasNext || false,
      totalRewardAmount: totalRewardAmount
    }
  }

  /**
   * 获取我的抵扣券
   */
  async getMyCoupons(page: number = 1, size: number = 20): Promise<{
    coupons: UserCoupon[]
    total: number
    hasMore: boolean
  }> {
    const response = await RequestManager.get(`/api/coupons/all?page=${page}&size=${size}`)
    const coupons = response.data !== undefined ? response.data : (response || [])

    return {
      coupons,
      total: coupons ? coupons.length : 0,
      hasMore: coupons && coupons.length === size
    }
  }

  /**
   * 验证邀请码
   */
  async validateInvitationCode(code: string): Promise<boolean> {
    const response = await RequestManager.get(`/api/invitation/validate/${code}`)
    return response.data !== undefined ? response.data : response
  }

  /**
   * 检查用户是否已使用过邀请码
   */
  async checkInvitationCodeUsedStatus(): Promise<boolean> {
    const response = await RequestManager.get('/api/invitation/check-used-status')
    return response.data !== undefined ? response.data : response
  }

  /**
   * 分享邀请码
   */
  generateInviteShareContent(invitationCode: string): {
    title: string
    desc: string
    path: string
    imageUrl: string
  } {
    return {
      title: '邀请好友领现金',
      desc: `使用我的邀请码 ${invitationCode}，注册即可获得优惠券！`,
      path: `/pages/home/<USER>
      imageUrl: '/images/invite-share.png'
    }
  }

  /**
   * 复制邀请码到剪贴板
   */
  copyInvitationCode(code: string): Promise<void> {
    return new Promise((resolve, reject) => {
      wx.setClipboardData({
        data: code,
        success: () => {
          wx.showToast({
            title: '邀请码已复制',
            icon: 'success'
          })
          resolve()
        },
        fail: (error) => {
          wx.showToast({
            title: '复制失败',
            icon: 'error'
          })
          reject(error)
        }
      })
    })
  }

  /**
   * 分享邀请码
   */
  shareInvitationCode(_invitationCode: string): Promise<void> {
    return new Promise((resolve, reject) => {
      wx.showShareMenu({
        withShareTicket: true,
        success: () => {
          resolve()
        },
        fail: reject
      })
    })
  }
}

export default new InvitationService()
