// services/productService.ts
// 商品相关的API服务

import RequestManager from '../utils/request'
import { ProductListResponse, ProductDTO, ProductQueryParams, ProductWithSkusResponse, ProductWithSkusDTO, PagedData } from '../types/product'

class ProductService {
  /**
   * 根据分类ID获取商品列表（公开接口）
   * @param categoryId 分类ID
   * @param params 查询参数
   * @returns Promise<ProductDTO[]>
   */
  async getProductsByCategoryId(categoryId: number, params?: ProductQueryParams): Promise<ProductDTO[]> {
    try {
      // 构建查询参数 - 微信小程序兼容方式
      const queryParts: string[] = []
      if (params && params.page) queryParts.push(`page=${params.page}`)
      if (params && params.pageSize) queryParts.push(`pageSize=${params.pageSize}`)
      if (params && params.sortBy) queryParts.push(`sortBy=${params.sortBy}`)
      if (params && params.sortOrder) queryParts.push(`sortOrder=${params.sortOrder}`)

      const queryString = queryParts.join('&')
      const url = `/api/public/categories/${categoryId}/products${queryString ? '?' + queryString : ''}`

      console.log('调用商品接口:', url)

      const response: ProductListResponse = await RequestManager.get(url)

      if (response.success && response.data) {
        // 接口返回的是分页对象，需要提取 records 数组
        return response.data.records || []
      } else {
        console.error('获取商品列表失败:', response.message)
        throw new Error(response.message || '获取商品数据失败')
      }
    } catch (error) {
      console.error('获取商品列表异常:', error)
      throw error
    }
  }

  /**
   * 根据商品ID获取商品详情
   * @param productId 商品ID
   * @returns Promise<ProductDTO>
   */
  async getProductById(productId: number): Promise<ProductDTO> {
    try {
      const response = await RequestManager.get(`/api/public/products/${productId}`)

      if (response.success && response.data) {
        return response.data
      } else {
        console.error('获取商品详情失败:', response.message)
        throw new Error(response.message || '获取商品详情失败')
      }
    } catch (error) {
      console.error('获取商品详情异常:', error)
      throw error
    }
  }

  /**
   * 根据商品ID获取商品及其SKU信息（公开接口）
   * @param productId 商品ID
   * @returns Promise<ProductWithSkusDTO>
   */
  async getProductWithSkus(productId: number): Promise<ProductWithSkusDTO> {
    try {
      const url = `/api/public/products/${productId}/with-skus`
      console.log('调用商品详情接口:', url)

      const response: ProductWithSkusResponse = await RequestManager.get(url)

      if (response.success && response.data) {
        console.log('获取到的商品详情数据:', response.data)
        return response.data
      } else {
        console.error('获取商品详情失败:', response.message)
        throw new Error(response.message || '获取商品详情失败')
      }
    } catch (error) {
      console.error('获取商品详情异常:', error)
      throw error
    }
  }

  /**
   * 搜索商品
   * @param keyword 搜索关键词
   * @param params 查询参数
   * @returns Promise<ProductDTO[]>
   */
  async searchProducts(keyword: string, params?: ProductQueryParams): Promise<PagedData<ProductDTO>> {
    try {
      console.log('ProductService搜索关键词:', keyword)

      // 构建查询参数 - 微信小程序兼容方式
      const queryParts: string[] = [`keyword=${encodeURIComponent(keyword)}`]
      if (params && params.page) queryParts.push(`page=${params.page}`)
      if (params && params.pageSize) queryParts.push(`pageSize=${params.pageSize}`)
      if (params && params.categoryId) queryParts.push(`categoryId=${params.categoryId}`)
      if (params && params.brandId) queryParts.push(`brandId=${params.brandId}`)
      if (params && params.sortBy) queryParts.push(`sortBy=${params.sortBy}`)
      if (params && params.sortOrder) queryParts.push(`sortOrder=${params.sortOrder}`)

      const queryString = queryParts.join('&')
      const url = `/api/public/products/search?${queryString}`

      console.log('调用搜索接口:', url)

      const response: ProductListResponse = await RequestManager.get(url)

      if (response.success && response.data) {
        // 返回完整的分页数据
        return response.data
      } else {
        console.error('搜索商品失败:', response.message)
        throw new Error(response.message || '搜索商品失败')
      }
    } catch (error) {
      console.error('搜索商品异常:', error)
      throw error
    }
  }

  /**
   * 获取推荐商品
   * @param params 查询参数
   * @returns Promise<ProductDTO[]>
   */
  async getFeaturedProducts(params?: ProductQueryParams): Promise<ProductDTO[]> {
    try {
      // 构建查询参数 - 微信小程序兼容方式
      const queryParts: string[] = []
      if (params && params.page) queryParts.push(`page=${params.page}`)
      if (params && params.pageSize) queryParts.push(`pageSize=${params.pageSize}`)

      const queryString = queryParts.join('&')
      const url = `/api/public/products/featured${queryString ? '?' + queryString : ''}`

      console.log('调用推荐商品接口:', url)

      const response: ProductListResponse = await RequestManager.get(url)

      if (response.success && response.data) {
        // 接口返回的是分页对象，需要提取 records 数组
        return response.data.records || []
      } else {
        console.error('获取推荐商品失败:', response.message)
        throw new Error(response.message || '获取推荐商品失败')
      }
    } catch (error) {
      console.error('获取推荐商品异常:', error)
      throw error
    }
  }
}

export default new ProductService()
