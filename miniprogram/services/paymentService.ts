// services/paymentService.ts
// 支付相关的API服务

import RequestManager from '../utils/request'
import OrderService from './orderService'
import {
  CreatePaymentRequest,
  PaymentParams,
  PaymentStatusResponse,
  PaymentStatus,
  CreatePaymentResponse,
  PaymentStatusQueryResponse,
  CreateOrderRequest,
  OrderInfo,
  ApiResponse
} from '../types/payment'

class PaymentService {
  /**
   * 创建支付订单
   * @param request 创建支付请求数据
   * @returns Promise<PaymentParams>
   */
  async createPayment(request: CreatePaymentRequest): Promise<PaymentParams> {
    try {
      // 调试日志：检查请求参数
      console.log('创建支付请求参数:', request)

      // 验证必要参数
      if (!request.orderNo) {
        throw new Error('orderNo 参数不能为空')
      }
      if (!request.totalAmount) {
        throw new Error('totalAmount 参数不能为空')
      }
      if (!request.description) {
        throw new Error('description 参数不能为空')
      }

      const response: CreatePaymentResponse = await RequestManager.post('/api/user/payment/create', request)

      // 调试日志：检查响应结果
      console.log('创建支付响应:', response)

      if (response.success && response.data) {
        return response.data
      } else {
        console.error('创建支付订单失败:', response.message)
        throw new Error(response.message || '创建支付订单失败')
      }
    } catch (error) {
      console.error('创建支付订单异常:', error)
      throw error
    }
  }

  /**
   * 查询支付状态
   * @param orderNo 订单号
   * @returns Promise<PaymentStatusResponse>
   */
  async queryPaymentStatus(orderNo: string): Promise<PaymentStatusResponse> {
    try {
      const response: PaymentStatusQueryResponse = await RequestManager.get(`/api/user/payment/status/${orderNo}`)

      if (response.success && response.data) {
        // 接口返回的 data 直接是状态字符串，需要包装成对象
        return {
          status: response.data,
          orderNo: orderNo
        }
      } else {
        console.error('查询支付状态失败:', response.message)
        throw new Error(response.message || '查询支付状态失败')
      }
    } catch (error) {
      console.error('查询支付状态异常:', error)
      throw error
    }
  }

  /**
   * 查询支付状态详情
   * @param orderNo 订单号
   * @returns Promise<PaymentStatusResponse>
   */
  async queryPaymentStatusDetail(orderNo: string): Promise<PaymentStatusResponse> {
    try {
      const response: ApiResponse<PaymentStatusResponse> = await RequestManager.get(`/api/user/payment/status-detail/${orderNo}`)

      if (response.success && response.data) {
        return response.data
      } else {
        console.error('查询支付状态详情失败:', response.message)
        throw new Error(response.message || '查询支付状态详情失败')
      }
    } catch (error) {
      console.error('查询支付状态详情异常:', error)
      throw error
    }
  }

  /**
   * 创建订单
   * @param request 创建订单请求数据
   * @returns Promise<OrderInfo>
   */
  async createOrder(request: CreateOrderRequest): Promise<OrderInfo> {
    try {
      // 调试日志：检查创建订单请求参数
      console.log('创建订单请求参数:', request)

      const response = await RequestManager.post('/api/user/orders', request)

      // 调试日志：检查创建订单响应
      console.log('创建订单响应:', response)

      if (response.success && response.data) {
        // 验证返回的订单信息
        if (!response.data.orderNumber) {
          console.error('创建订单成功但未返回订单号:', response.data)
          throw new Error('创建订单成功但未返回订单号')
        }

        console.log('订单创建成功，订单号:', response.data.orderNumber)
        return response.data
      } else {
        console.error('创建订单失败:', response.message)
        throw new Error(response.message || '创建订单失败')
      }
    } catch (error) {
      console.error('创建订单异常:', error)
      throw error
    }
  }

  /**
   * 根据订单号查询订单详情
   * @param orderNo 订单号
   * @returns Promise<OrderInfo>
   */
  async getOrderByOrderNo(orderNo: string): Promise<OrderInfo> {
    try {
      // 使用 OrderService 的方法来获取订单详情，确保数据转换逻辑一致
      const orderInfo = await OrderService.getOrderByNumber(orderNo)

      if (orderInfo) {
        return orderInfo
      } else {
        throw new Error('订单不存在')
      }
    } catch (error) {
      console.error('查询订单详情异常:', error)
      throw error
    }
  }

  /**
   * 调用微信支付
   * @param paymentParams 支付参数
   * @returns Promise<void>
   */
  async callWechatPay(paymentParams: PaymentParams): Promise<void> {
    try {
      await wx.requestPayment({
        timeStamp: paymentParams.timeStamp,
        nonceStr: paymentParams.nonceStr,
        package: paymentParams.packageValue,
        signType: paymentParams.signType as 'MD5' | 'HMAC-SHA256' | 'RSA',
        paySign: paymentParams.paySign
      })

      console.log('微信支付调用成功')
    } catch (error: any) {
      console.error('微信支付调用失败:', error)

      if (error.errMsg === 'requestPayment:cancel') {
        throw new Error('用户取消支付')
      } else {
        throw new Error('支付失败，请重试')
      }
    }
  }

  /**
   * 生成订单号
   * @returns string
   */
  generateOrderNo(): string {
    const timestamp = Date.now()
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    return `ORDER_${timestamp}_${random}`
  }

  /**
   * 元转分
   * @param yuan 元
   * @returns number 分
   */
  yuanToFen(yuan: number): number {
    return Math.round(yuan * 100)
  }

  /**
   * 分转元
   * @param fen 分
   * @returns number 元
   */
  fenToYuan(fen: number): number {
    return fen / 100
  }

  /**
   * 安全的价格乘法计算（避免浮点数精度问题）
   * @param price 单价（元）
   * @param quantity 数量
   * @returns number 总价（分）
   */
  calculatePriceInFen(price: number, quantity: number): number {
    return Math.round(price * quantity * 100)
  }

  /**
   * 格式化金额显示
   * @param amount 金额（分）
   * @returns string 格式化后的金额
   */
  formatAmount(amount: number): string {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return '0.00'
    }
    return (amount / 100).toFixed(2)
  }

  /**
   * 获取支付状态描述
   * @param status 支付状态
   * @returns string 状态描述
   */
  getPaymentStatusText(status: string): string {
    const statusMap: { [key: string]: string } = {
      'SUCCESS': '支付成功',
      'NOTPAY': '未支付',
      'CLOSED': '已关闭',
      'REFUND': '转入退款',
      'REVOKED': '已撤销',
      'USERPAYING': '用户支付中',
      'PAYERROR': '支付失败'
    }

    return statusMap[status] || '未知状态'
  }

  /**
   * 完整的支付流程（订单已存在）
   * @param paymentData 支付数据
   * @returns Promise<void>
   */
  async processPayment(paymentData: CreatePaymentRequest): Promise<void> {
    try {
      // 1. 创建支付订单（基于已存在的订单）
      const paymentParams = await this.createPayment(paymentData)

      // 2. 调用微信支付
      await this.callWechatPay(paymentParams)

      // 3. 支付成功，跳转到成功页面
      wx.redirectTo({
        url: `/pages/payment-success/payment-success?orderNo=${paymentData.orderNo}`
      })

    } catch (error: any) {
      console.error('支付流程失败:', error)

      // 根据错误类型显示不同提示
      if (error.message === '用户取消支付') {
        wx.showToast({
          title: '支付已取消',
          icon: 'none'
        })
      } else {
        wx.showToast({
          title: error.message || '支付失败，请重试',
          icon: 'error'
        })
      }

      throw error
    }
  }

  /**
   * 完整的订单创建和支付流程
   * @param orderData 订单数据
   * @param paymentDescription 支付描述
   * @returns Promise<void>
   */
  async createOrderAndPay(orderData: CreateOrderRequest, paymentDescription: string): Promise<void> {
    try {
      // 1. 创建订单
      const orderInfo = await this.createOrder(orderData)

      // 2. 创建支付数据
      const paymentData: CreatePaymentRequest = {
        orderNo: orderInfo.orderNumber,
        totalAmount: orderData.totalAmount,
        description: paymentDescription,
        attach: JSON.stringify({
          orderId: orderInfo.id,
          addressId: orderData.addressId
        })
      }

      // 3. 执行支付流程
      await this.processPayment(paymentData)

    } catch (error: any) {
      console.error('创建订单和支付失败:', error)
      throw error
    }
  }
}

export default new PaymentService()
