// services/cartService.ts
// 购物车相关的API服务

import RequestManager from '../utils/request'
import ImageHelper from '../utils/imageHelper'
import {
  CartItemDTO,
  CartSummaryDTO,
  AddToCartRequest,
  UpdateCartItemRequest,
  CartItemListResponse,
  CartSummaryResponse,
  CartCountResponse,
  CartOperationResponse,
  CartItem
} from '../types/cart'

class CartService {
  /**
   * 获取购物车商品列表
   * @returns Promise<CartItemDTO[]>
   */
  async getCartItems(): Promise<CartItemDTO[]> {
    try {
      const response: CartItemListResponse = await RequestManager.get('/api/user/cart/items')

      if (response.success && response.data) {
        return response.data
      } else {
        console.error('获取购物车列表失败:', response.message)
        throw new Error(response.message || '获取购物车数据失败')
      }
    } catch (error) {
      console.error('获取购物车列表异常:', error)
      throw error
    }
  }

  /**
   * 获取购物车汇总信息
   * @returns Promise<CartSummaryDTO>
   */
  async getCartSummary(): Promise<CartSummaryDTO> {
    try {
      const response: CartSummaryResponse = await RequestManager.get('/api/user/cart/summary')

      if (response.success && response.data) {
        return response.data
      } else {
        console.error('获取购物车汇总失败:', response.message)
        throw new Error(response.message || '获取购物车汇总失败')
      }
    } catch (error) {
      console.error('获取购物车汇总异常:', error)
      throw error
    }
  }

  /**
   * 获取购物车商品数量
   * @returns Promise<number>
   */
  async getCartItemCount(): Promise<number> {
    try {
      const response: CartCountResponse = await RequestManager.get('/api/user/cart/count')

      if (response.success && typeof response.data === 'number') {
        return response.data
      } else {
        console.error('获取购物车数量失败:', response.message)
        return 0
      }
    } catch (error) {
      console.error('获取购物车数量异常:', error)
      return 0
    }
  }

  /**
   * 添加商品到购物车
   * @param request 添加到购物车的请求数据
   * @returns Promise<void>
   */
  async addToCart(request: AddToCartRequest): Promise<void> {
    try {
      const response: CartOperationResponse = await RequestManager.post('/api/user/cart/add', request)

      if (!response.success) {
        console.error('添加到购物车失败:', response.message)
        throw new Error(response.message || '添加到购物车失败')
      }
    } catch (error) {
      console.error('添加到购物车异常:', error)
      throw error
    }
  }

  /**
   * 更新购物车项数量
   * @param cartItemId 购物车项ID
   * @param request 更新请求数据
   * @returns Promise<void>
   */
  async updateCartItem(cartItemId: number, request: UpdateCartItemRequest): Promise<void> {
    try {
      const response: CartOperationResponse = await RequestManager.put(
        `/api/user/cart/items/${cartItemId}`,
        request
      )

      if (!response.success) {
        console.error('更新购物车项失败:', response.message)
        throw new Error(response.message || '更新购物车项失败')
      }
    } catch (error) {
      console.error('更新购物车项异常:', error)
      throw error
    }
  }

  /**
   * 删除购物车项
   * @param cartItemId 购物车项ID
   * @returns Promise<void>
   */
  async removeCartItem(cartItemId: number): Promise<void> {
    try {
      const response: CartOperationResponse = await RequestManager.delete(`/api/user/cart/items/${cartItemId}`)

      if (!response.success) {
        console.error('删除购物车项失败:', response.message)
        throw new Error(response.message || '删除购物车项失败')
      }
    } catch (error) {
      console.error('删除购物车项异常:', error)
      throw error
    }
  }

  /**
   * 批量删除购物车项
   * @param cartItemIds 购物车项ID列表
   * @returns Promise<void>
   */
  async batchRemoveCartItems(cartItemIds: number[]): Promise<void> {
    try {
      const response: CartOperationResponse = await RequestManager.delete(
        '/api/user/cart/items/batch',
        cartItemIds
      )

      if (!response.success) {
        console.error('批量删除购物车项失败:', response.message)
        throw new Error(response.message || '批量删除购物车项失败')
      }
    } catch (error) {
      console.error('批量删除购物车项异常:', error)
      throw error
    }
  }

  /**
   * 清空购物车
   * @returns Promise<void>
   */
  async clearCart(): Promise<void> {
    try {
      const response: CartOperationResponse = await RequestManager.delete('/api/user/cart/clear')

      if (!response.success) {
        console.error('清空购物车失败:', response.message)
        throw new Error(response.message || '清空购物车失败')
      }
    } catch (error) {
      console.error('清空购物车异常:', error)
      throw error
    }
  }

  /**
   * 将CartItemDTO转换为页面使用的CartItem格式
   * @param dto CartItemDTO
   * @returns CartItem
   */
  convertDTOToCartItem(dto: CartItemDTO): CartItem {
    // 直接使用接口返回的图片URL，因为已经是完整的URL
    const imageUrl = dto.productImageUrl || '/images/default-avatar.png'

    console.log('转换购物车项:', {
      id: dto.id,
      productName: dto.productName,
      imageUrl: imageUrl,
      skuAttributes: dto.skuAttributes,
      unitPrice: dto.unitPrice,
      quantity: dto.quantity,
      subtotal: dto.subtotal,
      inStock: dto.inStock
    })

    return {
      id: (dto.id && Number(dto.id)) || (dto.skuId && Number(dto.skuId)) || 0,
      name: dto.productName || '未知商品',
      sku: dto.skuAttributes || '',
      price: dto.unitPrice || 0,
      quantity: dto.quantity || 1,
      selected: false, // 默认不选中，页面根据内存状态控制
      image: imageUrl,
      skuId: dto.skuId || 0,
      productId: dto.productId || 0,
      subtotal: dto.subtotal || 0,
      inStock: dto.inStock !== undefined ? dto.inStock : true,
      stockQuantity: dto.stockQuantity || 0,
      // 根据商品类型判断是否需要处方（这里可以根据实际业务逻辑调整）
      needPrescription: dto.productName ? (dto.productName.includes('镜片') || dto.productName.includes('近视') || dto.productName.includes('远视')) : false
    }
  }

  /**
   * 批量转换CartItemDTO为CartItem
   * @param dtos CartItemDTO数组
   * @returns CartItem数组
   */
  convertDTOsToCartItems(dtos: CartItemDTO[]): CartItem[] {
    if (!dtos || !Array.isArray(dtos)) {
      console.warn('convertDTOsToCartItems: 传入的数据不是有效数组', dtos)
      return []
    }

    return dtos
      .filter(dto => dto && typeof dto === 'object') // 过滤掉无效的数据项
      .map(dto => {
        try {
          return this.convertDTOToCartItem(dto)
        } catch (error) {
          console.error('转换购物车项失败:', error, dto)
          // 返回一个默认的购物车项，避免整个列表加载失败
          return {
            id: (dto && (dto.id || dto.skuId)) || 0,
            name: '数据异常商品',
            sku: '',
            price: 0,
            quantity: 1,
            selected: false,
            image: '',
            skuId: 0,
            productId: 0,
            subtotal: 0,
            inStock: false,
            stockQuantity: 0,
            needPrescription: false
          }
        }
      })
  }
}

export default new CartService()
