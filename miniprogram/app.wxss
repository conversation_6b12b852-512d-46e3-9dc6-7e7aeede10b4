/**app.wxss**/
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* Common styles */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.rounded {
  border-radius: 8rpx;
}

.rounded-lg {
  border-radius: 16rpx;
}

.rounded-full {
  border-radius: 50%;
}

.shadow-sm {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.bg-white {
  background-color: #ffffff;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.bg-black {
  background-color: #000000;
}

.text-black {
  color: #000000;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-800 {
  color: #1f2937;
}

.text-white {
  color: #ffffff;
}

.text-xs {
  font-size: 24rpx;
}

.text-sm {
  font-size: 28rpx;
}

.text-base {
  font-size: 32rpx;
}

.text-lg {
  font-size: 36rpx;
}

.font-medium {
  font-weight: 500;
}

.p-2 {
  padding: 16rpx;
}

.p-3 {
  padding: 24rpx;
}

.p-4 {
  padding: 32rpx;
}

.px-2 {
  padding-left: 16rpx;
  padding-right: 16rpx;
}

.px-3 {
  padding-left: 24rpx;
  padding-right: 24rpx;
}

.py-2 {
  padding-top: 16rpx;
  padding-bottom: 16rpx;
}

.py-3 {
  padding-top: 24rpx;
  padding-bottom: 24rpx;
}

.m-2 {
  margin: 16rpx;
}

.m-3 {
  margin: 24rpx;
}

.mt-2 {
  margin-top: 16rpx;
}

.mt-3 {
  margin-top: 24rpx;
}

.mt-4 {
  margin-top: 32rpx;
}

.mb-2 {
  margin-bottom: 16rpx;
}

.mb-3 {
  margin-bottom: 24rpx;
}

.mr-2 {
  margin-right: 16rpx;
}

.mr-3 {
  margin-right: 24rpx;
}

.ml-2 {
  margin-left: 16rpx;
}

.ml-3 {
  margin-left: 24rpx;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
