// pages/sku-test/sku-test.ts
import ProductService from '../../services/productService'
import { ProductWithSkusDTO } from '../../types/product'

interface SkuTestData {
  apiResult: ProductWithSkusDTO | null
}

Page<SkuTestData>({
  data: {
    apiResult: null
  },

  onLoad() {
    console.log('SKU测试页面加载')
  },

  /**
   * 测试商品详情页面
   */
  testProductDetail() {
    console.log('跳转到商品详情页面测试')
    wx.navigateTo({
      url: '/pages/product-detail/product-detail?productId=42&productName=测试SKU信息'
    })
  },

  /**
   * 直接调用API测试
   */
  async testDirectCall() {
    try {
      wx.showLoading({ title: '加载中...' })
      
      console.log('开始调用API测试')
      const result = await ProductService.getProductWithSkus(42)
      
      console.log('API调用成功:', result)
      
      // 处理SKU数据，将attributes对象转换为数组以便显示
      const processedSkus = result.skus.map(sku => ({
        ...sku,
        attributesList: sku.attributes ? Object.entries(sku.attributes).map(([name, value]) => ({
          name,
          value
        })) : []
      }))
      
      this.setData({
        apiResult: {
          ...result,
          skus: processedSkus
        }
      })
      
      wx.hideLoading()
      wx.showToast({
        title: 'API调用成功',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('API调用失败:', error)
      wx.hideLoading()
      wx.showModal({
        title: 'API调用失败',
        content: `错误信息: ${error}`,
        showCancel: false
      })
    }
  },

  onShareAppMessage() {
    return {
      title: 'SKU属性选择功能测试',
      path: '/pages/sku-test/sku-test'
    }
  }
})
