<!--pages/sku-test/sku-test.wxml-->
<view class="container">
  <view class="test-header">
    <text class="test-title">SKU属性选择功能测试</text>
    <text class="test-desc">测试商品ID: 42 (测试SKU信息)</text>
  </view>

  <view class="test-buttons">
    <button class="test-btn" bindtap="testProductDetail">
      测试商品详情页面
    </button>
    
    <button class="test-btn" bindtap="testDirectCall">
      直接调用API测试
    </button>
  </view>

  <view wx:if="{{apiResult}}" class="api-result">
    <view class="result-title">API调用结果:</view>
    <view class="result-content">
      <text>商品名称: {{apiResult.product.name}}</text>
      <text>SKU数量: {{apiResult.skus.length}}</text>
      <text>价格范围: ¥{{apiResult.statistics.minPrice}} - ¥{{apiResult.statistics.maxPrice}}</text>
    </view>
    
    <view class="sku-list">
      <view class="sku-title">SKU列表:</view>
      <view wx:for="{{apiResult.skus}}" wx:key="id" class="sku-item">
        <view class="sku-name">{{item.nameExtension}}</view>
        <view class="sku-price">¥{{item.price}}</view>
        <view class="sku-stock">库存: {{item.stockQuantity}}</view>
        <view class="sku-attributes">
          <text wx:for="{{item.attributesList}}" wx:key="name" class="attribute-tag">
            {{item.name}}: {{item.value}}
          </text>
        </view>
      </view>
    </view>
  </view>

  <view class="test-instructions">
    <view class="instruction-title">测试说明:</view>
    <view class="instruction-list">
      <text>1. 点击"测试商品详情页面"跳转到商品详情页</text>
      <text>2. 在商品详情页点击"已选"区域打开SKU选择器</text>
      <text>3. 验证属性分组显示是否正确</text>
      <text>4. 测试选择不同属性组合</text>
      <text>5. 验证价格和库存更新是否正确</text>
    </view>
  </view>
</view>
