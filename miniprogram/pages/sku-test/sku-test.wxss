/* pages/sku-test/sku-test.wxss */
.container {
  padding: 32rpx;
  background-color: #f9fafb;
  min-height: 100vh;
}

.test-header {
  background-color: #ffffff;
  padding: 32rpx;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  text-align: center;
}

.test-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.test-desc {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.test-btn {
  background-color: #3b82f6;
  color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.test-btn::after {
  border: none;
}

.api-result {
  background-color: #ffffff;
  padding: 32rpx;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24rpx;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 32rpx;
}

.result-content text {
  font-size: 28rpx;
  color: #374151;
}

.sku-list {
  border-top: 1rpx solid #e5e7eb;
  padding-top: 24rpx;
}

.sku-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.sku-item {
  background-color: #f8fafc;
  padding: 24rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.sku-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.sku-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #ef4444;
  margin-bottom: 8rpx;
}

.sku-stock {
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 12rpx;
}

.sku-attributes {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.attribute-tag {
  background-color: #eff6ff;
  color: #3b82f6;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 1rpx solid #bfdbfe;
}

.test-instructions {
  background-color: #ffffff;
  padding: 32rpx;
  border-radius: 16rpx;
}

.instruction-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24rpx;
}

.instruction-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.instruction-list text {
  font-size: 28rpx;
  color: #374151;
  line-height: 1.5;
}
