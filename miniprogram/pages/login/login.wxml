<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 遮罩层 -->
  <view class="modal-mask" wx:if="{{showModal}}" bindtap="closeModal"></view>
  
  <!-- 登录弹窗 -->
  <view class="login-modal" wx:if="{{showModal}}">
    <!-- 关闭按钮 -->
    <view class="close-btn" bindtap="closeModal">
      <text class="close-icon">×</text>
    </view>
    
    <!-- 登录内容 -->
    <view class="login-content">
      <!-- Logo -->
      <view class="logo-section">
        <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
        <text class="app-name">眼镜商城</text>
      </view>
      
      <!-- 提示文字 -->
      <view class="tip-section">
        <text class="tip-title">登录态已过期</text>
        <text class="tip-desc">请重新登录以继续使用</text>
      </view>
      
      <!-- 登录按钮 -->
      <view class="login-section">
        <button 
          class="login-btn {{isLoading ? 'loading' : ''}}" 
          bindtap="wechatLogin"
          disabled="{{isLoading}}"
        >
          <image wx:if="{{!isLoading}}" class="wechat-icon" src="/images/wechat-icon.png"></image>
          <text class="login-text">
            {{isLoading ? '登录中...' : '微信快速登录'}}
          </text>
        </button>
        
        <button class="cancel-btn" bindtap="cancelLogin">
          <text>稍后再说</text>
        </button>
      </view>
      
      <!-- 用户协议 -->
      <view class="agreement-section">
        <text class="agreement-text">
          登录即表示同意
          <text class="link">《用户协议》</text>
          和
          <text class="link">《隐私政策》</text>
        </text>
      </view>
    </view>
  </view>
</view>
