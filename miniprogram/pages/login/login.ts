// pages/login/login.ts
import AuthManager from '../../utils/auth'

interface LoginPageData {
  isLoading: boolean
  showModal: boolean
}

Page<LoginPageData>({
  data: {
    isLoading: false,
    showModal: true
  },

  onLoad(options: any) {
    // 检查是否是从其他页面跳转过来的
    const fromPage = options.from || ''
    this.setData({
      fromPage
    })
  },

  onShow() {
    // 如果已经登录，直接返回上一页
    if (AuthManager.isLoggedIn()) {
      this.navigateBack()
    }
  },

  /**
   * 微信登录
   */
  async wechatLogin() {
    if (this.data.isLoading) {
      return
    }

    this.setData({ isLoading: true })

    try {
      const result = await AuthManager.wechatLogin()

      if (result.success) {
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        })

        // 延迟一下再跳转，让用户看到成功提示
        setTimeout(() => {
          this.navigateBack()
        }, 1500)
      }
    } catch (error: any) {
      console.error('登录失败:', error)
      // 错误提示已经在 AuthManager 中处理了
    } finally {
      this.setData({ isLoading: false })
    }
  },

  /**
   * 取消登录
   */
  cancelLogin() {
    this.navigateBack()
  },

  /**
   * 返回上一页或首页
   */
  navigateBack() {
    const pages = getCurrentPages()
    if (pages.length > 1) {
      wx.navigateBack()
    } else {
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
    }
  },

  /**
   * 关闭弹窗
   */
  closeModal() {
    this.setData({ showModal: false })
    setTimeout(() => {
      this.navigateBack()
    }, 300)
  }
})
