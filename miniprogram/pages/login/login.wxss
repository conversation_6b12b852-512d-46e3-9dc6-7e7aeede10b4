/* pages/login/login.wxss */
.login-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

/* 遮罩层 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9998;
}

/* 登录弹窗 */
.login-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  z-index: 9999;
  animation: modalShow 0.3s ease-out;
}

@keyframes modalShow {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 关闭按钮 */
.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.close-icon {
  font-size: 40rpx;
  color: #999999;
  font-weight: bold;
}

/* 登录内容 */
.login-content {
  padding: 80rpx 60rpx 60rpx;
}

/* Logo区域 */
.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.app-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

/* 提示区域 */
.tip-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.tip-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}

.tip-desc {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

/* 登录区域 */
.login-section {
  margin-bottom: 40rpx;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.3);
}

.login-btn.loading {
  background: #cccccc;
  box-shadow: none;
}

.wechat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.login-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 500;
}

.cancel-btn {
  width: 100%;
  height: 88rpx;
  background-color: #f5f5f5;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666666;
}

/* 用户协议 */
.agreement-section {
  text-align: center;
}

.agreement-text {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.5;
}

.link {
  color: #07c160;
}
