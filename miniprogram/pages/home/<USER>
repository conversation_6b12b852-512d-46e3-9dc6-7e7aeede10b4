// pages/home/<USER>
import CategoryProductService from '../../services/categoryProductService'
import type { HomePageConfigs, BannerConfig, StatementConfig, CategoryConfig } from '../../services/configService'
import { DEFAULT_HOME_PAGE_CONFIG, HOME_PAGE } from '../../utils/homePageConfig'
import RequestManager from '../../utils/request'

Page({
  data: {
    // 配置驱动相关
    loading: true,
    configs: null as HomePageConfigs | null,

    // 解析后的配置数据
    announcement: '',
    banners: [] as BannerConfig['banners'],
    statements: [] as StatementConfig['statements'],
    categories: [] as CategoryConfig[],

    // 降级方案标识
    useFallback: false,

    // 公告栏滚动控制
    announcementNeedScroll: false
  },

  async onLoad() {
    console.log('Home page loaded')

    // 1. 优先从本地获取首页配置
    await this.loadLocalConfigFirst()
  },

  /**
   * 优先从本地加载配置，如果没有则使用默认配置
   */
  async loadLocalConfigFirst() {
    try {
      // 1. 尝试从本地存储获取配置
      const cachedConfig = this.getCachedHomePageConfig()

      if (cachedConfig) {
        console.log('使用本地缓存的首页配置')
        this.applyHomePageConfig(cachedConfig)
      } else {
        console.log('本地无缓存，使用默认配置')
        // 2. 使用默认配置
        const defaultConfig = this.getDefaultHomePageConfig()
        this.applyHomePageConfig(defaultConfig)
      }

      // 3. 界面显示完成后异步刷新配置
      setTimeout(() => {
        this.loadHomePageConfigs()
      }, 3000)

    } catch (error) {
      console.error('加载本地配置失败:', error)
      // 降级使用默认配置
      const defaultConfig = this.getDefaultHomePageConfig()
      this.applyHomePageConfig(defaultConfig)
    }
  },

  /**
   * 获取缓存的首页配置
   */
  getCachedHomePageConfig(): any {
    try {
      return wx.getStorageSync(HOME_PAGE.HOME_PAGE_CONFIG)
    } catch (error) {
      console.error('读取缓存配置失败:', error)
      return null
    }
  },

  /**
   * 获取默认首页配置
   */
  getDefaultHomePageConfig(): any[] {
    return JSON.parse(JSON.stringify(DEFAULT_HOME_PAGE_CONFIG.data))
  },

  /**
   * 应用首页配置到界面
   */
  applyHomePageConfig(configData: any[]) {
    try {
      // 解析配置数据
      const parsedConfigs = this.parseConfigData(configData)

      // 设置页面数据
      this.setData({
        configs: parsedConfigs,
        announcement: parsedConfigs.announcement,
        banners: parsedConfigs.banner.banners,
        statements: parsedConfigs.statement.statements.slice(0, parsedConfigs.statement.config.max_items),
        categories: parsedConfigs.categories,
        loading: false,
        useFallback: false
      })

      console.log('首页配置应用完成')

      // 检测公告是否需要滚动
      if (parsedConfigs.announcement) {
        setTimeout(() => {
          this.checkAnnouncementScroll()
        }, 100)
      }

      // 加载分类商品
      this.loadCategoryProducts()

    } catch (error) {
      console.error('应用首页配置失败:', error)
      this.setData({
        loading: false,
        useFallback: true
      })
    }
  },

  /**
   * 解析配置数据（复用 ConfigService 的逻辑）
   */
  parseConfigData(configs: any[]): HomePageConfigs {
    const result: HomePageConfigs = {
      announcement: '',
      banner: { banners: [] },
      statement: { statements: [], config: { max_items: 8 } },
      categories: []
    }

    configs.forEach(config => {
      try {
        switch (config.configKey) {
          case 'announcement.information.config':
            result.announcement = config.configValue
            break
          case 'homepage.banner.config':
            result.banner = JSON.parse(config.configValue)
            break
          case 'import.statement.config':
            result.statement = JSON.parse(config.configValue)
            break
          case 'homepage.category1.config':
          case 'homepage.category2.config':
            const categoryData = JSON.parse(config.configValue)
            if (categoryData.categories && Array.isArray(categoryData.categories)) {
              categoryData.categories.forEach((cat: any) => {
                result.categories.push({
                  categoryId: cat.id,
                  categoryName: cat.name,
                  description: cat.description,
                  imageUrl: cat.imageUrl,
                  maxProducts: 8
                })
              })
            }
            break
        }
      } catch (error) {
        console.error(`解析配置失败: ${config.configKey}`, error)
      }
    })

    // 对轮播图和分类进行排序
    result.banner.banners.sort((a, b) => a.sortOrder - b.sortOrder)
    result.statement.statements.sort((a, b) => a.sortOrder - b.sortOrder)

    return result
  },

  onReady() {
    console.log('Home page ready')
    // 页面渲染完成后再次检测公告滚动
    if (this.data.announcement) {
      setTimeout(() => {
        this.checkAnnouncementScroll()
      }, 200)
    }
  },


  async loadHomePageConfigs() {
    try {
      console.log('异步刷新首页配置...')

      // 获取最新配置（这里需要直接调用API获取原始配置数据）
      const rawConfigData = await this.fetchRawHomePageConfigs()

      if (rawConfigData && rawConfigData.length > 0) {
        console.log('获取到最新首页配置，保存到本地存储')

        // 仅仅保存原始配置数据到本地存储，但是不做解析和应用
        this.saveHomePageConfigToLocal(rawConfigData)
      }

    } catch (error) {
      console.error('异步刷新首页配置失败:', error)
      // 异步刷新失败不影响页面显示，只记录错误
    }
  },

  /**
   * 直接获取原始首页配置数据
   */
  async fetchRawHomePageConfigs(): Promise<any[]> {
    try {
      const response = await RequestManager.get('/api/public/configs/homepage')

      if (response.success && response.data) {
        return response.data
      } else {
        throw new Error(response.message || '获取配置失败')
      }
    } catch (error) {
      console.error('获取原始配置数据失败:', error)
      throw error
    }
  },

  /**
   * 保存首页配置到本地存储
   */
  saveHomePageConfigToLocal(configData: any[]) {
    try {
      wx.setStorageSync(HOME_PAGE.HOME_PAGE_CONFIG, configData)
      console.log('首页配置已保存到本地存储')
    } catch (error) {
      console.error('保存首页配置到本地存储失败:', error)
    }
  },

  /**
   * 加载分类商品
   */
  async loadCategoryProducts() {
    try {
      const { categories } = this.data
      if (categories.length === 0) {
        return
      }

      console.log('开始加载分类商品...')

      // 为每个分类加载商品
      const updatedCategories = await Promise.all(
        categories.map(async (category) => {
          try {
            const products = await CategoryProductService.getCategoryProductsPreview(
              category.categoryId,
              category.maxProducts
            )
            return { ...category, products }
          } catch (error) {
            console.error(`加载分类${category.categoryId}商品失败:`, error)
            return { ...category, products: [] }
          }
        })
      )

      console.log('分类商品加载完成:', updatedCategories)

      this.setData({
        categories: updatedCategories
      })

    } catch (error) {
      console.error('加载分类商品失败:', error)
    }
  },

  /**
   * 检测公告文本是否需要滚动
   */
  checkAnnouncementScroll() {
    const query = wx.createSelectorQuery().in(this)
    query.select('.announcement-scroll-container').boundingClientRect()
    query.select('.announcement-text').boundingClientRect()
    query.exec((res) => {
      console.log('公告滚动检测结果:', res)

      if (res && res[0] && res[1]) {
        const containerWidth = res[0].width
        const textWidth = res[1].width

        // 如果文本宽度大于容器宽度的90%，则需要滚动（留一些余量）
        const needScroll = textWidth > containerWidth * 0.9

        this.setData({
          announcementNeedScroll: needScroll
        })

        console.log('公告滚动检测:', {
          containerWidth,
          textWidth,
          needScroll,
          announcement: this.data.announcement
        })
      } else {
        console.warn('公告滚动检测失败，DOM元素未找到')
        // 如果检测失败，根据文本长度简单判断
        const textLength = this.data.announcement.length
        const needScroll = textLength > 15 // 超过15个字符就滚动

        this.setData({
          announcementNeedScroll: needScroll
        })

        console.log('使用文本长度判断滚动:', {
          textLength,
          needScroll
        })
      }
    })
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      await this.loadHomePageConfigs()
    } finally {
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 轮播图点击事件
   * 优先级：URL > PRODUCT > 无跳转
   */
  onBannerTap(e: any) {
    const { banner } = e.currentTarget.dataset

    if (!banner) {
      console.log('轮播图数据为空')
      return
    }

    console.log('轮播图点击:', banner)

    // 获取URL，支持多种数据结构
    let targetUrl = ''
    if (banner.linkType === 'URL' && banner.url) {
      targetUrl = banner.url.trim()
    } else if (banner.url && !banner.linkType) {
      // 兼容只有url字段的情况
      targetUrl = banner.url.trim()
    }

    // 处理URL跳转
    if (targetUrl) {
      const url = targetUrl

      // 判断是否为小程序内部页面
      if (url.startsWith('/pages/')) {
        console.log('跳转到小程序页面:', url)
        wx.navigateTo({
          url,
          fail: (err) => {
            console.error('页面跳转失败:', err)
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      } else if (url.startsWith('http://') || url.startsWith('https://')) {
        // 外部链接，使用webview页面打开
        console.log('跳转到外部链接:', url)
        const title = banner.title || '详情'
        wx.navigateTo({
          url: `/pages/webview/webview?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`,
          fail: (err) => {
            console.error('webview页面跳转失败:', err)
            wx.showToast({
              title: '页面打开失败',
              icon: 'none'
            })
          }
        })
      } else {
        console.warn('不支持的链接格式:', url)
        wx.showToast({
          title: '不支持的链接格式',
          icon: 'none'
        })
      }
      return
    }

    // 处理商品跳转
    if (banner.linkType === 'PRODUCT' && banner.productId) {
      console.log('跳转到商品详情页:', banner.productId)
      wx.navigateTo({
        url: `/pages/product-detail/product-detail?id=${banner.productId}`,
        fail: (err) => {
          console.error('商品页面跳转失败:', err)
          wx.showToast({
            title: '商品页面跳转失败',
            icon: 'none'
          })
        }
      })
      return
    }

    // 处理大图预览
    if (banner.linkType === 'IMAGE' && banner.largeImageUrl) {
      console.log('预览大图:', banner.largeImageUrl)
      wx.previewImage({
        current: banner.largeImageUrl,
        urls: [banner.largeImageUrl],
        fail: (err) => {
          console.error('图片预览失败:', err)
          wx.showToast({
            title: '图片预览失败',
            icon: 'none'
          })
        }
      })
      return
    }

    // 无跳转配置
    console.log('轮播图无跳转配置')
  },

  /**
   * 重要说明点击事件
   * 优先级：url > ossUrl
   * url: HTML页面，直接跳转
   * ossUrl: 图片，预览大图
   */
  onStatementTap(e: any) {
    const { url, ossUrl, title } = e.currentTarget.dataset

    console.log('重要说明点击:', { url, ossUrl, title })

    // 优先使用url（HTML页面）
    if (url && url.trim()) {
      console.log('跳转到HTML页面:', url)
      // 使用webview页面打开HTML链接
      wx.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title || '详情')}`
      })
      return
    }

    // 其次使用ossUrl（图片）
    if (ossUrl && ossUrl.trim()) {
      console.log('预览图片:', ossUrl)
      wx.previewImage({
        urls: [ossUrl],
        current: ossUrl
      })
      return
    }

    // 都没有则提示
    wx.showToast({
      title: '暂无详细信息',
      icon: 'none'
    })
  },

  /**
   * 商品点击事件
   */
  onProductTap(e: any) {
    const { productId } = e.currentTarget.dataset
    console.log('商品点击:', productId)
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${productId}`
    })
  },

  /**
   * 查看分类更多商品 - 跳转到分类页面
   */
  onViewMoreProducts(e: any) {
    const { categoryId, categoryName } = e.currentTarget.dataset
    console.log('查看更多商品，跳转到分类页面:', categoryId, categoryName)

    // 切换到分类页面（第二个导航栏）
    wx.switchTab({
      url: '/pages/category/category'
    })
  },

  onCategoryTap(e: any) {
    const category = e.currentTarget.dataset.category
    console.log('Category tapped:', category)
    
    // Navigate to category page
    wx.switchTab({
      url: '/pages/category/category'
    })
  },

  onBrandTap(e: any) {
    const brand = e.currentTarget.dataset.brand
    console.log('Brand tapped:', brand)
    
    // Navigate to brand products page
    wx.navigateTo({
      url: `/pages/products/products?brand=${brand.name}`
    })
  },

  onMoreTap(e: any) {
    const type = e.currentTarget.dataset.type
    console.log('More tapped:', type)
    
    // Navigate to category page with specific type
    wx.switchTab({
      url: '/pages/category/category'
    })
  },

  onShareAppMessage() {
    return {
      title: '首页',
      path: '/pages/home/<USER>'
    }
  }
})
