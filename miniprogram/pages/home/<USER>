/* pages/home/<USER>/
.container {
  padding: 0 0 32rpx;
  width: 100%;
  display: flex; 
  flex-direction: column; 
  align-items: stretch;
}

/* Config Content */
.config-content {
  width: 98%;
  min-height: 100vh;
}

/* Banner Section */
.banner-section {
  /* padding: 0 24rpx; */
  margin: 24rpx auto 0; /* 上边距24rpx，左右自动居中，下边距0 */
  width: 100%; /* 调整为100%以适应新的margin设置 */
  /* background-color: red; */ /* 移除调试用的红色背景 */
  border-radius: 12rpx; /* 添加圆角 */
  overflow: visible; /* 改为visible以显示左右间距 */
}

.banner-swiper {
  height: 400rpx;
  width: 100%;
}

.banner-item {
  position: relative;
  height: 100%;
  width: 100%;
  margin: 0 10rpx; /* 为每个轮播项添加左右间距 */
  border-radius: 12rpx; /* 为轮播项添加圆角 */
  overflow: hidden; /* 确保图片不会超出圆角边界 */
  box-sizing: border-box;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.6));
  padding: 40rpx 24rpx 24rpx;
  color: white;
}

.banner-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
}

.simple-banner {
  width: 100%;
  height: 320rpx;
  background-color: #FFD700;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.banner-content {
  text-align: center;
  z-index: 2;
}

.banner-main-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.banner-main-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* Category Icons */
.category-icons {
  padding: 32rpx 24rpx;
  width: 100%;
  box-sizing: border-box;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16rpx;
  width: 100%;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.category-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
}

.category-icon image {
  width: 48rpx;
  height: 48rpx;
}

.category-name {
  font-size: 24rpx;
  text-align: center;
  color: #374151;
}

/* Section Block */
.section-block {
  margin-top: 32rpx;
  padding: 0 24rpx;
  width: 100%;
  box-sizing: border-box;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
}

.more-link {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #6b7280;
}

.arrow {
  margin-left: 8rpx;
  font-size: 28rpx;
}

/* Brand Grid */
.brand-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.brand-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #f3f4f6;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  min-width: 0; /* 防止内容溢出 */
}

.brand-icon {
  width: 96rpx;
  height: 96rpx;
  margin: 0 auto 16rpx;
  background-color: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-icon image {
  width: 64rpx;
  height: 64rpx;
}

.brand-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
}

/* Dynamic Components Styles */

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  padding: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #6b7280;
}

/* Component Wrapper */
.component-wrapper {
  margin-bottom: 24rpx;
}

/* Announcement Bar */
.announcement-bar {
  padding: 16rpx 24rpx;
  background-color: #fef3e2; /* 淡淡的橙色背景 */
  position: relative;
  overflow: hidden;
}

.announcement-content {
  width: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.announcement-scroll-container {
  flex: 1;
  overflow: hidden;
  position: relative;
  height: 40rpx; /* 固定高度 */
  display: flex;
  align-items: center;
  width: 100%; /* 确保容器宽度 */
}

.announcement-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #d97706;
  white-space: nowrap;
  display: inline-block;
  line-height: 40rpx;
}

/* 不滚动时居中显示 */
.announcement-text:not(.announcement-scroll) {
  width: 100%;
  text-align: center;
}

/* 滚动动画 */
.announcement-scroll {
  animation: scroll-left-smooth 15s ease-in-out infinite;
  animation-delay: 2s; /* 延迟2秒开始滚动，让用户先看到开头 */
  will-change: transform; /* 优化动画性能 */
}

/* 滚动动画关键帧 - 从正常位置开始平滑向左滑动 */
@keyframes scroll-left-smooth {
  0% {
    transform: translateX(0); /* 从正常位置开始显示 */
  }
  25% {
    transform: translateX(0); /* 保持正常位置，让用户看到开头内容 */
  }
  75% {
    transform: translateX(-60%); /* 向左滑动60%，显示更多内容 */
  }
  100% {
    transform: translateX(0); /* 回到开始位置，形成循环 */
  }
}

/* 触摸时暂停动画 */
.announcement-bar:active .announcement-scroll {
  animation-play-state: paused;
}

/* Carousel Component */
.carousel-container {
  height: 400rpx;
  margin-bottom: 24rpx;
}

.carousel-swiper {
  height: 100%;
}

.carousel-item {
  position: relative;
  height: 100%;
}

.carousel-image {
  width: 100%;
  height: 100%;
}

.carousel-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.6));
  padding: 40rpx 24rpx 24rpx;
  color: white;
}

.carousel-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.carousel-desc {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

/* Statement Section */
.statement-section {
  padding: 24rpx;
  background-color: #ffffff;
  margin: 24rpx 24rpx 24rpx;
  border-radius: 16rpx;
}

/* Statement Component */
.statement-container {
  padding: 24rpx;
  background-color: #ffffff;
  margin: 0 24rpx 24rpx;
  border-radius: 16rpx;
}

.statement-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.statement-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: #f8fafc;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.statement-item:active {
  background-color: #e2e8f0;
  transform: scale(0.95);
}

.statement-content {
  font-size: 32rpx;
  font-weight: 600;
  color: #374151;
  text-align: center;
}

/* Categories Section */
.categories-section {
  padding: 0 24rpx 24rpx;
}

.category-section {
  background-color: #ffffff;
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #f3f4f6;
}

.category-title-area {
  display: flex;
  align-items: center;
  flex: 1;
}

.category-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.view-more-btn {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #3b82f6;
}

.arrow {
  margin-left: 8rpx;
  font-size: 28rpx;
}

/* Category Products */
.category-products {
  padding: 0 24rpx 24rpx;
}

.products-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx; /* 负边距抵消子元素的padding */
}

.product-item {
  width: 50%; /* 固定50%宽度确保每行2个 */
  padding: 0 8rpx; /* 左右padding创建间距 */
  margin-bottom: 16rpx; /* 下边距 */
  box-sizing: border-box; /* 确保padding不影响宽度计算 */
}

.product-item-inner {
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  width: 100%;
}

.product-item:active .product-item-inner {
  background-color: #f8fafc;
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.product-image {
  width: 100%;
  height: calc((50vw - 48rpx - 16rpx) * 1); /* 正方形高度：(50%屏幕宽度 - 分类左右padding - 商品间距) */
  background-color: #f3f4f6;
  object-fit: cover;
}

.product-info {
  padding: 16rpx;
}

.product-name {
  display: block;
  font-size: 26rpx;
  color: #1f2937;
  margin-bottom: 8rpx;
  line-height: 1.4;
  height: 72rpx; /* 固定高度，支持两行文字 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price-area {
  display: flex;
  align-items: baseline;
  margin-top: 8rpx;
}

.product-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #ef4444;
}

.product-price-range {
  font-size: 24rpx;
  color: #ef4444;
  margin-left: 4rpx;
}

.products-loading,
.products-empty {
  text-align: center;
  padding: 60rpx 40rpx;
  color: #9ca3af;
  font-size: 28rpx;
  width: 100%;
}

.category-placeholder {
  text-align: center;
  padding: 40rpx;
  color: #9ca3af;
  font-size: 28rpx;
}

.more-products-contact {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #6b7280;
}

.contact-button-style {
  background-color: transparent;
  border: none;
  padding: 0;
  margin: 0;
  line-height: inherit;
  color: inherit;
  font-size: inherit;
  text-align: center;
  text-decoration: none;
}

.contact-button-style::after {
  border: none;
}

.red-text {
  color: red;
  font-size: 32rpx;
}
