<!--pages/home/<USER>
<view class="container">
  <search-bar style-class="home-style" />

  <!-- Loading State -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- Config-Driven Content -->
  <view wx:elif="{{!useFallback}}" class="config-content">

    <!-- Announcement Bar -->
    <view wx:if="{{announcement}}" class="announcement-bar">
      <view class="announcement-content">
        <view class="announcement-scroll-container">
          <text class="announcement-text {{announcementNeedScroll ? 'announcement-scroll' : ''}}">{{announcement}}</text>
        </view>
      </view>
    </view>

    <!-- Banner Carousel -->
    <view wx:if="{{banners.length > 0}}" class="banner-section">
      <swiper class="banner-swiper"
              autoplay="true"
              interval="3000"
              indicator-dots="true"
              circular="true"
              previous-margin="40rpx"
              next-margin="40rpx"
              display-multiple-items="1">
        <swiper-item wx:for="{{banners}}" wx:key="sortOrder">
          <view class="banner-item"
                data-banner="{{item}}"
                bindtap="onBannerTap">
            <image class="banner-image"
                   src="{{item.imageUrl}}"
                   mode="aspectFill"
                   lazy-load="true"></image>
            <view wx:if="{{item.title}}" class="banner-overlay">
              <text class="banner-title">{{item.title}}</text>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- Important Statements -->
    <view wx:if="{{statements.length > 0}}" class="statement-section">
      <view class="statement-grid">
        <view wx:for="{{statements}}" wx:key="sortOrder"
              class="statement-item"
              data-url="{{item.url}}"
              data-oss-url="{{item.ossUrl}}"
              data-title="{{item.title}}"
              bindtap="onStatementTap">
          <text class="statement-content">{{item.title}}</text>
        </view>
      </view>
    </view>

    <!-- Categories with Products -->
    <view wx:if="{{categories.length > 0}}" class="categories-section">
      <view wx:for="{{categories}}" wx:key="categoryId" class="category-section">

        <!-- Category Header -->
        <view class="category-header">
          <view class="category-title-area">
            <text class="category-name">{{item.categoryName}}</text>
          </view>
          <view class="view-more-btn"
                data-category-id="{{item.categoryId}}"
                data-category-name="{{item.categoryName}}"
                bindtap="onViewMoreProducts">
            <text>查看更多</text>
            <text class="arrow">></text>
          </view>
        </view>

        <!-- Category Products -->
        <view class="category-products">
          <view wx:if="{{item.products && item.products.length > 0}}"
                class="products-grid">
            <view wx:for="{{item.products}}"
                  wx:key="id"
                  wx:for-item="product"
                  class="product-item"
                  data-product-id="{{product.id}}"
                  bindtap="onProductTap">
              <view class="product-item-inner">
                <image class="product-image"
                       src="{{product.mainImageUrl}}"
                       mode="aspectFit"
                       lazy-load="true"></image>
                <view class="product-info">
                  <text class="product-name">{{product.name}}</text>
                  <view class="product-price-area">
                    <text wx:if="{{product.minPrice}}" class="product-price">¥{{product.minPrice}}</text>
                    <text wx:elif="{{product.priceRange}}" class="product-price">{{product.priceRange}}</text>
                    <text wx:if="{{product.maxPrice && product.maxPrice !== product.minPrice}}"
                          class="product-price-range">-{{product.maxPrice}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- Loading State -->
          <view wx:elif="{{!item.products}}" class="products-loading">
            <text>加载中...</text>
          </view>

          <!-- Empty State -->
          <view wx:else class="products-empty">
            <text>暂无商品</text>
          </view>
        </view>

      </view>
    </view>

  </view>

  <!-- Static Content (Fallback) -->
  <view wx:else class="static-content">
    <view class="fallback-message">
      <text class="fallback-title">加载失败</text>
      <text class="fallback-subtitle">请检查网络连接后重试</text>
      <button class="retry-btn" bindtap="loadHomePageConfigs">重新加载</button>
    </view>
  </view> <!-- End of static-content -->

  <!-- More products contact -->
  <view class="more-products-contact">
    <button
      class="contact-button-style"
      open-type="contact"
      session-from="miniprogram"
      send-message-title="眼镜商城咨询"
      send-message-path="/pages/home/<USER>"
      send-message-img="/images/service-avatar.png"
      show-message-card="true"
    >
      <text>
        更多镜片 / 镜框 <text class="red-text">点这里！</text>
      </text>
    </button>
  </view>

</view> <!-- End of container -->
