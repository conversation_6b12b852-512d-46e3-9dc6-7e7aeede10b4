// pages/category/category.ts
import CategoryService from '../../services/categoryService'
import { CategoryDTO, CategoryItem, SubCategoryItem } from '../../types/category'
import { CONFIG } from '../../utils/config'
import { DEFAULT_CATEGORY_PAGE_CONFIG } from '../../utils/categoryPageConfig'
import SystemInfoManager from '../../utils/systemInfo'

Page({
  data: {
    loading: true,
    activeCategory: '',
    activeCategoryId: 0,
    categories: [] as CategoryItem[],
    subCategories: {} as { [key: string]: SubCategoryItem[] },
    scrollIntoView: '', // 用于控制滚动到指定位置
    systemInfo: null as any, // 系统信息
    safeAreaBottom: 0, // 底部安全区域高度
    scrollTimer: null as any, // 滚动防抖定时器
    scrollViewHeight: 0 // 动态计算的滚动区域高度
  },

  onLoad() {
    console.log('Category page loaded')
    this.getSystemInfo()
    this.loadCategoryData()
  },

  /**
   * 获取系统信息，计算安全区域和滚动区域高度
   */
  getSystemInfo() {
    try {
      const systemInfo = SystemInfoManager.getSystemInfo();
      const rpxRatio = 750 / systemInfo.windowWidth;

      const query = wx.createSelectorQuery().in(this);
      query.select('.search-section').boundingClientRect();
      query.exec((res) => {
        if (res && res[0]) {
          const searchBarHeight_px = res[0].height;
          const windowHeight_px = systemInfo.windowHeight;

          // Calculate scroll view height in px
          const scrollViewHeight_px = windowHeight_px - searchBarHeight_px;

          // Convert to rpx for the template
          const scrollViewHeight_rpx = scrollViewHeight_px * rpxRatio;

          // Calculate safe area in rpx for the spacer
          const safeAreaBottom_px = systemInfo.safeArea ? (systemInfo.screenHeight - systemInfo.safeArea.bottom) : 0;
          const safeAreaBottom_rpx = safeAreaBottom_px * rpxRatio;

          this.setData({
            systemInfo: systemInfo,
            safeAreaBottom: safeAreaBottom_rpx, // for the spacer
            scrollViewHeight: scrollViewHeight_rpx // for the scroll view
          });
        } else {
          // Fallback
          const windowHeight_rpx = systemInfo.windowHeight * rpxRatio;
          this.setData({ scrollViewHeight: windowHeight_rpx - 100 });
        }
      });
    } catch (error) {
      console.error('获取系统信息失败:', error);
      this.setData({ scrollViewHeight: 1000 }); // fallback
    }
  },

  /**
   * 优先从本地缓存中取分类树json数据(通过wx.getStorageSync(CONFIG.STORAGE_KEYS.MAIN_CATEGORY_TREE)),
   * 本地没有则取代码中默认的配置（getDefaultCategoryTree）
   * 异步请求服务器分类树结构，并存到本地缓存中（wx.setStorageSync(CONFIG.STORAGE_KEYS.MAIN_CATEGORY_TREE)）
   */
  async loadCategoryData() {
    this.setData({ loading: true })

    // 1. 优先从本地缓存中取分类树数据
    let categoryTree: CategoryDTO[] = []
    try {
      const cachedData = wx.getStorageSync(CONFIG.STORAGE_KEYS.MAIN_CATEGORY_TREE)
      if (cachedData && Array.isArray(cachedData) && cachedData.length > 0) {
        categoryTree = cachedData

        // 使用缓存数据立即更新UI
        const { categories, subCategories, firstCategory } = this.transformCategoryData(categoryTree)
        this.setData({
          categories,
          subCategories,
          activeCategory: firstCategory.name,
          activeCategoryId: firstCategory.id,
          loading: false
        })
      } else {
        // 2. 本地没有则取代码中默认的配置
        console.log('本地缓存为空，使用默认分类数据')
        const defaultData = this.getDefaultCategoryTree()
        categoryTree = defaultData.data as CategoryDTO[]

        // 使用默认数据更新UI
        const { categories, subCategories, firstCategory } = this.transformCategoryData(categoryTree)
        this.setData({
          categories,
          subCategories,
          activeCategory: firstCategory.name,
          activeCategoryId: firstCategory.id,
          loading: false
        })
      }
    } catch (error) {
      console.error('读取本地缓存失败:', error)
      // 缓存读取失败，使用默认数据
      const defaultData = this.getDefaultCategoryTree()
      categoryTree = defaultData.data as CategoryDTO[]

      const { categories, subCategories, firstCategory } = this.transformCategoryData(categoryTree)
      this.setData({
        categories,
        subCategories,
        activeCategory: firstCategory.name,
        activeCategoryId: firstCategory.id,
        loading: false
      })
    }

    // 3. 异步请求服务器分类树结构，并存到本地缓存中
    try {
      const serverCategoryTree = await CategoryService.getActiveCategoryTree()

      // 将服务器数据存储到本地缓存
      wx.setStorageSync(CONFIG.STORAGE_KEYS.MAIN_CATEGORY_TREE, serverCategoryTree)

      // 如果服务器数据与当前显示的数据不同，则更新UI
      if (JSON.stringify(serverCategoryTree) !== JSON.stringify(categoryTree)) {
        const { categories, subCategories, firstCategory } = this.transformCategoryData(serverCategoryTree)
        this.setData({
          categories,
          subCategories,
          activeCategory: firstCategory.name,
          activeCategoryId: firstCategory.id
        })
      }
    } catch (error) {
      console.error('异步加载服务器分类数据失败:', error)
    }
  },

  /**
   * 获取默认的分类树json结构体
   */
  getDefaultCategoryTree() {
    return DEFAULT_CATEGORY_PAGE_CONFIG;
  },

  /**
   * 转换API数据为页面所需格式
   */
  transformCategoryData(categoryTree: CategoryDTO[]) {
    const categories: CategoryItem[] = []
    const subCategories: { [key: string]: SubCategoryItem[] } = {}
    let firstCategory = { name: '', id: 0 }

    // 只处理一级分类（level = 1 或 parentId 为空）
    const topLevelCategories = categoryTree.filter(cat => cat.level === 1 || !cat.parentId)

    topLevelCategories.forEach((category, index) => {
      const categoryItem: CategoryItem = {
        id: category.id,
        name: category.name,
        icon: category.imageUrl || this.getDefaultIcon(category.name)
      }

      categories.push(categoryItem)

      // 设置第一个分类为默认激活分类
      if (index === 0) {
        firstCategory = { name: category.name, id: category.id }
      }

      // 处理二级分类
      if (category.children && category.children.length > 0) {
        const subCategoryItems: SubCategoryItem[] = category.children.map(subCat => ({
          id: subCat.id,
          name: subCat.name,
          icon: subCat.imageUrl || this.getDefaultIcon(subCat.name),
          parentId: category.id
        }))

        subCategories[category.name] = subCategoryItems
      } else {
        // 如果没有子分类，创建空数组
        subCategories[category.name] = []
      }
    })

    return { categories, subCategories, firstCategory }
  },

  /**
   * 获取默认图标
   */
  getDefaultIcon(categoryName: string): string {
    // 根据分类名称返回对应的默认图标
    const iconMap: { [key: string]: string } = {
      '品牌镜片': '/images/lens.png',
      '品牌镜框': '/images/frame.png',
      '防控镜片': '/images/control.png',
      '大牌原单': '/images/frame.png',
      '儿童眼镜': '/images/kids.png',
      '蔡司': '/images/brands/zeiss.png',
      '罗敦司得': '/images/brands/rodenstock.png',
      '尼康': '/images/brands/nikon.png',
      '依视路': '/images/brands/essilor.png',
      '豪雅': '/images/brands/hoya.png',
      '凯米': '/images/brands/chemilens.png',
      '明月': '/images/brands/mingyue.png',
      '康耐特': '/images/brands/conant.png',
      '全真': '/images/brands/quanzhen.png',
      '夏蒙': '/images/brands/charmant.png',
      '暴龙': '/images/brands/bolon.png',
      '陌森': '/images/brands/molsion.png',
      '施洛华': '/images/brands/silhouette.png',
      '海伦凯勒': '/images/brands/helen.png',
      '欧克利': '/images/brands/oakley.png',
      '控优点': '/images/brands/controlyou.png',
      '新乐学': '/images/brands/stellest.png',
      '新趣控': '/images/brands/xinqukon.png',
      '柯达优': '/images/brands/kodak.png',
      '新优学': '/images/brands/xinyouxue.png',
      'Ray-Ban复刻': '/images/brands/rayban.png',
      'Gucci复刻': '/images/brands/gucci.png',
      'Prada复刻': '/images/brands/prada.png',
      'Dior复刻': '/images/brands/dior.png',
      'Chanel复刻': '/images/brands/chanel.png',
      'Tom Ford复刻': '/images/brands/tomford.png'
    }

    return iconMap[categoryName] || '/images/lens.png'
  },

  onCategorySelect(e: any) {
    const category = e.currentTarget.dataset.category
    const categoryId = e.currentTarget.dataset.categoryId

    console.log('选择分类:', category, 'ID:', categoryId)

    // 更新选中状态
    this.setData({
      activeCategory: category,
      activeCategoryId: categoryId
    })

    // 滚动到对应的分类区域
    this.scrollToCategory(categoryId)
  },

  /**
   * 滚动到指定分类
   */
  scrollToCategory(categoryId: number) {
    const scrollIntoView = `category-${categoryId}`
    console.log('滚动到分类区域:', scrollIntoView)

    this.setData({
      scrollIntoView: scrollIntoView
    })

    // 清除滚动目标，避免影响后续滚动
    setTimeout(() => {
      this.setData({
        scrollIntoView: ''
      })
    }, 500)
  },

  /**
   * 右侧内容滚动时的监听事件
   * 根据滚动位置自动更新左侧选中状态
   */
  onContentScroll() {
    // 防抖处理，避免频繁触发
    if (this.data.scrollTimer) {
      clearTimeout(this.data.scrollTimer)
    }

    this.setData({
      scrollTimer: setTimeout(() => {
        this.updateActiveCategory()
      }, 500)
    })
  },

  /**
   * 根据滚动位置更新左侧选中分类
   * 思路：
   * - 使用 createSelectorQuery().in(this) 同时测量右侧 scroll-view 和每个分类区域（.category-section）的矩形信息
   * - 所有 top 值均是相对小程序可视窗口的，因此用 section.top - content.top 可以得到该 section 相对滚动容器可视区顶部的偏移
   * - 找到"顶部对齐（或刚刚越过顶部）"的那个分类作为当前激活分类
   */
  updateActiveCategory() {
    const { categories, activeCategoryId } = this.data as any
    if (!categories || categories.length === 0) return

    // 为了稳妥，给一个小的容差，避免由于像素取整导致的抖动
    const THRESHOLD_PX = 20

    try {
      const query = wx.createSelectorQuery().in(this)
      // 先量右侧滚动容器本身
      query.select('.category-content').boundingClientRect()
      // 再量所有分类分区，拿到它们的 id 和矩形信息
      query.selectAll('.category-section').fields({ id: true, rect: true })

      query.exec((res: any[]) => {
        if (!res || res.length < 2) return
        const contentRect = res[0]
        const sectionRects: Array<{ id: string; top: number; bottom: number }> = res[1] || []
        if (!contentRect || !sectionRects.length) return

        const contentTop = contentRect.top
        let newActiveId: number | null = null

        // 遍历 DOM 顺序（即展示顺序），挑选"顶部对齐或已越过顶部"的最后一个 section
        for (let i = 0; i < sectionRects.length; i++) {
          const sec = sectionRects[i]
          const offsetTop = sec.top - contentTop
          // 当 section 的顶部已经到达滚动容器可视区顶部附近（<= THRESHOLD_PX）时，认为它是候选项
          if (offsetTop <= THRESHOLD_PX) {
            const idStr = (sec.id || '').toString()
            const parsed = parseInt(idStr.replace('category-', ''), 10)
            if (!isNaN(parsed)) {
              newActiveId = parsed
            }
          } else {
            // 当前这个 section 的顶部还在可视区下方，则上一项就是当前激活项
            break
          }
        }

        // 如果没有任何 section 满足（例如滚动位置非常靠上），默认选中第一个
        if (newActiveId === null) {
          newActiveId = categories[0].id
        }

        if (newActiveId !== activeCategoryId) {
          const matched = categories.find((c: any) => c.id === newActiveId)
          if (matched) {
            this.setData({
              activeCategory: matched.name,
              activeCategoryId: matched.id
            })
          }
        }
      })
    } catch (err) {
      console.error('updateActiveCategory 测量失败:', err)
    }
  },

  /**
   * 设置默认选中的分类（不触发滚动）
   */
  setDefaultActiveCategory(categoryName: string, categoryId: number) {
    this.setData({
      activeCategory: categoryName,
      activeCategoryId: categoryId
    })
  },

  onSubCategoryTap(e: any) {
    const subcategory = e.currentTarget.dataset.subcategory
    console.log('Subcategory tapped:', subcategory)

    // Navigate to products page with category ID
    wx.navigateTo({
      url: `/pages/products/products?categoryId=${subcategory.id}&categoryName=${subcategory.name}`
    })
  },

  onShareAppMessage() {
    return {
      title: '眼镜分类 - 专业眼镜品牌',
      path: '/pages/category/category'
    }
  }
})
