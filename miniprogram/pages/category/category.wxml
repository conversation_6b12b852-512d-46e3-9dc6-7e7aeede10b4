<!--pages/category/category.wxml-->
<view class="container">
  <!-- Search Bar -->
  <search-bar />

  <!-- Loading State -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- Content -->
  <view wx:else class="content-wrapper">
    <!-- First Level Categories - 左侧分类列表 -->
    <scroll-view class="category-sidebar" scroll-y="true" enhanced="true" show-scrollbar="false" style="height: {{scrollViewHeight}}rpx;">
      <view
        class="category-item {{activeCategory === item.name ? 'active' : ''}}"
        wx:for="{{categories}}"
        wx:key="id"
        bindtap="onCategorySelect"
        data-category="{{item.name}}"
        data-category-id="{{item.id}}"
      >
        {{item.name}}
      </view>
      <!-- 左侧底部占位区域 -->
      <view class="sidebar-bottom-spacer" style="height: {{safeAreaBottom + 40}}rpx;"></view>
    </scroll-view>

    <!-- All Categories and Subcategories - 右侧内容区域 -->
    <scroll-view class="category-content" scroll-y="true" scroll-into-view="{{scrollIntoView}}" scroll-with-animation="true" enhanced="true" show-scrollbar="false" bindscroll="onContentScroll" style="height: {{scrollViewHeight}}rpx;">
      <!-- Loop through all categories -->
      <view wx:for="{{categories}}" wx:key="id" wx:for-item="category">
        <!-- Category Section -->
        <view class="category-section" id="category-{{category.id}}">
          <!-- Category Title -->
          <view class="category-title">
            <text class="category-title-text">{{category.name}}</text>
          </view>

          <!-- Subcategory Grid -->
          <view wx:if="{{subCategories[category.name] && subCategories[category.name].length > 0}}" class="subcategory-grid">
            <view
              class="subcategory-item"
              wx:for="{{subCategories[category.name]}}"
              wx:key="id"
              bindtap="onSubCategoryTap"
              data-subcategory="{{item}}"
            >
              <view class="subcategory-icon">
                <image src="{{item.icon}}" mode="aspectFit"></image>
              </view>
              <text class="subcategory-name">{{item.name}}</text>
            </view>
          </view>

          <!-- Empty State for this category -->
          <view wx:else class="category-empty-state">
            <text class="empty-text">暂无子分类</text>
          </view>
        </view>
      </view>

      <!-- More products contact -->
      <view class="more-products-contact">
        <button
          class="contact-button-style"
          open-type="contact"
          session-from="miniprogram"
          send-message-title="眼镜商城咨询"
          send-message-path="/pages/home/<USER>"
          send-message-img="/images/service-avatar.png"
          show-message-card="true"
        >
          <text>
            更多镜片 / 镜框 <text class="red-text">点这里 ！</text>
          </text>
        </button>
      </view>

      <!-- 右侧底部占位区域，防止内容被tabBar遮挡 -->
      <view class="content-bottom-spacer" style="height: {{safeAreaBottom + 40}}rpx;"></view>
    </scroll-view>
  </view>

</view>
