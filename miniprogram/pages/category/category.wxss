/* pages/category/category.wxss */
/* 重置全局container样式，避免冲突 */
.container {
  padding-top: 10rpx;
  height: 100%;
  display: flex;
  padding-bottom: 0rpx;
  margin-bottom: 0rpx;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  /* 重置全局样式中的对齐属性 */
  align-items: stretch;
  justify-content: flex-start;
  /* 移除固定的padding-bottom，改为动态设置 */
}

/* Content Wrapper */
.content-wrapper {
  display: flex;
  overflow: hidden;
  width: 100%;
}

/* Category Sidebar */
.category-sidebar {
  width: 25%;
  background-color: #f9fafb;
  box-sizing: border-box;
  /* 高度通过内联样式动态设置 */
}

.category-item {
  padding: 24rpx 16rpx;
  text-align: center;
  font-size: 28rpx;
  color: #6b7280;
  border-bottom: 1rpx solid #e5e7eb;
  position: relative;
  transition: all 0.3s ease;
}

.category-item.active {
  background-color: #ffffff;
  color: #000000;
  font-weight: 500;
  position: relative;
}

/* 选中状态的左侧指示条 */
.category-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background-color: #3b82f6;
}

/* Category Content */
.category-content {
  width: 75%;
  padding: 0;
  box-sizing: border-box;
  /* 高度通过内联样式动态设置 */
}

/* Category Section */
.category-section {
  margin-bottom: 32rpx;
}

/* 最后一个分类区域的额外底部间距 */
.category-section:last-child {
  margin-bottom: 10rpx;
}

/* Category Title */
.category-title {
  /* background-color: #ffffff; */
  padding: 24rpx;
  margin-bottom: 16rpx;
  border-bottom: 1rpx solid #e5e7eb;
  /* 暂时移除sticky定位，测试是否影响滚动 */
}

.category-title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.subcategory-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  padding: 0 24rpx;
  margin-bottom: 24rpx;
  max-width: 100%;
  box-sizing: border-box;
}

.subcategory-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 16rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #f3f4f6;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.subcategory-icon {
  width: 140rpx;
  height: 140rpx;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.subcategory-icon image {
  width: 100%;
  height: 100%;
}

.subcategory-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #1f2937;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
  text-align: center;
}

/* Category Empty State */
.category-empty-state {
  padding: 40rpx 24rpx;
  text-align: center;
}

.category-empty-state .empty-text {
  font-size: 28rpx;
  color: #9ca3af;
}

/* 底部占位区域 */
.bottom-spacer {
  width: 100%;
  background-color: transparent;
}

/* 左侧分类列表底部占位区域 */
.sidebar-bottom-spacer {
  width: 100%;
  background-color: transparent;
}

/* 右侧内容区域底部占位区域 */
.content-bottom-spacer {
  width: 100%;
  background-color: transparent;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  padding: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #6b7280;
}

.more-products-contact {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #6b7280;
}

.contact-button-style {
  background-color: transparent;
  border: none;
  padding: 0;
  margin: 0;
  line-height: inherit;
  color: inherit;
  font-size: inherit;
  text-align: center;
  text-decoration: none;
}

.contact-button-style::after {
  border: none;
}

.red-text {
  color: red;
  font-size: 32rpx;
}