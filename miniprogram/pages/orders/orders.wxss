/* pages/orders/orders.wxss */
.orders-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 状态标签栏 */
.status-tabs {
  background-color: #fff;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs-container {
  display: flex;
  padding: 0 32rpx;
}

.tab-item {
  position: relative;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: #666;
  font-size: 28rpx;
}

.tab-item.active {
  color: #007aff;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #007aff;
  border-radius: 2rpx;
}

.tab-name {
  margin-right: 8rpx;
}

.tab-count {
  background-color: #ff3b30;
  color: #fff;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  text-align: center;
  line-height: 1.2;
}

/* 退款/售后标签特殊样式 */
.tab-item[data-status="refund_afterSale"] .tab-name {
  color: #ff6b35;
  font-weight: 500;
}

.tab-item[data-status="refund_afterSale"].active .tab-name {
  color: #ff6b35;
}

/* 退款列表样式 */
.refunds-list {
  padding: 0;
}

.refund-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.refund-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.refund-info {
  flex: 1;
}

.refund-time {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 退款状态统一样式 - 全部使用灰色背景 */
.refund-status {
  font-size: 28rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background-color: #f0f0f0;
  color: #666;
}

/* 移除所有特定状态的颜色样式，统一使用灰色背景 */
.refund-status.pending_review,
.refund-status.approved,
.refund-status.user_shipping,
.refund-status.refunded,
.refund-status.rejected,
.refund-status.cancelled,
.refund-status.pending_refund {
  background-color: #f0f0f0;
  color: #666;
}

.refund-products {
  margin-bottom: 20rpx;
}

/* 退款金额区域 */
.refund-amount-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.refund-amount-section .amount-label {
  font-size: 28rpx;
  color: #333;
}

.refund-amount-section .amount-value {
  font-size: 32rpx;
  color: #ff3b30;
  font-weight: 600;
  flex: 1;
  text-align: right;
  margin-right: 16rpx;
}

.refund-type-tag {
  font-size: 24rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}

.refund-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 订单容器 */
.orders-container {
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #999;
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.5;
}

.empty-text {
  margin-top: 40rpx;
  color: #999;
  font-size: 32rpx;
}

.empty-action {
  margin-top: 40rpx;
}

.go-shopping-btn {
  background-color: #007aff;
  color: #fff;
  padding: 20rpx 60rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  text-decoration: none;
}

/* 订单列表 */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding-bottom: 24rpx;
  border-bottom: 1px solid #f0f0f0;
}

.order-info {
  flex: 1;
}

/* 订单号容器 */
.order-number-container {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.order-number {
  font-size: 28rpx;
  color: #333;
}

/* 复制图标 */
.copy-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  background-color: #f5f5f5;
  border-radius: 6rpx;
  transition: background-color 0.2s;
}

.copy-icon:active {
  background-color: #e0e0e0;
}

.copy-text {
  font-size: 20rpx;
  line-height: 1;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  font-size: 28rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background-color: #f0f0f0;
  color: #666;
}

.order-status.pending_payment {
  background-color: #fff3cd;
  color: #856404;
}

.order-status.paid_shipped {
  background-color: #cce5ff;
  color: #004085;
}

.order-status.shipped {
  background-color: #e2e3ff;
  color: #383d41;
}



/* 商品列表 */
.order-products {
  margin-bottom: 24rpx;
}

/* 商品样式已移至 product-item 组件 */

/* 订单金额 */
.order-amount {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 24rpx;
  padding-top: 24rpx;
  border-top: 1px solid #f0f0f0;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

.amount-value {
  font-size: 32rpx;
  color: #ff3b30;
  font-weight: 600;
}

/* 操作按钮 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  text-align: center;
  border: 1px solid #ddd;
  background-color: #fff;
  color: #333;
  min-width: 120rpx;
}

.action-btn.primary {
  background-color: #007aff;
  color: #fff;
  border-color: #007aff;
}

.action-btn.secondary {
  background-color: #f8f9fa;
  color: #6c757d;
  border-color: #dee2e6;
}

.action-btn:active {
  opacity: 0.7;
}

/* 加载更多 */
.load-more,
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.load-more-text,
.no-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .order-item {
    padding: 24rpx;
  }
  
  .product-image {
    width: 100rpx;
    height: 100rpx;
  }
  
  .product-info {
    height: 100rpx;
  }

  .product-right {
    height: 100rpx;
    justify-content: center;
  }
  
  .action-btn {
    padding: 12rpx 24rpx;
    font-size: 24rpx;
    min-width: 100rpx;
  }
}
