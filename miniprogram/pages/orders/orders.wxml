<!--pages/orders/orders.wxml-->
<wxs module="utils">
  function formatAmount(amount) {
    if (!amount || isNaN(amount)) {
      return '0.00';
    }
    return (amount / 100).toFixed(2);
  }

  function splitAttributes(attributes) {
    if (!attributes || typeof attributes !== 'string' || attributes.trim() === '') {
      return [];
    }

    var trimmed = attributes.trim();

    // 首先尝试中文逗号分割
    if (trimmed.indexOf('，') > -1) {
      return trimmed.split('，').filter(function(attr) {
        return attr && attr.trim();
      }).map(function(attr) {
        return attr.trim();
      });
    }

    // 然后尝试英文逗号分割
    if (trimmed.indexOf(',') > -1) {
      return trimmed.split(',').filter(function(attr) {
        return attr && attr.trim();
      }).map(function(attr) {
        return attr.trim();
      });
    }

    // 尝试分号分割
    if (trimmed.indexOf(';') > -1 || trimmed.indexOf('；') > -1) {
      var sep = trimmed.indexOf(';') > -1 ? ';' : '；';
      return trimmed.split(sep).filter(function(attr) {
        return attr && attr.trim();
      }).map(function(attr) {
        return attr.trim();
      });
    }

    // 尝试 × 符号分割（用于 skuNameExtension 格式）
    if (trimmed.indexOf('×') > -1) {
      return trimmed.split('×').filter(function(attr) {
        return attr && attr.trim();
      }).map(function(attr) {
        return attr.trim();
      });
    }

    // 尝试英文 x 符号分割
    if (trimmed.indexOf(' x ') > -1) {
      return trimmed.split(' x ').filter(function(attr) {
        return attr && attr.trim();
      }).map(function(attr) {
        return attr.trim();
      });
    }

    // 如果没有找到分隔符，返回单个元素数组
    return [trimmed];
  }

  module.exports = {
    formatAmount: formatAmount,
    splitAttributes: splitAttributes
  };
</wxs>

<view class="orders-page">
  <!-- 状态标签栏 -->
  <view class="status-tabs">
    <scroll-view scroll-x="true" class="tabs-scroll">
      <view class="tabs-container">
        <view 
          wx:for="{{statusTabs}}" 
          wx:key="key"
          class="tab-item {{currentStatus === item.key ? 'active' : ''}}"
          data-status="{{item.key}}"
          bindtap="onTabChange"
        >
          <text class="tab-name">{{item.name}}</text>
          <text wx:if="{{item.count > 0}}" class="tab-count">{{item.count}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 内容列表 -->
  <view class="orders-container">
    <!-- 退款/售后列表 -->
    <view wx:if="{{currentStatus === 'refund_afterSale'}}">
      <!-- 加载状态 -->
      <view wx:if="{{loading && refunds.length === 0}}" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view wx:elif="{{!loading && refunds.length === 0}}" class="empty-container">
        <image class="empty-icon" src="/images/empty-refunds.png" mode="aspectFit"></image>
        <text class="empty-text">暂无退款申请</text>
        <view class="empty-action">
          <navigator url="/pages/index/index" open-type="switchTab" class="go-shopping-btn">
            去逛逛
          </navigator>
        </view>
      </view>

      <!-- 退款列表 -->
      <view wx:else class="refunds-list">
        <view
          wx:for="{{refunds}}"
          wx:for-item="refund"
          wx:key="id"
          class="refund-item"
          data-refund-id="{{refund.id}}"
          bindtap="viewRefundDetail"
        >
          <!-- 退款头部 -->
          <view class="refund-header">
            <view class="refund-info">
              <view class="order-number-container">
                <text class="order-number">订单号：{{refund.orderNumber}}</text>
                <view class="copy-icon" data-order-number="{{refund.orderNumber}}" catchtap="copyOrderNumber">
                  <text class="copy-text">📋</text>
                </view>
              </view>
              <text class="refund-time">{{refund.createdAt}}</text>
            </view>
            <view class="refund-status {{refund.status}}">
              {{refund.statusDesc}}
            </view>
          </view>

          <!-- 退款商品信息 -->
          <view class="refund-products">
            <view wx:for="{{refund.productDetails}}" wx:for-item="product" wx:key="orderItemId" class="product-item">
              <image class="product-image" src="{{product.productMainImageUrl || product.productImageUrl || '/images/placeholder.png'}}" mode="aspectFill"></image>
              <view class="product-info">
                <text class="product-name">{{product.productName}}</text>
                <view class="product-spec-container">
                  <text wx:if="{{product.skuNameExtension}}" wx:for="{{utils.splitAttributes(product.skuNameExtension)}}" wx:key="*this" class="product-spec">{{item}}</text>
                </view>
              </view>
              <view class="product-right">
                <text class="product-total-price">¥{{utils.formatAmount(product.refundAmount)}}</text>
                <text class="product-qty">×{{product.refundQuantity}}</text>
              </view>
            </view>
          </view>

          <!-- 退款金额和类型 -->
          <view class="refund-amount-section">
            <text class="amount-label">退款金额：</text>
            <text class="amount-value">¥{{utils.formatAmount(refund.refundAmount)}}</text>
            <text class="refund-type-tag">{{refund.refundTypeDesc}}</text>
          </view>


        </view>
      </view>
    </view>

    <!-- 订单列表 -->
    <view wx:else>
      <!-- 加载状态 -->
      <view wx:if="{{loading && orders.length === 0}}" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view wx:elif="{{!loading && orders.length === 0}}" class="empty-container">
        <image class="empty-icon" src="/images/empty-orders.png" mode="aspectFit"></image>
        <text class="empty-text">暂无订单</text>
        <view class="empty-action">
          <navigator url="/pages/index/index" open-type="switchTab" class="go-shopping-btn">
            去逛逛
          </navigator>
        </view>
      </view>

      <!-- 订单列表 -->
      <view wx:else class="orders-list">
      <view
        wx:for="{{orders}}"
        wx:for-item="order"
        wx:key="id"
        class="order-item"
        data-order-id="{{order.id}}"
        bindtap="viewOrderDetail"
      >
        <!-- 订单头部 -->
        <view class="order-header">
          <view class="order-info">
            <view class="order-number-container">
              <text class="order-number">订单号：{{order.orderNumber}}</text>
              <view class="copy-icon" data-order-number="{{order.orderNumber}}" catchtap="copyOrderNumber">
                <text class="copy-text">📋</text>
              </view>
            </view>
            <text class="order-time">{{order.createTime}}</text>
          </view>
          <view class="order-status {{order.status}}">
            {{order.statusDescription || formatOrderStatus(order.status)}}
          </view>
        </view>

        <!-- 商品列表 -->
        <view class="order-products">
          <product-item
            wx:for="{{order.items}}"
            wx:for-item="product"
            wx:for-index="productIndex"
            wx:key="id"
            product="{{product}}"
            mode="order"
            show-total-price="{{true}}"
            order-total-amount="{{order.totalAmount}}"
          />
        </view>

        <!-- 订单金额 (非待付款订单才显示) -->
        <view wx:if="{{order.status !== 'pending_payment'}}" class="order-amount">
          <text class="amount-label">实付款：</text>
          <text class="amount-value">¥{{utils.formatAmount(order.totalAmount)}}</text>
        </view>

        <!-- 操作按钮 -->
        <view class="order-actions">
          <view
            wx:for="{{getOrderActions(order.status)}}"
            wx:for-item="action"
            wx:key="action"
            class="action-btn {{action.type || 'default'}}"
            data-action="{{action.action}}"
            data-order-id="{{order.id}}"
            data-order-number="{{order.orderNumber}}"
            bindtap="handleOrderAction"
            catchtap="handleOrderAction"
          >
            {{action.text}}
          </view>
        </view>
      </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{hasMore && (orders.length > 0 || refunds.length > 0)}}" class="load-more">
      <text class="load-more-text">加载更多...</text>
    </view>

    <!-- 没有更多 -->
    <view wx:elif="{{!hasMore && (orders.length > 0 || refunds.length > 0)}}" class="no-more">
      <text class="no-more-text">没有更多了</text>
    </view>
  </view>
</view>
