// pages/orders/orders.ts
import OrderService from '../../services/orderService'
import PaymentService from '../../services/paymentService'
import RefundService from '../../services/refundService'
import { OrderInfo } from '../../types/payment'
import { CreatePaymentRequest } from '../../types/payment'
import { RefundInfo } from '../../types/refund'
import OrderManagementConfig from '../../utils/orderManagement'

interface OrderPageData {
  orders: OrderInfo[]
  refunds: RefundInfo[]
  loading: boolean
  refreshing: boolean
  currentStatus: string
  statusTabs: Array<{
    key: string
    name: string
    count: number
  }>
  hasMore: boolean
  page: number
}

Page({
  data: {
    orders: [],
    refunds: [],
    loading: true,
    refreshing: false,
    currentStatus: '',
    statusTabs: [
      { key: '', name: '全部', count: 0 },
      { key: 'pending_payment', name: '待支付', count: 0 },
      { key: 'paid_shipped', name: '待发货', count: 0 },
      { key: 'shipped', name: '待收货', count: 0 },
      { key: 'refund_afterSale', name: '退款/售后', count: 0 }
    ],
    hasMore: true,
    page: 1
  } as OrderPageData,

  onLoad(options: any) {
    // 处理从微信订单管理跳转的情况
    const wechatJump = OrderManagementConfig.handleWechatOrderJump(options)

    if (wechatJump.isFromWechat && wechatJump.orderNo) {
      // 从微信订单管理跳转，直接查看特定订单
      this.handleWechatOrderView(wechatJump.orderNo)
      return
    }

    // 获取传入的状态参数
    const status = options.status || ''
    this.setData({ currentStatus: status })

    // 设置页面标题 - 统一显示为"我的订单"
    wx.setNavigationBarTitle({
      title: '我的订单'
    })

    // 根据状态类型加载不同的数据
    if (status === 'refund_afterSale') {
      // 对于退款页面，先加载退款数据，然后在回调中更新统计
      this.loadRefundsAndCounts()
    } else {
      this.loadOrders()
      this.loadOrderCounts()
    }
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshOrders()
  },

  /**
   * 获取状态名称
   */
  getStatusName(status: string): string {
    const statusMap: Record<string, string> = {
      'pending_payment': '待支付',
      'paid_shipped': '待发货',
      'shipped': '待收货',
      'refund_afterSale': '退款/售后'
    }
    return statusMap[status] || ''
  },

  /**
   * 加载订单列表
   */
  async loadOrders(isRefresh: boolean = false) {
    if (isRefresh) {
      this.setData({
        refreshing: true,
        page: 1,
        hasMore: true
      })
    } else {
      this.setData({ loading: true })
    }

    try {
      const { currentStatus, page } = this.data
      const params = {
        status: currentStatus || undefined,
        page: page,
        pageSize: 10,
        sortBy: 'createTime',
        sortOrder: 'desc' as 'desc'
      }

      const orders = await OrderService.getUserOrders(params)

      if (isRefresh) {
        this.setData({
          orders: orders,
          hasMore: orders.length >= 10
        })
      } else {
        const existingOrders = page === 1 ? [] : this.data.orders
        this.setData({
          orders: [...existingOrders, ...orders],
          hasMore: orders.length >= 10
        })
      }

    } catch (error) {
      console.error('加载订单列表失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      })
    }
  },

  /**
   * 加载退款列表
   */
  async loadRefunds(isRefresh: boolean = false) {
    if (isRefresh) {
      this.setData({
        refreshing: true,
        page: 1,
        hasMore: true
      })
    } else {
      this.setData({ loading: true })
    }

    try {
      const { page } = this.data
      const params = {
        pageNum: page,
        pageSize: 10
      }

      const result = await RefundService.getRefundList(params)

      if (isRefresh) {
        this.setData({
          refunds: result.records,
          hasMore: result.records.length >= 10
        })
      } else {
        const existingRefunds = page === 1 ? [] : this.data.refunds
        this.setData({
          refunds: [...existingRefunds, ...result.records],
          hasMore: result.records.length >= 10
        })
      }

      return result

    } catch (error) {
      console.error('加载退款列表失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      throw error
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      })
    }
  },

  /**
   * 加载退款数据和统计信息（避免重复请求）
   */
  async loadRefundsAndCounts() {
    try {
      // 并行加载退款数据和订单统计
      const [refundResult, orderCounts] = await Promise.all([
        this.loadRefunds(),
        OrderService.getOrderCounts()
      ])

      // 使用退款数据的总数更新统计标签
      const statusTabs = this.data.statusTabs.map(tab => {
        if (tab.key === '') {
          return { ...tab, count: orderCounts.total }
        } else if (tab.key === 'refund_afterSale') {
          return { ...tab, count: refundResult.total || 0 }
        } else {
          return { ...tab, count: orderCounts[tab.key] || 0 }
        }
      })

      this.setData({ statusTabs })
    } catch (error) {
      console.error('加载退款数据和统计失败:', error)
      // 如果退款数据加载失败，仍然尝试加载统计数据
      this.loadOrderCounts()
    }
  },

  /**
   * 加载订单数量统计
   */
  async loadOrderCounts() {
    try {
      // 获取订单统计
      const counts = await OrderService.getOrderCounts()

      // 获取退款统计
      let refundTotal = 0
      try {
        const refundResult = await RefundService.getRefundList({ pageNum: 1, pageSize: 1 })
        refundTotal = refundResult.total || 0
      } catch (error) {
        console.error('获取退款统计失败:', error)
        refundTotal = 0
      }

      const statusTabs = this.data.statusTabs.map(tab => {
        if (tab.key === '') {
          return { ...tab, count: counts.total }
        } else if (tab.key === 'refund_afterSale') {
          return { ...tab, count: refundTotal }
        } else {
          return { ...tab, count: counts[tab.key] || 0 }
        }
      })

      this.setData({ statusTabs })
    } catch (error) {
      console.error('加载订单统计失败:', error)
    }
  },

  /**
   * 刷新数据列表
   */
  async refreshOrders() {
    if (this.data.currentStatus === 'refund_afterSale') {
      // 对于退款页面，使用优化的加载方法
      try {
        const refundResult = await this.loadRefunds(true)
        // 同时更新统计数据，但使用已获取的退款总数
        const orderCounts = await OrderService.getOrderCounts()
        const statusTabs = this.data.statusTabs.map(tab => {
          if (tab.key === '') {
            return { ...tab, count: orderCounts.total }
          } else if (tab.key === 'refund_afterSale') {
            return { ...tab, count: refundResult.total || 0 }
          } else {
            return { ...tab, count: orderCounts[tab.key] || 0 }
          }
        })
        this.setData({ statusTabs })
      } catch (error) {
        console.error('刷新退款数据失败:', error)
      }
    } else {
      await this.loadOrders(true)
      await this.loadOrderCounts()
    }
  },

  /**
   * 切换状态标签
   */
  onTabChange(e: any) {
    const status = e.currentTarget.dataset.status
    if (status === this.data.currentStatus) return

    this.setData({
      currentStatus: status,
      page: 1
    })

    // 根据状态类型加载不同的数据
    if (status === 'refund_afterSale') {
      // 切换到退款页面时，使用优化的加载方法
      this.loadRefundsAndCounts()
    } else {
      this.loadOrders(true)
    }
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail(e: any) {
    const orderId = e.currentTarget.dataset.orderId
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?orderId=${orderId}`
    })
  },

  /**
   * 订单操作处理
   */
  async handleOrderAction(e: any) {
    const { action, orderId, orderNumber } = e.currentTarget.dataset
    const order = this.data.orders.find(o => o.id === orderId)

    if (!order) return

    switch (action) {
      case 'pay':
        await this.payOrder(order)
        break
      case 'cancel':
        await this.cancelOrder(orderId)
        break
      case 'confirm':
        await this.confirmOrder(orderId)
        break
      case 'delete':
        await this.deleteOrder(orderId)
        break
      case 'prescription':
        this.submitPrescription(orderId)
        break

      case 'review':
        this.reviewOrder(orderId)
        break
      case 'rebuy':
        this.rebuyOrder(order)
        break
      case 'refund':
        this.applyRefund(order)
        break
      default:
        console.log('未知操作:', action)
    }
  },

  /**
   * 支付订单
   */
  async payOrder(order: OrderInfo) {
    try {
      wx.showLoading({ title: '正在跳转支付...' })

      const paymentData: CreatePaymentRequest = {
        orderNo: order.orderNumber,
        totalAmount: order.totalAmount,
        description: this.generateOrderDescription(order.items),
        attach: JSON.stringify({
          orderId: order.id,
          fromOrderList: true
        })
      }

      await PaymentService.processPayment(paymentData)

    } catch (error: any) {
      console.error('支付失败:', error)
      if (error.message !== '用户取消支付') {
        wx.showToast({
          title: '支付失败，请重试',
          icon: 'error'
        })
      }
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 取消订单
   */
  async cancelOrder(orderId: number) {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '取消中...' })

            const success = await OrderService.cancelOrder(orderId)
            if (success) {
              wx.showToast({
                title: '订单已取消',
                icon: 'success'
              })
              this.refreshOrders()
            } else {
              wx.showToast({
                title: '取消失败，请重试',
                icon: 'error'
              })
            }
          } catch (error) {
            console.error('取消订单失败:', error)
            wx.showToast({
              title: '取消失败，请重试',
              icon: 'error'
            })
          } finally {
            wx.hideLoading()
          }
        }
      }
    })
  },

  /**
   * 确认收货
   */
  async confirmOrder(orderId: number) {
    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '确认中...' })

            const success = await OrderService.confirmReceived(orderId)
            if (success) {
              wx.showToast({
                title: '确认收货成功',
                icon: 'success'
              })
              this.refreshOrders()
            } else {
              wx.showToast({
                title: '确认失败，请重试',
                icon: 'error'
              })
            }
          } catch (error) {
            console.error('确认收货失败:', error)
            wx.showToast({
              title: '确认失败，请重试',
              icon: 'error'
            })
          } finally {
            wx.hideLoading()
          }
        }
      }
    })
  },

  /**
   * 删除订单
   */
  async deleteOrder(orderId: number) {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个订单吗？删除后无法恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '删除中...' })

            const success = await OrderService.deleteOrder(orderId)
            if (success) {
              wx.showToast({
                title: '订单已删除',
                icon: 'success'
              })
              this.refreshOrders()
            } else {
              wx.showToast({
                title: '删除失败，请重试',
                icon: 'error'
              })
            }
          } catch (error) {
            console.error('删除订单失败:', error)
            wx.showToast({
              title: '删除失败，请重试',
              icon: 'error'
            })
          } finally {
            wx.hideLoading()
          }
        }
      }
    })
  },

  /**
   * 提交处方
   */
  submitPrescription(orderId: number) {
    wx.navigateTo({
      url: `/pages/prescription/prescription?orderId=${orderId}`
    })
  },



  /**
   * 评价订单
   */
  reviewOrder(orderId: number) {
    wx.navigateTo({
      url: `/pages/review/review?orderId=${orderId}`
    })
  },

  /**
   * 再次购买
   */
  rebuyOrder(order: OrderInfo) {
    // 将订单商品加入购物车
    wx.showModal({
      title: '再次购买',
      content: '将订单中的商品重新加入购物车？',
      success: (res) => {
        if (res.confirm) {
          // TODO: 实现将订单商品加入购物车的逻辑
          wx.showToast({
            title: '已加入购物车',
            icon: 'success'
          })

          // 跳转到购物车
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/cart/cart'
            })
          }, 1500)
        }
      }
    })
  },

  /**
   * 申请退款
   */
  applyRefund(order: OrderInfo) {
    wx.navigateTo({
      url: `/pages/refund-apply/refund-apply?orderId=${order.id}`
    })
  },

  /**
   * 查看退款详情
   */
  viewRefundDetail(e: any) {
    const refundId = e.currentTarget.dataset.refundId
    wx.navigateTo({
      url: `/pages/refund-detail/refund-detail?refundId=${refundId}`
    })
  },

  /**
   * 退款操作处理
   */
  async handleRefundAction(e: any) {
    const { action, refundId } = e.currentTarget.dataset
    const refund = this.data.refunds.find(r => r.id === refundId)

    if (!refund) return

    switch (action) {
      case 'cancel':
        await this.cancelRefund(refundId)
        break
      case 'submit_shipping':
        this.submitShipping(refundId)
        break
      case 'view_detail':
        this.viewRefundDetail(e)
        break
      default:
        console.log('未知操作:', action)
    }
  },

  /**
   * 取消退款申请
   */
  async cancelRefund(refundId: number) {
    try {
      const result = await new Promise<boolean>((resolve) => {
        wx.showModal({
          title: '确认取消',
          content: '确定要取消这个退款申请吗？',
          success: (res) => resolve(res.confirm),
          fail: () => resolve(false)
        })
      })

      if (!result) return

      wx.showLoading({ title: '取消中...' })

      await RefundService.cancelRefundRequest(refundId)

      wx.hideLoading()
      wx.showToast({
        title: '取消成功',
        icon: 'success'
      })

      // 重新加载退款列表
      setTimeout(() => {
        this.loadRefunds(true)
      }, 1500)

    } catch (error: any) {
      wx.hideLoading()
      console.error('取消退款申请失败:', error)
      wx.showToast({
        title: error.message || '取消失败',
        icon: 'error'
      })
    }
  },

  /**
   * 提交物流信息
   */
  submitShipping(refundId: number) {
    wx.navigateTo({
      url: `/pages/refund-detail/refund-detail?refundId=${refundId}&action=shipping`
    })
  },

  /**
   * 生成订单描述
   */
  generateOrderDescription(items: any[]): string {
    if (items.length === 1) {
      return items[0].productName
    } else if (items.length <= 3) {
      return items.map(item => item.productName).join('、')
    } else {
      return `${items[0].productName}等${items.length}件商品`
    }
  },

  /**
   * 格式化金额显示
   */
  formatAmount(amount: number): string {
    return PaymentService.formatAmount(amount)
  },

  /**
   * 复制订单号
   */
  copyOrderNumber(e: any) {
    const orderNumber = e.currentTarget.dataset.orderNumber
    if (!orderNumber) {
      wx.showToast({
        title: '订单号不存在',
        icon: 'none'
      })
      return
    }

    wx.setClipboardData({
      data: orderNumber,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success',
          duration: 1500
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 格式化订单状态
   */
  formatOrderStatus(status: string): string {
    return OrderService.formatOrderStatus(status)
  },

  /**
   * 获取订单操作按钮
   */
  getOrderActions(status: string, orderInfo?: any) {
    return OrderService.getOrderActions(status, orderInfo)
  },

  /**
   * 加载更多
   */
  async loadMore() {
    if (!this.data.hasMore || this.data.loading) return

    this.setData({
      page: this.data.page + 1
    })

    if (this.data.currentStatus === 'refund_afterSale') {
      await this.loadRefunds()
    } else {
      await this.loadOrders()
    }
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    await this.refreshOrders()
    wx.stopPullDownRefresh()
  },

  /**
   * 上拉加载更多
   */
  async onReachBottom() {
    await this.loadMore()
  },

  /**
   * 处理从微信订单管理跳转查看特定订单
   * @param orderNo 订单号
   */
  async handleWechatOrderView(orderNo: string) {
    try {
      wx.showLoading({ title: '加载订单...' })

      // 根据订单号查询订单详情
      const orderInfo = await OrderService.getOrderByNumber(orderNo)

      if (orderInfo) {
        // 设置页面标题 - 统一显示为"我的订单"
        wx.setNavigationBarTitle({
          title: '我的订单'
        })

        // 显示该订单
        this.setData({
          orders: [orderInfo],
          loading: false,
          currentStatus: orderInfo.status
        })

        // 显示来源提示
        wx.showToast({
          title: '已为您找到该订单',
          icon: 'success',
          duration: 2000
        })
      } else {
        // 订单不存在
        wx.showModal({
          title: '订单不存在',
          content: '未找到该订单，可能已被删除或订单号错误',
          showCancel: false,
          success: () => {
            // 跳转到订单列表
            this.setData({
              currentStatus: '',
              loading: false
            })
            this.loadOrders()
          }
        })
      }
    } catch (error) {
      console.error('查询订单失败:', error)
      wx.showModal({
        title: '查询失败',
        content: '查询订单失败，请稍后重试',
        showCancel: false,
        success: () => {
          // 跳转到订单列表
          this.setData({
            currentStatus: '',
            loading: false
          })
          this.loadOrders()
        }
      })
    } finally {
      wx.hideLoading()
    }
  }
})
