<!--pages/review-list/review-list.wxml-->
<view class="container">
  
  <!-- Loading State -->
  <view wx:if="{{loading && reviews.length === 0}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- Content -->
  <view wx:else class="content">
    
    <!-- Review Stats -->
    <view wx:if="{{reviewStats}}" class="review-stats">
      <view class="stats-header">
        <view class="average-rating">
          <text class="rating-number">{{reviewStats.averageRating}}</text>
          <view class="rating-stars">
            <text class="stars">{{reviewStats.displayAverageRating}}</text>
          </view>
        </view>
        <view class="stats-info">
          <text class="total-reviews">共{{reviewStats.totalReviews}}条评价</text>
          <text class="positive-rate">好评率{{reviewStats.displayPositiveRate}}%</text>
        </view>
      </view>
    </view>

    <!-- Review List -->
    <view class="review-list">
      <view wx:if="{{reviews.length === 0 && !loading}}" class="review-empty">
        <text>暂无评价</text>
      </view>
      
      <view wx:for="{{reviews}}" wx:key="id" wx:for-index="reviewIndex" class="review-item">
        <view class="review-header">
          <image class="user-avatar"
                 src="{{item.displayAvatar}}"
                 mode="aspectFill"></image>
          <view class="review-main">
            <view class="user-info">
              <text class="user-name">{{item.displayName}}</text>
              <view class="review-meta">
                <view class="rating-stars">
                  <text class="stars">{{item.displayRating}}</text>
                </view>
                <text class="review-time">{{item.displayTime}}</text>
                <text wx:if="{{item.isVerifiedPurchase}}" class="verified-badge">已购买</text>
              </view>
            </view>

            <view class="review-content">
              <text class="review-text">{{item.content}}</text>

              <!-- 评价图片 -->
              <view wx:if="{{item.images && item.images.length > 0}}" class="review-images">
                <image wx:for="{{item.images}}"
                       wx:key="id"
                       wx:for-index="imageIndex"
                       class="review-image"
                       src="{{item.imageUrl}}"
                       mode="aspectFill"
                       bindtap="onReviewImagePreview"
                       data-review-index="{{reviewIndex}}"
                       data-image-index="{{imageIndex}}"></image>
              </view>

              <!-- 商家回复 -->
              <view wx:if="{{item.adminReply}}" class="admin-reply">
                <view class="reply-header">
                  <text class="reply-label">商家回复：</text>
                  <text class="reply-time">{{item.displayAdminReplyTime}}</text>
                </view>
                <text class="reply-content">{{item.adminReply}}</text>
              </view>
            </view>

            <view class="review-actions">
              <view class="action-item">
                <text class="like-count">{{item.likeCount > 0 ? item.likeCount : ''}}</text>
                <text class="like-text">赞</text>
              </view>
              <view wx:if="{{item.replyCount > 0}}" class="action-item">
                <text class="reply-count">{{item.replyCount}}</text>
                <text class="reply-text">回复</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view wx:if="{{reviewHasMore}}" class="load-more">
        <text wx:if="{{!loading}}">上拉加载更多</text>
        <text wx:else>加载中...</text>
      </view>
      
      <view wx:if="{{!reviewHasMore && reviews.length > 0}}" class="no-more">
        <text>没有更多评价了</text>
      </view>
    </view>
  </view>
</view>
