// pages/review-list/review-list.ts
import ReviewService from '../../services/reviewService'
import { ReviewDTO, ReviewStatsDTO, PageResult } from '../../types/review'

interface ReviewListData {
  loading: boolean
  productId: number
  productName: string
  reviewStats: ReviewStatsDTO | null
  reviews: ReviewDTO[]
  reviewPage: number
  reviewPageSize: number
  reviewHasMore: boolean
  refreshing: boolean
}

Page<ReviewListData>({
  data: {
    loading: true,
    productId: 0,
    productName: '',
    reviewStats: null,
    reviews: [],
    reviewPage: 1,
    reviewPageSize: 10,
    reviewHasMore: true,
    refreshing: false
  },

  onLoad(options: any) {
    const productId = parseInt(options.productId || '0')
    const productName = options.productName || '商品评价'
    
    this.setData({
      productId,
      productName
    })

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: `${productName} - 评价`
    })

    this.loadReviewData()
  },

  /**
   * 加载评论数据
   */
  async loadReviewData() {
    try {
      this.setData({ loading: true })

      const { productId } = this.data

      // 同时加载统计和评论列表
      const results = await Promise.all([
        ReviewService.getProductReviewStats(productId).catch(() => null),
        ReviewService.getProductReviews(productId, {
          page: 1,
          size: this.data.reviewPageSize,
          sortBy: 'created_at'
        })
      ])
      const stats = results[0]
      const reviewResult = results[1]

      // 处理统计数据
      let processedStats = null
      if (stats) {
        processedStats = Object.assign({}, stats, {
          displayAverageRating: ReviewService.formatRating(stats.averageRating),
          displayPositiveRate: (stats.positiveRate).toFixed(1)
        })
      }

      // 处理评论数据
      const processedReviews = reviewResult.records.map(review => {
        return Object.assign({}, review, {
          displayName: review.isAnonymous ? '匿名用户' : review.userName,
          displayAvatar: review.isAnonymous ? '/images/default-avatar.png' : (review.userAvatar || '/images/default-avatar.png'),
          displayRating: ReviewService.formatRating(review.rating),
          displayTime: ReviewService.formatTime(review.createdAt),
          displayAdminReplyTime: review.adminReplyTime ? ReviewService.formatTime(review.adminReplyTime) : ''
        })
      })

      this.setData({
        reviewStats: processedStats,
        reviews: processedReviews,
        reviewHasMore: reviewResult.hasNext,
        reviewPage: 2,
        loading: false
      })

    } catch (error) {
      console.error('加载评论数据失败:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载更多评论
   */
  async loadMoreReviews() {
    if (this.data.loading || !this.data.reviewHasMore) {
      return
    }

    try {
      this.setData({ loading: true })

      const { productId, reviewPage, reviewPageSize } = this.data
      
      const result = await ReviewService.getProductReviews(productId, {
        page: reviewPage,
        size: reviewPageSize,
        sortBy: 'created_at'
      })

      // 处理新评论数据
      const processedReviews = result.records.map(review => {
        return Object.assign({}, review, {
          displayName: review.isAnonymous ? '匿名用户' : review.userName,
          displayAvatar: review.isAnonymous ? '/images/default-avatar.png' : (review.userAvatar || '/images/default-avatar.png'),
          displayRating: ReviewService.formatRating(review.rating),
          displayTime: ReviewService.formatTime(review.createdAt),
          displayAdminReplyTime: review.adminReplyTime ? ReviewService.formatTime(review.adminReplyTime) : ''
        })
      })

      this.setData({
        reviews: this.data.reviews.concat(processedReviews),
        reviewHasMore: result.hasNext,
        reviewPage: this.data.reviewPage + 1,
        loading: false
      })

    } catch (error) {
      console.error('加载更多评论失败:', error)
      this.setData({ loading: false })
    }
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      this.setData({
        refreshing: true,
        reviewPage: 1,
        reviews: [],
        reviewHasMore: true
      })

      await this.loadReviewData()
      
    } finally {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    this.loadMoreReviews()
  },

  /**
   * 评论图片预览
   */
  onReviewImagePreview(e: any) {
    const { reviewIndex, imageIndex } = e.currentTarget.dataset
    const review = this.data.reviews[reviewIndex]
    
    if (review && review.images && review.images.length > 0) {
      const imageUrls = review.images.map(img => img.imageUrl)
      wx.previewImage({
        current: imageUrls[imageIndex],
        urls: imageUrls
      })
    }
  },

  /**
   * 分享
   */
  onShareAppMessage() {
    const { productName } = this.data
    return {
      title: `${productName} - 用户评价`,
      path: `/pages/review-list/review-list?productId=${this.data.productId}&productName=${encodeURIComponent(productName)}`
    }
  }
})
