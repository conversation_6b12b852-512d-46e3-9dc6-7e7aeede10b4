/* pages/review-list/review-list.wxss */
.container {
  background-color: #f9fafb;
  min-height: 100vh;
  padding-top: 10rpx;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  padding: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #6b7280;
}

/* Content */
.content {
  padding: 0;
  width: 96%;
}

/* Review Stats */
.review-stats {
  margin-bottom: 24rpx;
  padding: 24rpx;
  background-color: #ffffff;
}

.stats-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.average-rating {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 32rpx;
}

.rating-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #f59e0b;
  line-height: 1;
}

.rating-stars {
  margin-top: 8rpx;
}

.stars {
  font-size: 24rpx;
  color: #f59e0b;
}

.stats-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.total-reviews {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}

.positive-rate {
  font-size: 24rpx;
  color: #6b7280;
}

/* Review List */
.review-list {
  background-color: #ffffff;
}

.review-empty {
  text-align: center;
  padding: 80rpx 0;
  color: #9ca3af;
  font-size: 28rpx;
}

.review-item {
  padding: 24rpx;
  border-bottom: 1rpx solid #f3f4f6;
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: #f3f4f6;
  flex-shrink: 0;
}

.review-main {
  flex: 1;
}

.user-info {
  margin-bottom: 16rpx;
}

.user-name {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.review-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.review-time {
  font-size: 24rpx;
  color: #9ca3af;
}

.verified-badge {
  font-size: 20rpx;
  color: #059669;
  background-color: #d1fae5;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}

.review-content {
  margin-bottom: 16rpx;
}

.review-text {
  font-size: 28rpx;
  color: #4b5563;
  line-height: 1.6;
  display: block;
  margin-bottom: 16rpx;
}

/* Review Images */
.review-images {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.review-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background-color: #f3f4f6;
}

/* Admin Reply */
.admin-reply {
  background-color: #f8fafc;
  padding: 16rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #3b82f6;
  margin-bottom: 16rpx;
}

.reply-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.reply-label {
  font-size: 24rpx;
  color: #3b82f6;
  font-weight: 500;
}

.reply-time {
  font-size: 20rpx;
  color: #9ca3af;
}

.reply-content {
  font-size: 26rpx;
  color: #4b5563;
  line-height: 1.5;
}

/* Review Actions */
.review-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #9ca3af;
}

.like-count,
.reply-count {
  font-size: 24rpx;
}

.like-text,
.reply-text {
  font-size: 24rpx;
}

/* Load More */
.load-more {
  text-align: center;
  padding: 32rpx 0;
  color: #9ca3af;
  font-size: 28rpx;
}

.no-more {
  text-align: center;
  padding: 32rpx 0;
  color: #9ca3af;
  font-size: 24rpx;
}
