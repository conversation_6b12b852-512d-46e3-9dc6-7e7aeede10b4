// pages/product-detail/product-detail.ts
import ProductService from '../../services/productService'
import CartService from '../../services/cartService'
import ReviewService from '../../services/reviewService'
import { ProductWithSkusDTO, ProductSkuDTO } from '../../types/product'
import { ReviewDTO, ReviewStatsDTO } from '../../types/review'

// SKU属性选项
interface SkuAttributeOption {
  value: string
  available: boolean // 是否有库存可选
  selected: boolean  // 是否被选中
}

// SKU属性组
interface SkuAttributeGroup {
  name: string
  options: SkuAttributeOption[]
}

interface ProductDetailData {
  loading: boolean
  productId: number
  productName: string
  productData: ProductWithSkusDTO | null
  selectedSku: ProductSkuDTO | null
  selectedSkuIndex: number
  quantity: number
  showSkuSelector: boolean
  skuAction: 'buyNow' | 'addToCart' | null // 记录SKU选择器的触发动作
  currentImageIndex: number
  allImages: string[]
  detailImages: string[]
  // SKU属性选择相关
  skuAttributeGroups: SkuAttributeGroup[] // 属性分组
  selectedAttributes: { [key: string]: string } // 当前选中的属性值
  // 评论相关
  reviewStats: ReviewStatsDTO | null
  previewReviews: ReviewDTO[]  // 预览评论（最多2条）
  reviews: ReviewDTO[]         // 完整评论列表
  reviewLoading: boolean       // 评论加载状态
  reviewHasMore: boolean       // 是否有更多评论
  reviewPage: number           // 评论页码
  reviewPageSize: number       // 评论页大小
}

Page<ProductDetailData>({
  data: {
    loading: true,
    productId: 0,
    productName: '',
    productData: null,
    selectedSku: null,
    selectedSkuIndex: 0,
    quantity: 1,
    showSkuSelector: false,
    skuAction: null, // 初始化SKU选择器的触发动作为null
    currentImageIndex: 0,
    allImages: [],
    detailImages: [],
    // SKU属性选择相关
    skuAttributeGroups: [],
    selectedAttributes: {},
    // 评论相关
    reviewStats: null,
    previewReviews: [],
    reviews: [],
    reviewLoading: false,
    reviewHasMore: true,
    reviewPage: 1,
    reviewPageSize: 10
  },

  onLoad(options: any) {
    console.log('Product detail page loaded with options:', options)

    // 获取页面参数 - 支持id和productId两种参数名
    const productId = parseInt(options.id || options.productId) || 0
    const productName = options.productName || options.name || '商品详情'

    console.log('原始参数:', options)
    console.log('解析的商品ID:', productId, '类型:', typeof productId)

    this.setData({
      productId,
      productName
    })

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: productName
    })

    // 加载商品详情数据
    this.loadProductDetail()
  },

  /**
   * 加载商品详情数据
   */
  async loadProductDetail() {
    try {
      this.setData({ loading: true })

      const { productId } = this.data
      console.log('开始调用商品详情接口:', `/api/public/products/${productId}/with-skus`)

      const productData = await ProductService.getProductWithSkus(productId)

      console.log('获取到的商品详情数据:', productData)

      // 选择默认SKU（第一个可用的SKU）
      const availableSkus = productData.skus.filter(sku => sku.isActive && sku.stockQuantity > 0)
      const selectedSku = availableSkus.length > 0 ? availableSkus[0] : productData.skus[0]
      const selectedSkuIndex = productData.skus.findIndex(sku => sku.id === (selectedSku && selectedSku.id)) || 0

      // 构建所有图片数组（主图 + 商品图片）
      const allImages: string[] = []
      if (productData.product.mainImageUrl) {
        allImages.push(productData.product.mainImageUrl)
      }
      if (productData.product.productImages && productData.product.productImages.length > 0) {
        // 按 sortOrder 排序
        const sortedImages = productData.product.productImages.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
        sortedImages.forEach(img => {
          if (img.imageUrl) {
            allImages.push(img.imageUrl)
          }
        })
      }
      // 如果没有图片，使用默认图片
      if (allImages.length === 0) {
        allImages.push('/images/products/essilor.jpg')
      }

      // 构建详情图片数组
      const detailImages: string[] = []
      if (productData.product.detailImages && productData.product.detailImages.length > 0) {
        // 按 sortOrder 排序
        const sortedDetailImages = productData.product.detailImages.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
        sortedDetailImages.forEach(img => {
          if (img.imageUrl) {
            detailImages.push(img.imageUrl)
          }
        })
      }

      // 处理SKU属性分组
      const { skuAttributeGroups, selectedAttributes } = this.processSkuAttributes(productData.skus, selectedSku)

      this.setData({
        productData,
        selectedSku,
        selectedSkuIndex,
        allImages,
        detailImages,
        currentImageIndex: 0,
        skuAttributeGroups,
        selectedAttributes,
        loading: false
      })

      // 自动加载评论预览
      this.loadReviewPreview()

    } catch (error) {
      console.error('加载商品详情失败:', error)
      this.setData({ loading: false })

      wx.showModal({
        title: '加载失败',
        content: '商品详情加载失败，请检查网络连接后重试',
        showCancel: true,
        cancelText: '返回',
        confirmText: '重试',
        success: (res) => {
          if (res.confirm) {
            // 用户点击重试
            this.loadProductDetail()
          } else {
            // 用户点击返回
            wx.navigateBack()
          }
        }
      })
    }
  },

  /**
   * 处理SKU属性，生成属性分组和选项
   * @param skus SKU列表
   * @param selectedSku 当前选中的SKU
   * @returns 属性分组和选中的属性值
   */
  processSkuAttributes(skus: ProductSkuDTO[], selectedSku: ProductSkuDTO | null) {
    const attributeGroups: SkuAttributeGroup[] = []
    const selectedAttributes: { [key: string]: string } = {}

    if (!skus || skus.length === 0) {
      return { skuAttributeGroups: attributeGroups, selectedAttributes }
    }

    // 收集所有属性名称
    const attributeNames = new Set<string>()
    skus.forEach(sku => {
      if (sku.attributes) {
        Object.keys(sku.attributes).forEach(name => attributeNames.add(name))
      }
    })

    // 为每个属性名称创建分组
    attributeNames.forEach(attributeName => {
      const options = new Set<string>()

      // 收集该属性的所有可能值
      skus.forEach(sku => {
        if (sku.attributes && sku.attributes[attributeName]) {
          options.add(sku.attributes[attributeName])
        }
      })

      // 创建属性选项
      const attributeOptions: SkuAttributeOption[] = Array.from(options).map(value => {
        // 检查该属性值是否有可用库存
        const hasAvailableStock = skus.some(sku =>
          sku.attributes &&
          sku.attributes[attributeName] === value &&
          sku.isActive &&
          sku.stockQuantity > 0
        )

        // 检查是否为当前选中的值
        const isSelected = selectedSku &&
          selectedSku.attributes &&
          selectedSku.attributes[attributeName] === value

        if (isSelected) {
          selectedAttributes[attributeName] = value
        }

        return {
          value,
          available: hasAvailableStock,
          selected: !!isSelected
        }
      })

      // 按值排序（可以根据需要调整排序逻辑）
      attributeOptions.sort((a, b) => a.value.localeCompare(b.value))

      attributeGroups.push({
        name: attributeName,
        options: attributeOptions
      })
    })

    // 按属性名称排序（可以根据需要调整排序逻辑）
    attributeGroups.sort((a, b) => a.name.localeCompare(b.name))

    return { skuAttributeGroups: attributeGroups, selectedAttributes }
  },

  /**
   * 处理属性选择
   * @param e 事件对象
   */
  onAttributeSelect(e: any) {
    const { attributeName, value } = e.currentTarget.dataset
    const { selectedAttributes, productData } = this.data

    console.log('选择属性:', attributeName, '值:', value)

    // 更新选中的属性
    const newSelectedAttributes = { ...selectedAttributes }
    newSelectedAttributes[attributeName] = value

    // 根据新的属性组合查找匹配的SKU
    const matchedSku = this.findMatchingSku(productData!.skus, newSelectedAttributes)

    if (matchedSku) {
      const selectedSkuIndex = productData!.skus.findIndex(sku => sku.id === matchedSku.id)

      // 重新处理属性分组，更新选中状态和可用性
      const { skuAttributeGroups } = this.processSkuAttributesWithSelection(
        productData!.skus,
        newSelectedAttributes
      )

      this.setData({
        selectedSku: matchedSku,
        selectedSkuIndex,
        selectedAttributes: newSelectedAttributes,
        skuAttributeGroups
      })

      console.log('找到匹配的SKU:', matchedSku)
    } else {
      console.log('未找到匹配的SKU，保持当前选择')
    }
  },

  /**
   * 根据选中的属性查找匹配的SKU
   * @param skus SKU列表
   * @param selectedAttributes 选中的属性
   * @returns 匹配的SKU或null
   */
  findMatchingSku(skus: ProductSkuDTO[], selectedAttributes: { [key: string]: string }): ProductSkuDTO | null {
    return skus.find(sku => {
      if (!sku.attributes || !sku.isActive || sku.stockQuantity <= 0) {
        return false
      }

      // 检查所有选中的属性是否都匹配
      return Object.keys(selectedAttributes).every(attrName =>
        sku.attributes![attrName] === selectedAttributes[attrName]
      )
    }) || null
  },

  /**
   * 根据当前选择处理SKU属性，更新可用性
   * @param skus SKU列表
   * @param selectedAttributes 当前选中的属性
   * @returns 更新后的属性分组
   */
  processSkuAttributesWithSelection(skus: ProductSkuDTO[], selectedAttributes: { [key: string]: string }) {
    const attributeGroups: SkuAttributeGroup[] = []

    if (!skus || skus.length === 0) {
      return { skuAttributeGroups: attributeGroups }
    }

    // 收集所有属性名称
    const attributeNames = new Set<string>()
    skus.forEach(sku => {
      if (sku.attributes) {
        Object.keys(sku.attributes).forEach(name => attributeNames.add(name))
      }
    })

    // 为每个属性名称创建分组
    attributeNames.forEach(attributeName => {
      const options = new Set<string>()

      // 收集该属性的所有可能值
      skus.forEach(sku => {
        if (sku.attributes && sku.attributes[attributeName]) {
          options.add(sku.attributes[attributeName])
        }
      })

      // 创建属性选项
      const attributeOptions: SkuAttributeOption[] = Array.from(options).map(value => {
        // 构建临时的属性组合来检查可用性
        const tempAttributes = { ...selectedAttributes }
        tempAttributes[attributeName] = value

        // 检查该属性值组合是否有可用库存
        const hasAvailableStock = skus.some(sku => {
          if (!sku.attributes || !sku.isActive || sku.stockQuantity <= 0) {
            return false
          }

          // 检查是否匹配所有已选属性
          return Object.keys(tempAttributes).every(attrName =>
            sku.attributes![attrName] === tempAttributes[attrName]
          )
        })

        // 检查是否为当前选中的值
        const isSelected = selectedAttributes[attributeName] === value

        return {
          value,
          available: hasAvailableStock,
          selected: isSelected
        }
      })

      // 按值排序
      attributeOptions.sort((a, b) => a.value.localeCompare(b.value))

      attributeGroups.push({
        name: attributeName,
        options: attributeOptions
      })
    })

    // 按属性名称排序
    attributeGroups.sort((a, b) => a.name.localeCompare(b.name))

    return { skuAttributeGroups: attributeGroups }
  },

  /**
   * 加载评论预览（最多2条）
   */
  async loadReviewPreview() {
    try {
      const { productId } = this.data
      console.log('=== 开始加载评论预览 ===', productId)

      // 同时加载统计和评论列表
      const results = await Promise.all([
        ReviewService.getProductReviewStats(productId).catch(() => null),
        ReviewService.getProductReviews(productId, { page: 1, size: 2, sortBy: 'created_at' }).catch(() => null)
      ])
      const stats = results[0]
      const reviewResult = results[1]

      // 处理统计数据
      let processedStats = null
      if (stats) {
        processedStats = Object.assign({}, stats, {
          displayAverageRating: ReviewService.formatRating(stats.averageRating),
          displayPositiveRate: (stats.positiveRate).toFixed(1)
        })
      }

      // 处理评论数据
      let processedReviews: ReviewDTO[] = []
      if (reviewResult && reviewResult.records) {
        processedReviews = reviewResult.records.map(review => {
          return Object.assign({}, review, {
            displayName: review.isAnonymous ? '匿名用户' : review.userName,
            displayAvatar: review.isAnonymous ? '/images/default-avatar.png' : (review.userAvatar || '/images/default-avatar.png'),
            displayRating: ReviewService.formatRating(review.rating),
            displayTime: ReviewService.formatTime(review.createdAt)
          })
        })
      }

      this.setData({
        reviewStats: processedStats,
        previewReviews: processedReviews
      })

      console.log('=== 评论预览加载完成 ===', { stats: processedStats, reviews: processedReviews })

    } catch (error) {
      console.error('=== 加载评论预览失败 ===', error)
    }
  },

  /**
   * 加载评论统计
   */
  async loadReviewStats() {
    try {
      const { productId } = this.data
      console.log('=== 开始加载评论统计 ===', productId)
      const stats = await ReviewService.getProductReviewStats(productId)
      console.log('评论统计加载成功:', stats)

      // 预处理统计数据
      const processedStats = Object.assign({}, stats, {
        displayAverageRating: ReviewService.formatRating(stats.averageRating),
        displayPositiveRate: (stats.positiveRate).toFixed(1),
        // 预计算各星级的百分比
        rating5Percent: stats.totalReviews > 0 ? (stats.rating5Count / stats.totalReviews * 100) : 0,
        rating4Percent: stats.totalReviews > 0 ? (stats.rating4Count / stats.totalReviews * 100) : 0,
        rating3Percent: stats.totalReviews > 0 ? (stats.rating3Count / stats.totalReviews * 100) : 0,
        rating2Percent: stats.totalReviews > 0 ? (stats.rating2Count / stats.totalReviews * 100) : 0,
        rating1Percent: stats.totalReviews > 0 ? (stats.rating1Count / stats.totalReviews * 100) : 0
      })

      this.setData({
        reviewStats: processedStats
      })
    } catch (error) {
      console.error('=== 加载评论统计失败 ===', error)
      // 设置默认统计数据
      this.setData({
        reviewStats: {
          productId: this.data.productId,
          totalReviews: 0,
          averageRating: 0,
          rating1Count: 0,
          rating2Count: 0,
          rating3Count: 0,
          rating4Count: 0,
          rating5Count: 0,
          withImagesCount: 0,
          verifiedPurchaseCount: 0,
          positiveRate: 0
        }
      })
    }
  },

  /**
   * 加载评论列表
   */
  async loadReviews(refresh = false) {
    try {
      console.log('=== 开始加载评论列表 ===', { refresh })

      if (refresh) {
        this.setData({
          reviewLoading: true,
          reviewPage: 1,
          reviews: [],
          reviewHasMore: true
        })
      } else {
        this.setData({ reviewLoading: true })
      }

      const { productId, reviewPage, reviewPageSize } = this.data
      console.log('评论请求参数:', { productId, reviewPage, reviewPageSize })

      const result = await ReviewService.getProductReviews(productId, {
        page: reviewPage,
        size: reviewPageSize,
        sortBy: 'created_at'
      })
      console.log('评论列表加载成功:', result)

      // 预处理评论数据，添加显示用的字段
      const processedReviews = result.records.map(review => {
        return Object.assign({}, review, {
          displayName: review.isAnonymous ? '匿名用户' : review.userName,
          displayAvatar: review.isAnonymous ? '/images/default-avatar.png' : (review.userAvatar || '/images/default-avatar.png'),
          displayRating: ReviewService.formatRating(review.rating),
          displayTime: ReviewService.formatTime(review.createdAt),
          displayAdminReplyTime: review.adminReplyTime ? ReviewService.formatTime(review.adminReplyTime) : ''
        })
      })

      const newReviews = refresh ? processedReviews : this.data.reviews.concat(processedReviews)

      this.setData({
        reviews: newReviews,
        reviewHasMore: result.hasNext,
        reviewPage: refresh ? 2 : this.data.reviewPage + 1,
        reviewLoading: false
      })

    } catch (error) {
      console.error('=== 加载评论列表失败 ===', error)
      this.setData({
        reviewLoading: false,
        reviewHasMore: false
      })
    }
  },



  /**
   * 滚动到商品详情
   */
  onScrollToDetail() {
    console.log('滚动到商品详情')
    wx.pageScrollTo({
      selector: '#product-detail-section',
      duration: 300
    })
  },

  /**
   * 打开评论页面
   */
  onOpenReviewPage() {
    const { productId } = this.data
    console.log('打开评论页面:', productId)
    wx.navigateTo({
      url: `/pages/review-list/review-list?productId=${productId}`
    })
  },

  /**
   * 处理用户点击“选择规格”区域
   */
  handleSelectSku() {
    this.showSkuSelector(null);
  },

  /**
   * 显示SKU选择器
   * @param action 记录触发选择器的意图
   */
  showSkuSelector(action: 'buyNow' | 'addToCart' | null = null) {
    this.setData({ 
      showSkuSelector: true,
      skuAction: action
    });
  },

  /**
   * 隐藏SKU选择器，并重置意图
   */
  hideSkuSelector() {
    this.setData({ 
      showSkuSelector: false,
      skuAction: null 
    });
  },

  /**
   * 数量减少
   */
  onQuantityDecrease() {
    const { quantity } = this.data
    if (quantity > 1) {
      this.setData({ quantity: quantity - 1 })
    }
  },

  /**
   * 数量增加
   */
  onQuantityIncrease() {
    const { quantity, selectedSku } = this.data
    const maxQuantity = selectedSku?.stockQuantity || 99

    if (quantity < maxQuantity) {
      this.setData({ quantity: quantity + 1 })
    } else {
      wx.showToast({
        title: `最多只能购买${maxQuantity}件`,
        icon: 'none'
      })
    }
  },

  /**
   * 数量输入
   */
  onQuantityInput(e: any) {
    const value = parseInt(e.detail.value) || 1
    const { selectedSku } = this.data
    const maxQuantity = selectedSku?.stockQuantity || 99

    let quantity = Math.max(1, Math.min(value, maxQuantity))

    this.setData({ quantity })
  },

  /**
   * 数量输入失焦
   */
  onQuantityBlur(e: any) {
    const value = parseInt(e.detail.value) || 1
    const { selectedSku } = this.data
    const maxQuantity = selectedSku?.stockQuantity || 99

    let quantity = Math.max(1, Math.min(value, maxQuantity))

    this.setData({ quantity })
  },

  /**
   * 选择SKU（保留原有方法以兼容）
   */
  onSkuSelect(e: any) {
    const skuIndex = e.currentTarget.dataset.index
    const { productData } = this.data

    if (productData && productData.skus[skuIndex]) {
      const selectedSku = productData.skus[skuIndex]

      // 检查SKU是否可选
      if (!selectedSku.isActive || selectedSku.stockQuantity <= 0) {
        wx.showToast({
          title: '该规格暂时缺货',
          icon: 'none'
        })
        return
      }

      // 重新处理属性分组
      const { skuAttributeGroups, selectedAttributes } = this.processSkuAttributes(productData.skus, selectedSku)

      this.setData({
        selectedSku,
        selectedSkuIndex: skuIndex,
        skuAttributeGroups,
        selectedAttributes
      })

      console.log('选择SKU:', selectedSku)
    }
  },

  /**
   * 确认SKU选择 - 核心逻辑处理
   */
  async onSkuConfirm() {
    const { skuAction, selectedSku, quantity, productData } = this.data;

    // 1. 验证SKU选择和库存
    if (!selectedSku) {
      wx.showToast({ title: '请选择商品规格', icon: 'none' });
      return;
    }
    if (!selectedSku.isActive || selectedSku.stockQuantity <= 0) {
      wx.showToast({ title: '商品暂时缺货', icon: 'none' });
      return;
    }
    if (quantity > selectedSku.stockQuantity) {
      wx.showToast({ title: '库存不足', icon: 'none' });
      return;
    }

    // 2. 根据意图执行操作
    if (skuAction === 'buyNow') {
      // 执行“立即购买”逻辑
      console.log('SKU确认后 -> 立即购买:', { sku: selectedSku, quantity });
      const LoginHelper = require('../../utils/loginHelper').LoginHelper;
      LoginHelper.requireLogin(() => {
        this.proceedToBuyNow(selectedSku, quantity, productData);
        this.hideSkuSelector();
      }, () => {
        console.log('用户取消登录，无法进行立即购买');
      });
    } else if (skuAction === 'addToCart') {
      // 执行“加入购物车”逻辑
      try {
        await CartService.addToCart({
          skuId: selectedSku.id,
          quantity: quantity
        });
        wx.showToast({ title: '已加入购物车', icon: 'success' });
        console.log('SKU确认后 -> 成功加入购物车:', { sku: selectedSku, quantity });
        this.hideSkuSelector();
      } catch (error) {
        console.error('加入购物车失败:', error);
        wx.showToast({ title: '加入购物车失败，请重试', icon: 'none' });
      }
    } else {
      // 如果没有特定意图（例如只是选择规格），则只关闭选择器
      this.hideSkuSelector();
    }
  },

  /**
   * 加入购物车 - 修改为打开SKU选择器
   */
  onAddToCart() {
    this.showSkuSelector('addToCart');
  },

  /**
   * 立即购买 - 修改为打开SKU选择器
   */
  onBuyNow() {
    this.showSkuSelector('buyNow');
  },

  /**
   * 执行立即购买流程
   */
  proceedToBuyNow(selectedSku: any, quantity: number, productData: any) {
    // 引入PaymentService进行价格计算
    const PaymentService = require('../../services/paymentService').default

    // 构建订单项数据
    const unitPriceInFen = PaymentService.yuanToFen(selectedSku.price)
    const subtotalInFen = PaymentService.calculatePriceInFen(selectedSku.price, quantity)

    // 构建SKU属性字符串
    let skuAttributes = ''
    if (selectedSku.attributes && Object.keys(selectedSku.attributes).length > 0) {
      skuAttributes = Object.entries(selectedSku.attributes)
        .map(([key, value]) => `${key}：${value}`)
        .join('，')
    } else {
      // 如果没有attributes，使用nameExtension作为备选
      skuAttributes = selectedSku.nameExtension || ''
    }

    const orderItem = {
      skuId: selectedSku.id,
      skuCode: selectedSku.skuCode,
      productName: productData?.product.name || '商品',
      skuAttributes: skuAttributes,
      unitPrice: unitPriceInFen,
      quantity: quantity,
      subtotal: subtotalInFen,
      productImageUrl: productData?.product.mainImageUrl || ''
    }

    // 将订单项数据存储到全局数据中
    const app = getApp<IAppOption>()
    app.globalData.buyNowItem = orderItem

    // 跳转到结算页面
    wx.navigateTo({
      url: '/pages/checkout/checkout?from=buyNow'
    })
  },

  /**
   * 跳转到购物车
   */
  onGoToCart() {
    wx.switchTab({
      url: '/pages/cart/cart'
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadProductDetail()
    wx.stopPullDownRefresh()
  },

  /**
   * 图片轮播变化
   */
  onImageChange(e: any) {
    this.setData({
      currentImageIndex: e.detail.current
    })
  },

  /**
   * 图片预览
   */
  onImagePreview(e: any) {
    const { allImages, currentImageIndex } = this.data
    const index = e.currentTarget.dataset.index || currentImageIndex

    wx.previewImage({
      current: allImages[index],
      urls: allImages
    })
  },

  /**
   * 详情图片预览
   */
  onDetailImagePreview(e: any) {
    const { detailImages } = this.data
    const index = e.currentTarget.dataset.index || 0

    wx.previewImage({
      current: detailImages[index],
      urls: detailImages
    })
  },

  /**
   * 评论图片预览
   */
  onReviewImagePreview(e: any) {
    const { reviewIndex, imageIndex } = e.currentTarget.dataset
    const review = this.data.reviews[reviewIndex]

    if (review && review.images && review.images.length > 0) {
      const imageUrls = review.images.map(img => img.imageUrl)
      wx.previewImage({
        current: imageUrls[imageIndex],
        urls: imageUrls
      })
    }
  },

  /**
   * 加载更多评论
   */
  onLoadMoreReviews() {
    if (!this.data.reviewLoading && this.data.reviewHasMore) {
      this.loadReviews(false)
    }
  },

  /**
   * 刷新评论列表
   */
  onRefreshReviews() {
    this.loadReviews(true)
  },

  /**
   * 格式化评分显示（用于模板）
   */
  formatRating(rating: number): string {
    return ReviewService.formatRating(rating)
  },

  /**
   * 格式化时间显示（用于模板）
   */
  formatTime(dateString: string): string {
    return ReviewService.formatTime(dateString)
  },

  /**
   * 处理匿名用户显示（用于模板）
   */
  getDisplayUserInfo(review: ReviewDTO): { displayName: string, displayAvatar: string } {
    return ReviewService.processAnonymousUser(review)
  },

  /**
   * 分享
   */
  onShareAppMessage() {
    const { productName, productId } = this.data
    return {
      title: `${productName} - 专业眼镜品牌`,
      path: `/pages/product-detail/product-detail?productId=${productId}&productName=${productName}`
    }
  }
})
