<!--pages/product-detail/product-detail.wxml-->
<view class="container">

  <!-- Loading State -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- Product Detail Content -->
  <view wx:else class="product-detail">
    
    <!-- Header Tabs -->
    <view class="header-tabs">
      <view
        class="tab-item active"
      >
        商品
      </view>
      <view
        class="tab-item"
        bindtap="onScrollToDetail"
      >
        详情
      </view>
      <view
        class="tab-item"
        bindtap="onOpenReviewPage"
      >
        评价
      </view>
    </view>

    <!-- Product Images -->
    <view class="product-images">
      <swiper
        class="image-swiper"
        indicator-dots="{{allImages.length > 1}}"
        indicator-color="rgba(255,255,255,0.5)"
        indicator-active-color="#ffffff"
        autoplay="{{false}}"
        circular="{{true}}"
        current="{{currentImageIndex}}"
        bindchange="onImageChange"
      >
        <swiper-item wx:for="{{allImages}}" wx:key="index">
          <image
            class="product-image"
            src="{{item}}"
            mode="aspectFit"
            bindtap="onImagePreview"
            data-index="{{index}}"
          ></image>
        </swiper-item>
      </swiper>
      <view wx:if="{{allImages.length > 1}}" class="image-indicator">
        {{currentImageIndex + 1}}/{{allImages.length}}
      </view>
    </view>

    <!-- Product Info -->
    <view class="product-info">
      <view class="price-section">
        <view class="current-price">¥{{selectedSku.price || productData.statistics.minPrice}}</view>
        <view wx:if="{{selectedSku.originalPrice && selectedSku.originalPrice > selectedSku.price}}" class="original-price">
          ¥{{selectedSku.originalPrice}}
        </view>
        <view class="sales-info">库存 {{selectedSku.stockQuantity || productData.statistics.totalStockQuantity}} 件</view>
      </view>

      <view class="product-title">{{productData.product.name}}</view>
      
      <view wx:if="{{productData.product.brandName}}" class="product-brand">
        品牌: {{productData.product.brandName}}
      </view>
    </view>





    <!-- Review Preview Section -->
    <view class="review-preview-section">
      <view class="section-header">
        <text class="section-title">商品评价</text>
        <view class="more-link" bindtap="onOpenReviewPage">
          <text>查看全部</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- Review Stats Summary -->
      <view class="review-stats-summary">
        <view wx:if="{{reviewStats && reviewStats.totalReviews > 0}}" class="rating-summary">
          <text class="rating-number">{{reviewStats.averageRating}}</text>
          <view class="rating-stars">
            <text class="stars">{{reviewStats.displayAverageRating}}</text>
          </view>
          <text class="review-count">{{reviewStats.totalReviews}}条评价</text>
          <text class="positive-rate">好评率{{reviewStats.displayPositiveRate}}%</text>
        </view>
        <view wx:else class="rating-summary">
          <text class="rating-number">5.0</text>
          <view class="rating-stars">
            <text class="stars">★★★★★</text>
          </view>
        </view>
      </view>

      <!-- Review List Preview (最多2条) -->
      <view class="review-preview-list">
        <view wx:if="{{previewReviews.length === 0 && (!reviewStats || reviewStats.totalReviews === 0)}}" class="review-empty">
          <!-- 不显示"暂无评价"文字 -->
        </view>

        <view wx:for="{{previewReviews}}" wx:key="id" class="review-preview-item">
          <view class="review-header">
            <image class="user-avatar" src="{{item.displayAvatar}}" mode="aspectFill"></image>
            <view class="review-main">
              <view class="user-info">
                <text class="user-name">{{item.displayName}}</text>
                <view class="rating-stars">
                  <text class="stars">{{item.displayRating}}</text>
                </view>
                <text class="review-time">{{item.displayTime}}</text>
              </view>
              <view class="review-content">
                <text class="review-text">{{item.content}}</text>
                <!-- 评价图片预览 -->
                <view wx:if="{{item.images && item.images.length > 0}}" class="review-images-preview">
                  <image wx:for="{{item.images}}" wx:for-item="img" wx:key="id"
                         class="review-image-small"
                         src="{{img.imageUrl}}"
                         mode="aspectFill"></image>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- Product Detail Section -->
    <view id="product-detail-section" class="product-detail-section">
      <view class="section-title">商品详情</view>
      <!-- Detail Images -->
      <view wx:if="{{detailImages.length > 0}}" class="detail-images">
        <view class="detail-images-list">
          <image
            wx:for="{{detailImages}}"
            wx:key="index"
            class="detail-image"
            src="{{item}}"
            mode="widthFix"
            bindtap="onDetailImagePreview"
            data-index="{{index}}"
          ></image>
        </view>
      </view>
    </view>



  </view>

  <!-- Bottom Action Bar -->
  <view class="bottom-action-bar">
    <view class="action-buttons">
      <view class="service-button">
        <button class="contact-overlay-button" open-type="contact"></button>
        <text class="service-icon">💬</text>
        <text class="service-text">客服</text>
      </view>
      <view class="cart-button" bindtap="onGoToCart">
        <text class="cart-icon">🛒</text>
        <text class="cart-text">购物车</text>
      </view>
    </view>
    <view class="purchase-buttons">
      <button class="add-cart-btn" bindtap="onAddToCart">加入购物车</button>
      <button class="buy-now-btn" bindtap="onBuyNow">立即购买</button>
    </view>
  </view>

  <!-- SKU Selector Modal -->
  <view wx:if="{{showSkuSelector}}" class="sku-modal">
    <view class="modal-mask" bindtap="hideSkuSelector"></view>
    <view class="modal-content">
      <view class="modal-header">
        <view class="product-summary">
          <image 
            class="summary-image" 
            src="{{productData.product.mainImageUrl || '/images/products/essilor.jpg'}}" 
            mode="aspectFill"
          ></image>
          <view class="summary-info">
            <view class="summary-price">¥{{selectedSku.price || productData.statistics.minPrice}}</view>
            <view class="summary-stock">库存 {{selectedSku.stockQuantity || productData.statistics.totalStockQuantity}} 件</view>
            <view class="summary-selected">已选：{{selectedSku.nameExtension || '请选择规格'}}</view>
          </view>
        </view>
        <view class="close-btn" bindtap="hideSkuSelector">✕</view>
      </view>

      <!-- SKU Attribute Groups -->
      <view class="sku-attributes">
        <view
          wx:for="{{skuAttributeGroups}}"
          wx:key="name"
          wx:for-item="group"
          class="attribute-group"
        >
          <view class="attribute-title">{{group.name}}</view>
          <view class="attribute-options">
            <view
              wx:for="{{group.options}}"
              wx:key="value"
              wx:for-item="option"
              class="attribute-option {{option.selected ? 'selected' : ''}} {{!option.available ? 'disabled' : ''}}"
              bindtap="onAttributeSelect"
              data-attribute-name="{{group.name}}"
              data-value="{{option.value}}"
            >
              <text class="option-text">{{option.value}}</text>
              <view wx:if="{{!option.available}}" class="unavailable-mark">×</view>
            </view>
          </view>
        </view>
      </view>

      <!-- Quantity Selector -->
      <view class="quantity-selector">
        <view class="quantity-title">数量</view>
        <view class="quantity-controls">
          <view
            class="quantity-btn {{quantity <= 1 ? 'disabled' : ''}}"
            bindtap="onQuantityDecrease"
          >-</view>
          <input
            class="quantity-input"
            type="number"
            value="{{quantity}}"
            bindinput="onQuantityInput"
            bindblur="onQuantityBlur"
          />
          <view
            class="quantity-btn {{quantity >= (selectedSku.stockQuantity || 99) ? 'disabled' : ''}}"
            bindtap="onQuantityIncrease"
          >+</view>
        </view>
      </view>

      <!-- Confirm Button -->
      <button class="confirm-btn" bindtap="onSkuConfirm">确定</button>
    </view>
  </view>

</view>
