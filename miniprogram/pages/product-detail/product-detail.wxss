/* pages/product-detail/product-detail.wxss */
.container {
  min-height: 100vh;
  background-color: #f9fafb;
  padding: 0;
  padding-bottom: calc(160rpx + env(safe-area-inset-bottom)); /* 为底部操作栏和安全区域留出空间 */
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
}

/* Product Detail Content */
.product-detail {
  padding-top: 1rpx;
  width: 96%;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  padding: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #6b7280;
}

/* Header Tabs */
.header-tabs {
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.tab-item {
  padding: 0 40rpx;
  font-size: 32rpx;
  color: #6b7280;
  position: relative;
}

.tab-item.active {
  color: #000000;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #000000;
}

/* Product Images */
.product-images {
  position: relative;
  background-color: #ffffff;
  margin-bottom: 16rpx;
}

.image-swiper {
  width: 100%;
  height: 750rpx;
}

.product-image {
  width: 100%;
  height: 750rpx;
}

.image-indicator {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

/* Product Info */
.product-info {
  background-color: #ffffff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.price-section {
  display: flex;
  align-items: baseline;
  margin-bottom: 24rpx;
}

.current-price {
  font-size: 48rpx;
  font-weight: 600;
  color: #ef4444;
  margin-right: 16rpx;
}

.original-price {
  font-size: 28rpx;
  color: #9ca3af;
  text-decoration: line-through;
  margin-right: 16rpx;
}

.sales-info {
  font-size: 24rpx;
  color: #6b7280;
  margin-left: auto;
}

.product-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.4;
  margin-bottom: 16rpx;
}

.product-brand {
  font-size: 28rpx;
  color: #6b7280;
}

/* 已删除的样式：SKU Selection 和 Product Description */

.full-desc {
  display: block;
  margin-top: 16rpx;
}

/* Product Detail Tab */
.product-detail-tab {
  background-color: #ffffff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.detail-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 24rpx;
}



/* Product Review Tab */
.product-review-tab {
  background-color: #ffffff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}



.review-empty {
  text-align: center;
  padding: 80rpx 0;
  color: #9ca3af;
  font-size: 28rpx;
}

/* Bottom Action Bar */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e7eb;
  display: flex;
  align-items: center;
  min-height: 120rpx;
  padding: 20rpx 32rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  z-index: 20;
  box-sizing: border-box;
  /* 确保在所有设备上都能正确显示 */
  width: 100%;
  max-width: 100vw;
}

.action-buttons {
  display: flex;
  margin-right: 32rpx;
  flex-shrink: 0; /* 防止收缩 */
}

.service-button,
.cart-button {
  position: relative; /* 为覆盖按钮提供定位上下文 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 80rpx;
}

.service-icon,
.cart-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.service-text,
.cart-text {
  font-size: 20rpx;
  color: #6b7280;
}

.contact-overlay-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0; /* 完全透明 */
}

.purchase-buttons {
  display: flex;
  flex: 1;
  height: 80rpx;
  gap: 16rpx;
  max-width: calc(100% - 280rpx); /* 减去左侧按钮和间距的宽度 */
  min-width: 0; /* 允许收缩 */
}

.add-cart-btn {
  flex: 1;
  min-width: 0; /* 允许收缩 */
  background-color: #f3f4f6;
  color: #374151;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  border-radius: 8rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap; /* 防止文字换行 */
  overflow: hidden; /* 隐藏溢出内容 */
}

.buy-now-btn {
  flex: 1;
  min-width: 0; /* 允许收缩 */
  background-color: #e60012; /* 修改为红色 */
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  border-radius: 8rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap; /* 防止文字换行 */
  overflow: hidden; /* 隐藏溢出内容 */
}

/* SKU Selector Modal */
.sku-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 30;
  display: flex;
  align-items: flex-end;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  padding: 32rpx;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
}

.product-summary {
  display: flex;
  flex: 1;
}

.summary-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
}

.summary-info {
  flex: 1;
}

.summary-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ef4444;
  margin-bottom: 8rpx;
}

.summary-stock {
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}

.summary-selected {
  font-size: 28rpx;
  color: #1f2937;
}

.close-btn {
  font-size: 32rpx;
  color: #9ca3af;
  padding: 8rpx;
}

/* SKU Options */
.sku-options {
  margin-bottom: 40rpx;
}

.option-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 24rpx;
}

.option-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background-color: #ffffff;
}

.option-item.selected {
  border-color: #000000;
  background-color: #f9fafb;
}

.option-item.disabled {
  opacity: 0.5;
  background-color: #f3f4f6;
}

.option-name {
  font-size: 28rpx;
  color: #1f2937;
  flex: 1;
}

.option-price {
  font-size: 28rpx;
  font-weight: 500;
  color: #ef4444;
  margin-right: 16rpx;
}

.option-stock {
  font-size: 24rpx;
  color: #9ca3af;
}

/* SKU Attributes */
.sku-attributes {
  margin-bottom: 40rpx;
}

.attribute-group {
  margin-bottom: 32rpx;
}

.attribute-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 24rpx;
}

.attribute-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.attribute-option {
  position: relative;
  padding: 16rpx 24rpx;
  background-color: #f8fafc;
  border: 2rpx solid #e5e7eb;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  min-width: 80rpx;
  text-align: center;
}

.attribute-option.selected {
  background-color: #eff6ff;
  border-color: #3b82f6;
  color: #3b82f6;
}

.attribute-option.disabled {
  background-color: #f3f4f6;
  border-color: #e5e7eb;
  color: #9ca3af;
  opacity: 0.6;
}

.option-text {
  font-size: 28rpx;
  font-weight: 500;
}

.unavailable-mark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 32rpx;
  color: #ef4444;
  font-weight: bold;
}

/* Quantity Selector */
.quantity-selector {
  margin-bottom: 40rpx;
}

.quantity-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 24rpx;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
  border: 2rpx solid #e5e7eb;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  transition: all 0.3s ease;
}

.quantity-btn.disabled {
  background-color: #f3f4f6;
  color: #9ca3af;
  opacity: 0.6;
}

.quantity-input {
  width: 120rpx;
  height: 60rpx;
  text-align: center;
  background-color: #ffffff;
  border: 2rpx solid #e5e7eb;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #1f2937;
}

/* Confirm Button */
.confirm-btn {
  width: 100%;
  height: 88rpx;
  background-color: #000000;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  border-radius: 16rpx;
}

/* 响应式适配 */
@media (max-height: 667px) {
  .container {
    padding-bottom: calc(140rpx + env(safe-area-inset-bottom));
  }

  .bottom-action-bar {
    min-height: 100rpx;
    padding: 10rpx 32rpx;
    padding-bottom: calc(10rpx + env(safe-area-inset-bottom));
  }

  .purchase-buttons {
    height: 70rpx;
    max-width: calc(100% - 260rpx); /* 小屏幕调整 */
  }

  .add-cart-btn,
  .buy-now-btn {
    height: 70rpx;
    font-size: 26rpx;
  }
}

/* 处理超小屏幕 */
@media (max-width: 375px) {
  .bottom-action-bar {
    padding: 16rpx 24rpx;
    padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  }

  .action-buttons {
    margin-right: 24rpx; /* 减小间距 */
  }

  .service-button,
  .cart-button {
    width: 100rpx; /* 减小左侧按钮宽度 */
  }

  .purchase-buttons {
    gap: 12rpx;
    max-width: calc(100% - 240rpx); /* 超小屏幕进一步调整 */
  }

  .add-cart-btn,
  .buy-now-btn {
    font-size: 24rpx;
  }
}

/* Detail Images */
.detail-images {
  margin-top: 32rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
}

.detail-images-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24rpx;
  text-align: center;
}

.detail-images-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.detail-image {
  width: 100%;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* Review Tab Styles */
.product-review-tab {
  padding: 24rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin: 0 24rpx 24rpx;
}

/* Review Stats */
.review-stats {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background-color: #f8fafc;
  border-radius: 12rpx;
}

.stats-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.average-rating {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 32rpx;
}

.rating-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #f59e0b;
  line-height: 1;
}

.rating-stars {
  margin-top: 8rpx;
}

.stars {
  font-size: 24rpx;
  color: #f59e0b;
}

.stats-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.total-reviews {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}

.positive-rate {
  font-size: 24rpx;
  color: #6b7280;
}

.rating-breakdown {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.rating-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.rating-label {
  font-size: 24rpx;
  color: #6b7280;
  width: 60rpx;
}

.rating-bar {
  flex: 1;
  height: 8rpx;
  background-color: #e5e7eb;
  border-radius: 4rpx;
  overflow: hidden;
}

.rating-fill {
  height: 100%;
  background-color: #f59e0b;
  transition: width 0.3s ease;
}

.rating-count {
  font-size: 24rpx;
  color: #6b7280;
  width: 40rpx;
  text-align: right;
}

/* Review List */
.review-list {
  margin-top: 32rpx;
}

.review-empty {
  text-align: center;
  padding: 80rpx 0;
  color: #9ca3af;
  font-size: 28rpx;
}

.review-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  margin-bottom: 16rpx;
}

.user-info {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: #f3f4f6;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.review-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.review-time {
  font-size: 24rpx;
  color: #9ca3af;
}

.verified-badge {
  font-size: 20rpx;
  color: #059669;
  background-color: #d1fae5;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}

.review-content {
  margin-bottom: 16rpx;
}

.review-text {
  font-size: 28rpx;
  color: #4b5563;
  line-height: 1.6;
  display: block;
  margin-bottom: 16rpx;
}

/* Review Images */
.review-images {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.review-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background-color: #f3f4f6;
}

/* Admin Reply */
.admin-reply {
  background-color: #f8fafc;
  padding: 16rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #3b82f6;
  margin-bottom: 16rpx;
}

.reply-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.reply-label {
  font-size: 24rpx;
  color: #3b82f6;
  font-weight: 500;
}

.reply-time {
  font-size: 20rpx;
  color: #9ca3af;
}

.reply-content {
  font-size: 26rpx;
  color: #4b5563;
  line-height: 1.5;
}

/* Review Actions */
.review-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #9ca3af;
}

.like-count,
.reply-count {
  font-size: 24rpx;
}

.like-text,
.reply-text {
  font-size: 24rpx;
}

/* Load More */
.load-more {
  text-align: center;
  padding: 32rpx 0;
  color: #3b82f6;
  font-size: 28rpx;
}

.no-more {
  text-align: center;
  padding: 32rpx 0;
  color: #9ca3af;
  font-size: 24rpx;
}

/* Review Preview Section */
.review-preview-section {
  background-color: #ffffff;
  /* margin: 0 24rpx 24rpx; */
  border-radius: 16rpx;
  padding: 24rpx;
}
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.more-link {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #3b82f6;
}

.arrow {
  margin-left: 8rpx;
  font-size: 28rpx;
}

.review-stats-summary {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 16rpx;
  background-color: #f8fafc;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}

.rating-summary {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.review-count {
  font-size: 24rpx;
  color: #6b7280;
}

.review-preview-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.review-preview-item {
  padding: 20rpx;
  background-color: #f9fafb;
  border-radius: 12rpx;
}

.review-preview-item .review-header {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
}

.review-preview-item .user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.review-preview-item .review-main {
  flex: 1;
}

.review-preview-item .user-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.review-preview-item .user-name {
  font-size: 24rpx;
  color: #374151;
  font-weight: 500;
}

.review-preview-item .rating-stars {
  margin-top: 4rpx;
}

.review-preview-item .stars {
  font-size: 20rpx;
  color: #f59e0b;
}

.review-preview-item .review-time {
  font-size: 20rpx;
  color: #9ca3af;
}

.review-preview-item .review-text {
  font-size: 24rpx;
  color: #4b5563;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.review-images-preview {
  display: flex;
  gap: 8rpx;
  margin-top: 12rpx;
}

.review-image-small {
  width: 60rpx;
  height: 60rpx;
  border-radius: 6rpx;
  background-color: #f3f4f6;
}

/* Product Detail Section */
.product-detail-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 20rpx;
}
