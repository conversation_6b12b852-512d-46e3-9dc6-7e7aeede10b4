/* pages/address-edit/address-edit.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  padding-top: 0;
}

/* Form Styles */
.address-form {
  padding: 24rpx;
  width: 96%;
}

.form-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f3f4f6;
}

.form-item {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  border-bottom: 1rpx solid #f9fafb;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 28rpx;
  color: #374151;
  width: 160rpx;
  flex-shrink: 0;
  line-height: 1.5;
  margin-top: 4rpx;
}

.form-input {
  flex: 1;
  font-size: 28rpx;
  color: #1f2937;
  padding: 0;
  margin: 0;
  line-height: 1.5;
}

.form-input::placeholder {
  color: #9ca3af;
}

.form-textarea {
  flex: 1;
  font-size: 28rpx;
  color: #1f2937;
  padding: 0;
  margin: 0;
  line-height: 1.5;
  min-height: 80rpx;
}

.form-textarea::placeholder {
  color: #9ca3af;
}

/* Region Picker */
.region-picker {
  flex: 1;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 0;
}

.picker-text {
  font-size: 28rpx;
  color: #1f2937;
  flex: 1;
}

.picker-placeholder {
  font-size: 28rpx;
  color: #9ca3af;
  flex: 1;
}

.picker-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* Switch Item */
.switch-item {
  align-items: center;
}

.default-switch {
  transform: scale(0.8);
}

/* Save Button */
.save-btn {
  position: fixed;
  bottom: 40rpx;
  left: 24rpx;
  right: 24rpx;
  background-color: #000000;
  color: #ffffff;
  padding: 32rpx;
  border-radius: 16rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.save-btn:active {
  background-color: #1f2937;
  transform: translateY(2rpx);
}
