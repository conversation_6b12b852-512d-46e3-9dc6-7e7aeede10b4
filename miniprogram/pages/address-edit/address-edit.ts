// pages/address-edit/address-edit.ts
import AddressService from '../../services/addressService'
import { AddressFormData } from '../../types/address'

Page({
  data: {
    isEdit: false,
    addressId: 0,
    formData: {
      recipientName: '',
      phoneNumber: '',
      regionProvince: '',
      regionCity: '',
      regionDistrict: '',
      streetAddress: '',
      postalCode: '',
      isDefault: false
    } as AddressFormData,
    selectedRegion: {
      province: '',
      city: '',
      district: ''
    },
    showRegionPicker: false
  },

  onLoad(options: any) {
    if (options.id) {
      // 编辑模式
      this.setData({
        isEdit: true,
        addressId: parseInt(options.id)
      })

      wx.setNavigationBarTitle({
        title: '编辑地址'
      })

      this.loadAddressData(parseInt(options.id))
    } else {
      // 新增模式
      wx.setNavigationBarTitle({
        title: '新增地址'
      })
    }
  },

  /**
   * 加载地址数据（编辑模式）
   */
  async loadAddressData(addressId: number) {
    wx.showLoading({
      title: '加载中...'
    })

    try {
      const address = await AddressService.getAddressById(addressId)
      wx.hideLoading()

      if (address) {
        this.setData({
          formData: {
            recipientName: address.recipientName,
            phoneNumber: address.phoneNumber,
            regionProvince: address.regionProvince,
            regionCity: address.regionCity,
            regionDistrict: address.regionDistrict,
            streetAddress: address.streetAddress,
            postalCode: address.postalCode || '',
            isDefault: address.isDefault
          },
          selectedRegion: {
            province: address.regionProvince,
            city: address.regionCity,
            district: address.regionDistrict
          }
        })
      } else {
        wx.showToast({
          title: '地址不存在',
          icon: 'none'
        })

        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      wx.hideLoading()
      console.error('加载地址数据失败:', error)

      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })

      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  /**
   * 输入框变化处理
   */
  onInputChange(e: any) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: value
    })
  },

  /**
   * 选择地区
   */
  onRegionChange(e: any) {
    const { value } = e.detail
    const [province, city, district] = value

    this.setData({
      'formData.regionProvince': province,
      'formData.regionCity': city,
      'formData.regionDistrict': district,
      selectedRegion: {
        province,
        city,
        district
      }
    })
  },

  /**
   * 默认地址开关变化
   */
  onDefaultChange(e: any) {
    const { value } = e.detail
    
    this.setData({
      'formData.isDefault': value
    })
  },

  /**
   * 保存地址
   */
  async saveAddress() {
    const { isEdit, addressId, formData } = this.data

    // 验证表单数据
    if (!this.validateForm()) {
      return
    }

    wx.showLoading({
      title: isEdit ? '更新中...' : '保存中...'
    })

    try {
      let result

      if (isEdit) {
        result = await AddressService.updateAddress(addressId, formData)
      } else {
        result = await AddressService.addAddress(formData)
      }

      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: result.message,
          icon: 'success'
        })

        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none',
          duration: 3000
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('保存地址失败:', error)

      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 验证表单数据
   */
  validateForm(): boolean {
    const { formData } = this.data

    if (!formData.recipientName.trim()) {
      wx.showToast({
        title: '请输入收货人姓名',
        icon: 'none'
      })
      return false
    }

    if (!formData.phoneNumber.trim()) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return false
    }

    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(formData.phoneNumber.trim())) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      })
      return false
    }

    if (!formData.regionProvince || !formData.regionCity || !formData.regionDistrict) {
      wx.showToast({
        title: '请选择所在地区',
        icon: 'none'
      })
      return false
    }

    if (!formData.streetAddress.trim()) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      })
      return false
    }

    return true
  },

  /**
   * 分享
   */
  onShareAppMessage() {
    return {
      title: '地址编辑',
      path: '/pages/address-edit/address-edit'
    }
  }
})
