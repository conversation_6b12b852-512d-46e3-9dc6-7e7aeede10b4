<!--pages/address-edit/address-edit.wxml-->
<view class="container">
  <form class="address-form">
    <!-- 收货人信息 -->
    <view class="form-section">
      <view class="section-title">收货人信息</view>
      
      <view class="form-item">
        <text class="form-label">收货人</text>
        <input
          class="form-input"
          type="text"
          placeholder="请输入收货人姓名"
          value="{{formData.recipientName}}"
          data-field="recipientName"
          bindinput="onInputChange"
          maxlength="20"
        />
      </view>

      <view class="form-item">
        <text class="form-label">手机号</text>
        <input
          class="form-input"
          type="number"
          placeholder="请输入手机号"
          value="{{formData.phoneNumber}}"
          data-field="phoneNumber"
          bindinput="onInputChange"
          maxlength="11"
        />
      </view>
    </view>

    <!-- 收货地址 -->
    <view class="form-section">
      <view class="section-title">收货地址</view>
      
      <view class="form-item">
        <text class="form-label">所在地区</text>
        <picker
          class="region-picker"
          mode="region"
          value="{{[formData.regionProvince, formData.regionCity, formData.regionDistrict]}}"
          bindchange="onRegionChange"
        >
          <view class="picker-content">
            <text wx:if="{{formData.regionProvince}}" class="picker-text">
              {{formData.regionProvince}} {{formData.regionCity}} {{formData.regionDistrict}}
            </text>
            <text wx:else class="picker-placeholder">请选择省市区</text>
            <image class="picker-arrow" src="/images/icons/arrow-right.png"></image>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">详细地址</text>
        <textarea
          class="form-textarea"
          placeholder="请输入详细地址，如街道、楼牌号等"
          value="{{formData.streetAddress}}"
          data-field="streetAddress"
          bindinput="onInputChange"
          maxlength="100"
          auto-height
        />
      </view>

      <view class="form-item">
        <text class="form-label">邮政编码</text>
        <input
          class="form-input"
          type="number"
          placeholder="请输入邮政编码（选填）"
          value="{{formData.postalCode}}"
          data-field="postalCode"
          bindinput="onInputChange"
          maxlength="6"
        />
      </view>
    </view>

    <!-- 默认地址设置 -->
    <view class="form-section">
      <view class="form-item switch-item">
        <text class="form-label">设为默认地址</text>
        <switch 
          class="default-switch"
          checked="{{formData.isDefault}}"
          bindchange="onDefaultChange"
          color="#000000"
        />
      </view>
    </view>
  </form>

  <!-- 保存按钮 -->
  <view class="save-btn" bindtap="saveAddress">
    <text>{{isEdit ? '更新地址' : '保存地址'}}</text>
  </view>
</view>
