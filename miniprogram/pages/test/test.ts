// pages/test/test.ts
// 测试页面，用于验证 wx.getSystemInfoSync 替换效果

import SystemInfoTest from '../../utils/systemInfoTest'
import SystemInfoManager from '../../utils/systemInfo'
import AuthManager from '../../utils/auth'

Page({
  data: {
    testResults: [] as Array<{ name: string; status: string; message: string }>,
    systemInfo: null as any,
    apiSupport: null as any,
    profileCapabilities: null as any
  },

  onLoad() {
    console.log('测试页面加载')
    this.runTests()
  },

  /**
   * 运行所有测试
   */
  async runTests() {
    const results: Array<{ name: string; status: string; message: string }> = []

    // 测试1: API支持情况
    try {
      const apiSupport = SystemInfoManager.checkNewAPISupport()
      this.setData({ apiSupport })
      
      const allSupported = Object.values(apiSupport).every(Boolean)
      results.push({
        name: 'API支持检测',
        status: allSupported ? 'success' : 'warning',
        message: allSupported ? '所有新API都支持' : '部分API不支持，将使用降级方案'
      })
    } catch (error) {
      results.push({
        name: 'API支持检测',
        status: 'error',
        message: `检测失败: ${error}`
      })
    }

    // 测试2: 系统信息获取
    try {
      const systemInfo = SystemInfoManager.getSystemInfo()
      this.setData({ systemInfo })
      
      results.push({
        name: '系统信息获取',
        status: 'success',
        message: `成功获取系统信息，SDK版本: ${systemInfo.SDKVersion}`
      })
    } catch (error) {
      results.push({
        name: '系统信息获取',
        status: 'error',
        message: `获取失败: ${error}`
      })
    }

    // 测试3: 头像昵称功能检测
    try {
      const profileCapabilities = AuthManager.checkNewProfileCapabilities()
      this.setData({ profileCapabilities })
      
      results.push({
        name: '头像昵称功能检测',
        status: profileCapabilities.canChooseAvatar ? 'success' : 'warning',
        message: `头像选择: ${profileCapabilities.canChooseAvatar ? '支持' : '不支持'}, 昵称编辑: ${profileCapabilities.canEditNickname ? '支持' : '不支持'}`
      })
    } catch (error) {
      results.push({
        name: '头像昵称功能检测',
        status: 'error',
        message: `检测失败: ${error}`
      })
    }

    // 测试4: 版本比较功能
    try {
      const version1 = '2.21.2'
      const version2 = '2.21.1'
      const compareResult = SystemInfoManager.compareVersion(version1, version2)
      
      results.push({
        name: '版本比较功能',
        status: compareResult === 1 ? 'success' : 'error',
        message: `${version1} vs ${version2} = ${compareResult} (期望: 1)`
      })
    } catch (error) {
      results.push({
        name: '版本比较功能',
        status: 'error',
        message: `比较失败: ${error}`
      })
    }

    // 测试5: 缓存功能
    try {
      SystemInfoManager.clearCache()
      
      const start1 = Date.now()
      SystemInfoManager.getSystemInfo()
      const time1 = Date.now() - start1
      
      const start2 = Date.now()
      SystemInfoManager.getSystemInfo()
      const time2 = Date.now() - start2
      
      const cacheWorking = time2 <= time1
      results.push({
        name: '缓存功能',
        status: cacheWorking ? 'success' : 'warning',
        message: `第一次: ${time1}ms, 第二次: ${time2}ms, 缓存${cacheWorking ? '生效' : '未生效'}`
      })
    } catch (error) {
      results.push({
        name: '缓存功能',
        status: 'error',
        message: `测试失败: ${error}`
      })
    }

    this.setData({ testResults: results })

    // 显示测试完成提示
    const successCount = results.filter(r => r.status === 'success').length
    const totalCount = results.length
    
    wx.showToast({
      title: `测试完成 ${successCount}/${totalCount}`,
      icon: successCount === totalCount ? 'success' : 'none',
      duration: 2000
    })
  },

  /**
   * 运行完整测试套件
   */
  runFullTests() {
    console.log('运行完整测试套件')
    SystemInfoTest.runAllTests()
    
    wx.showToast({
      title: '请查看控制台输出',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 清除缓存并重新测试
   */
  clearCacheAndRetest() {
    SystemInfoManager.clearCache()
    wx.showToast({
      title: '缓存已清除',
      icon: 'success',
      duration: 1000
    })
    
    setTimeout(() => {
      this.runTests()
    }, 1000)
  },

  /**
   * 复制测试结果
   */
  copyResults() {
    const results = this.data.testResults
    const text = results.map(r => `${r.name}: ${r.status} - ${r.message}`).join('\n')
    
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '结果已复制',
          icon: 'success'
        })
      }
    })
  }
})
