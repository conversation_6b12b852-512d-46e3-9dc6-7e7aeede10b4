/* pages/test/test.wxss */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  color: white;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.btn.primary {
  background: #007aff;
  color: white;
}

.btn.secondary {
  background: white;
  color: #007aff;
  border: 2rpx solid #007aff;
}

.btn:active {
  opacity: 0.8;
}

.results {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #e0e0e0;
}

.result-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.result-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item.success {
  border-left: 8rpx solid #4caf50;
}

.result-item.warning {
  border-left: 8rpx solid #ff9800;
}

.result-item.error {
  border-left: 8rpx solid #f44336;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.result-status {
  font-size: 32rpx;
  font-weight: bold;
}

.result-item.success .result-status {
  color: #4caf50;
}

.result-item.warning .result-status {
  color: #ff9800;
}

.result-item.error .result-status {
  color: #f44336;
}

.result-message {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.details {
  margin-bottom: 40rpx;
}

.detail-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.detail-value {
  font-size: 26rpx;
  color: #666;
}

.detail-value.supported {
  color: #4caf50;
  font-weight: bold;
}

.detail-value.unsupported {
  color: #f44336;
  font-weight: bold;
}

.footer {
  text-align: center;
  padding: 40rpx 20rpx;
  margin-top: 40rpx;
}

.footer-text {
  font-size: 24rpx;
  color: #999;
}
