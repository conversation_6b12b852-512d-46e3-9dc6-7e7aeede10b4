<!-- pages/test/test.wxml -->
<view class="container">
  <view class="header">
    <text class="title">系统信息API测试</text>
    <text class="subtitle">验证 wx.getSystemInfoSync 替换效果</text>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button class="btn primary" bindtap="runTests">重新测试</button>
    <button class="btn secondary" bindtap="runFullTests">完整测试</button>
    <button class="btn secondary" bindtap="clearCacheAndRetest">清除缓存</button>
    <button class="btn secondary" bindtap="copyResults">复制结果</button>
  </view>

  <!-- 测试结果 -->
  <view class="results">
    <view class="section-title">测试结果</view>
    <view class="result-list">
      <view 
        class="result-item {{item.status}}" 
        wx:for="{{testResults}}" 
        wx:key="name"
      >
        <view class="result-header">
          <text class="result-name">{{item.name}}</text>
          <text class="result-status">
            <text wx:if="{{item.status === 'success'}}">✓</text>
            <text wx:elif="{{item.status === 'warning'}}">⚠</text>
            <text wx:else>✗</text>
          </text>
        </view>
        <text class="result-message">{{item.message}}</text>
      </view>
    </view>
  </view>

  <!-- API支持情况 -->
  <view class="details" wx:if="{{apiSupport}}">
    <view class="section-title">API支持情况</view>
    <view class="detail-list">
      <view class="detail-item">
        <text class="detail-label">getWindowInfo:</text>
        <text class="detail-value {{apiSupport.getWindowInfo ? 'supported' : 'unsupported'}}">
          {{apiSupport.getWindowInfo ? '支持' : '不支持'}}
        </text>
      </view>
      <view class="detail-item">
        <text class="detail-label">getAppBaseInfo:</text>
        <text class="detail-value {{apiSupport.getAppBaseInfo ? 'supported' : 'unsupported'}}">
          {{apiSupport.getAppBaseInfo ? '支持' : '不支持'}}
        </text>
      </view>
      <view class="detail-item">
        <text class="detail-label">getDeviceInfo:</text>
        <text class="detail-value {{apiSupport.getDeviceInfo ? 'supported' : 'unsupported'}}">
          {{apiSupport.getDeviceInfo ? '支持' : '不支持'}}
        </text>
      </view>
      <view class="detail-item">
        <text class="detail-label">getSystemSetting:</text>
        <text class="detail-value {{apiSupport.getSystemSetting ? 'supported' : 'unsupported'}}">
          {{apiSupport.getSystemSetting ? '支持' : '不支持'}}
        </text>
      </view>
      <view class="detail-item">
        <text class="detail-label">getAppAuthorizeSetting:</text>
        <text class="detail-value {{apiSupport.getAppAuthorizeSetting ? 'supported' : 'unsupported'}}">
          {{apiSupport.getAppAuthorizeSetting ? '支持' : '不支持'}}
        </text>
      </view>
    </view>
  </view>

  <!-- 系统信息详情 -->
  <view class="details" wx:if="{{systemInfo}}">
    <view class="section-title">系统信息详情</view>
    <view class="detail-list">
      <view class="detail-item">
        <text class="detail-label">SDK版本:</text>
        <text class="detail-value">{{systemInfo.SDKVersion}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">平台:</text>
        <text class="detail-value">{{systemInfo.platform}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">设备品牌:</text>
        <text class="detail-value">{{systemInfo.brand}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">设备型号:</text>
        <text class="detail-value">{{systemInfo.model}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">窗口尺寸:</text>
        <text class="detail-value">{{systemInfo.windowWidth}} x {{systemInfo.windowHeight}}</text>
      </view>
      <view class="detail-item" wx:if="{{systemInfo.safeArea}}">
        <text class="detail-label">安全区域:</text>
        <text class="detail-value">{{systemInfo.safeArea.width}} x {{systemInfo.safeArea.height}}</text>
      </view>
    </view>
  </view>

  <!-- 头像昵称功能支持 -->
  <view class="details" wx:if="{{profileCapabilities}}">
    <view class="section-title">头像昵称功能</view>
    <view class="detail-list">
      <view class="detail-item">
        <text class="detail-label">头像选择:</text>
        <text class="detail-value {{profileCapabilities.canChooseAvatar ? 'supported' : 'unsupported'}}">
          {{profileCapabilities.canChooseAvatar ? '支持' : '不支持'}}
        </text>
      </view>
      <view class="detail-item">
        <text class="detail-label">昵称编辑:</text>
        <text class="detail-value {{profileCapabilities.canEditNickname ? 'supported' : 'unsupported'}}">
          {{profileCapabilities.canEditNickname ? '支持' : '不支持'}}
        </text>
      </view>
    </view>
  </view>

  <view class="footer">
    <text class="footer-text">检查控制台输出获取详细信息</text>
  </view>
</view>
