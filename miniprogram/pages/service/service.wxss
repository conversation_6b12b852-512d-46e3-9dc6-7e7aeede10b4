/* pages/service/service.wxss */
.container {
  padding-bottom: 32rpx;
  padding-top: 10rpx;
  width: 100%;
  background-color: #f5f5f5;
}

/* Service Header */
.service-header {
  background-color: #ffffff;
  padding: 32rpx 24rpx;
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  width: 100%;
  box-sizing: border-box;
}

.service-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.service-info {
  flex: 1;
}

.service-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #000000;
  display: block;
  margin-bottom: 8rpx;
}

.service-desc {
  font-size: 28rpx;
  color: #6b7280;
}

/* Contact Options */
.contact-options {
  padding: 0 24rpx;
  width: 100%;
  box-sizing: border-box;
}

.contact-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.contact-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.contact-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: 16rpx;
}

.contact-info {
  flex: 1;
}

.contact-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
  display: block;
  margin-bottom: 4rpx;
}

.contact-desc {
  font-size: 26rpx;
  color: #6b7280;
}

.contact-button {
  width: 100%;
  background-color: #000000;
  color: #ffffff;
  text-align: center;
  border-radius: 50rpx;
  padding: 16rpx;
  font-size: 28rpx;
  border: none;
  outline: none;
}

.contact-button::after {
  border: none;
}

/* FAQ Section */
.faq-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
  margin-bottom: 16rpx;
}

.faq-list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.faq-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-question {
  font-size: 28rpx;
  color: #374151;
  flex: 1;
}

.faq-arrow {
  font-size: 32rpx;
  color: #9ca3af;
  margin-left: 16rpx;
}

.faq-item:active {
  background-color: #f9fafb;
}

/* Contact Info Section */
.contact-info-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-top: 16rpx;
  width: 100%;
}

.method-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background-color: #f9fafb;
  border-radius: 12rpx;
  width: 100%;
  box-sizing: border-box;
}

.method-item:active {
  background-color: #f3f4f6;
}

.method-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}

.method-info {
  flex: 1;
}

.method-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #000000;
  display: block;
  margin-bottom: 4rpx;
}

.method-desc {
  font-size: 26rpx;
  color: #6b7280;
}
