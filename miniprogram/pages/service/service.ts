// pages/service/service.ts
Page({
  data: {
    messages: [],
    inputValue: '',
    scrollTop: 0,
    scrollIntoView: '',
    showQuickActions: true,
    userAvatar: '/images/default-avatar.png',
    currentTime: '',
    messageId: 1
  },

  onLoad() {
    this.setCurrentTime()
  },

  onShow() {
    this.setData({
      showQuickActions: this.data.messages.length === 0
    })
  },

  setCurrentTime() {
    const now = new Date()
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    this.setData({
      currentTime: time
    })
  },

  onInputChange(e: any) {
    this.setData({
      inputValue: e.detail.value
    })
  },

  sendMessage() {
    const { inputValue, messages, messageId } = this.data
    
    if (!inputValue.trim()) {
      return
    }

    // Add user message
    const userMessage = {
      id: messageId,
      type: 'user',
      content: inputValue.trim(),
      time: this.getCurrentTime()
    }

    const newMessages = [...messages, userMessage]
    
    this.setData({
      messages: newMessages,
      inputValue: '',
      messageId: messageId + 1,
      showQuickActions: false
    })

    // Scroll to bottom
    this.scrollToBottom()

    // Simulate service response
    setTimeout(() => {
      this.addServiceResponse(inputValue.trim())
    }, 1000)
  },

  sendQuickMessage(e: any) {
    const message = e.currentTarget.dataset.message
    this.setData({
      inputValue: message
    })
    this.sendMessage()
  },

  addServiceResponse(userMessage: string) {
    const { messages, messageId } = this.data
    
    let response = '感谢您的咨询，我们会尽快为您处理。如需更多帮助，请联系人工客服。'
    
    // Simple response logic based on user message
    if (userMessage.includes('订单')) {
      response = '您可以在"我的"页面查看订单状态，或者提供订单号让我帮您查询具体信息。'
    } else if (userMessage.includes('处方') || userMessage.includes('验光')) {
      response = '提交验光处方很简单：1. 进入订单详情 2. 点击"上传处方" 3. 拍照或选择处方照片 4. 确认提交。如有疑问可联系人工客服。'
    } else if (userMessage.includes('退换货')) {
      response = '我们支持7天无理由退换货（定制产品除外）。请确保商品完好无损，包装完整。具体流程请在订单页面申请售后。'
    } else if (userMessage.includes('人工客服')) {
      response = '人工客服服务时间：9:00-21:00。您可以拨打客服热线400-123-4567，或者点击下方"转人工客服"按钮。'
    }

    const serviceMessage = {
      id: messageId,
      type: 'service',
      content: response,
      time: this.getCurrentTime()
    }

    this.setData({
      messages: [...messages, serviceMessage],
      messageId: messageId + 1
    })

    this.scrollToBottom()
  },

  getCurrentTime() {
    const now = new Date()
    return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
  },

  scrollToBottom() {
    const { messages } = this.data
    if (messages.length > 0) {
      const lastMessageId = `msg-${messages[messages.length - 1].id}`
      this.setData({
        scrollIntoView: lastMessageId
      })
    }
  },

  onShareAppMessage() {
    return {
      title: '客服中心',
      path: '/pages/service/service'
    }
  }
})
