<!--pages/service/service.wxml-->
<view class="container">
  <!-- Service Header -->
  <view class="contact-options">
    <!-- 微信客服 -->
    <view class="contact-item">
      <view class="contact-header">
        <image class="contact-icon" src="/images/wechat-icon.png"></image>
        <view class="contact-info">
          <text class="contact-title">微信客服</text>
          <text class="contact-desc">点击直接与客服对话</text>
        </view>
      </view>
      <button
        class="contact-button"
        open-type="contact"
        session-from="miniprogram"
        send-message-title="眼镜商城咨询"
        send-message-path="/pages/home/<USER>"
        send-message-img="/images/service-avatar.png"
        show-message-card="true"
      >
        立即咨询
      </button>
    </view>

    <!-- 常见问题 -->
    <view class="faq-section">
      <view class="section-title">常见问题</view>
      <view class="faq-list">
        <view class="faq-item" bindtap="showFaqAnswer" data-question="如何查看订单状态？">
          <text class="faq-question">如何查看订单状态？</text>
          <text class="faq-arrow">></text>
        </view>
        <view class="faq-item" bindtap="showFaqAnswer" data-question="如何提交验光处方？">
          <text class="faq-question">如何提交验光处方？</text>
          <text class="faq-arrow">></text>
        </view>
        <view class="faq-item" bindtap="showFaqAnswer" data-question="退换货政策是什么？">
          <text class="faq-question">退换货政策是什么？</text>
          <text class="faq-arrow">></text>
        </view>
        <view class="faq-item" bindtap="showFaqAnswer" data-question="如何选择合适的镜片？">
          <text class="faq-question">如何选择合适的镜片？</text>
          <text class="faq-arrow">></text>
        </view>
        <view class="faq-item" bindtap="showFaqAnswer" data-question="配镜需要多长时间？">
          <text class="faq-question">配镜需要多长时间？</text>
          <text class="faq-arrow">></text>
        </view>
        <view class="faq-item" bindtap="showFaqAnswe1r" data-question="合作分销？">
          <text class="faq-question">合作分销？</text>
          <text class="faq-arrow">></text>
        </view>
        <view class="faq-item" bindtap="showFaqAnswe2r" data-question="找不到要的镜片/镜框？">
          <text class="faq-question">找不到要的镜片/镜框？</text>
          <text class="faq-arrow">></text>
        </view>
      </view>
    </view>
  </view>
</view>