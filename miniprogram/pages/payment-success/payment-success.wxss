/* pages/payment-success/payment-success.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
}

/* Loading */
.loading-container {
  text-align: center;
  
}

.loading-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #6b7280;
}

/* Success */
.success-container {
  text-align: center;
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 80rpx 48rpx;
  width: 100%;
  max-width: 600rpx;
}

.success-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.success-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #059669;
  margin-bottom: 16rpx;
}

.success-subtitle {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

/* Error */
.error-container {
  text-align: center;
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 80rpx 48rpx;
  width: 100%;
  max-width: 600rpx;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.error-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 16rpx;
}

.error-subtitle {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

/* Cancel */
.cancel-container {
  text-align: center;
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 80rpx 48rpx;
  width: 100%;
  max-width: 600rpx;
}

.cancel-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.cancel-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #f59e0b;
  margin-bottom: 16rpx;
}

.cancel-subtitle {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

/* Unknown */
.unknown-container {
  text-align: center;
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 80rpx 48rpx;
  width: 100%;
  max-width: 600rpx;
}

.unknown-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.unknown-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 16rpx;
}

.unknown-subtitle {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

/* Order Info */
.order-info {
  background-color: #f9fafb;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 48rpx;
  text-align: left;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #e5e7eb;
}

.order-item:last-child {
  border-bottom: none;
}

.order-label {
  font-size: 28rpx;
  color: #4b5563;
}

.order-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
}

.order-value.amount {
  color: #dc2626;
  font-weight: 600;
  font-size: 32rpx;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 24rpx;
}

.btn {
  flex: 1;
  padding: 24rpx 32rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
}

.btn-primary {
  background-color: #000000;
  color: #ffffff;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.btn:active {
  opacity: 0.8;
}
