// pages/payment-success/payment-success.ts
import PaymentService from '../../services/paymentService'
import { PaymentPageData, OrderInfo } from '../../types/payment'

Page({
  data: {
    orderInfo: null as OrderInfo | null,
    paymentParams: null,
    paymentStatus: 'checking',
    loading: true
  },

  onLoad(options: any) {
    const { orderNo } = options
    if (orderNo) {
      this.checkPaymentStatus(orderNo)
    } else {
      this.setData({
        paymentStatus: 'error',
        loading: false
      })
    }
  },

  /**
   * 检查支付状态
   */
  async checkPaymentStatus(orderNo: string) {
    try {
      // 查询支付状态详情
      const paymentStatus = await PaymentService.queryPaymentStatusDetail(orderNo)

      if (paymentStatus.status === 'SUCCESS') {
        // 支付成功，尝试获取订单详情
        try {
          // 从支付状态返回的orderNo中提取原始订单号
          // 支付状态返回的orderNo可能是商户订单号格式（如：20250902101652810600_69）
          // 需要提取原始订单号（如：20250902101652810600）
          const originalOrderNo = this.extractOriginalOrderNo(paymentStatus.orderNo || orderNo)
          console.log('提取原始订单号:', paymentStatus.orderNo, '->', originalOrderNo)

          const orderInfo = await PaymentService.getOrderByOrderNo(originalOrderNo)
          this.setData({
            orderInfo,
            paymentStatus: 'success',
            loading: false
          })
        } catch (orderError) {
          console.error('获取订单详情失败:', orderError)
          // 支付成功但获取订单详情失败，仍然显示支付成功，但不显示订单详情
          this.setData({
            orderInfo: null,
            paymentStatus: 'success',
            loading: false
          })
          wx.showToast({
            title: '订单详情获取失败',
            icon: 'none',
            duration: 2000
          })
        }
      } else {
        // 支付未成功
        this.setData({
          paymentStatus: paymentStatus.status.toLowerCase(),
          loading: false
        })
      }
    } catch (error) {
      console.error('查询支付状态失败:', error)
      this.setData({
        paymentStatus: 'error',
        loading: false
      })
    }
  },

  /**
   * 从商户订单号中提取原始订单号
   * 商户订单号格式：原订单号_支付记录ID（如：20250902101652810600_69）
   * 原始订单号格式：20250902101652810600
   */
  extractOriginalOrderNo(merchantOrderNo: string): string {
    if (!merchantOrderNo) {
      return merchantOrderNo
    }

    // 如果包含下划线，提取下划线前的部分作为原始订单号
    if (merchantOrderNo.includes('_')) {
      return merchantOrderNo.substring(0, merchantOrderNo.lastIndexOf('_'))
    }

    // 如果不包含下划线，说明已经是原始订单号
    return merchantOrderNo
  },

  /**
   * 继续购物
   */
  continueShopping() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },



  /**
   * 重新支付
   */
  async retryPayment() {
    const { orderInfo } = this.data
    if (!orderInfo) return

    try {
      this.setData({ loading: true })

      const paymentData = {
        orderNo: orderInfo.orderNumber,
        totalAmount: orderInfo.totalAmount,
        description: `订单${orderInfo.orderNumber}`,
        attach: JSON.stringify({
          retry: true,
          originalOrderNo: orderInfo.orderNumber
        })
      }

      await PaymentService.processPayment(paymentData)
    } catch (error: any) {
      console.error('重新支付失败:', error)
      if (error.message !== '用户取消支付') {
        wx.showToast({
          title: '支付失败，请重试',
          icon: 'error'
        })
      }
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 返回首页
   */
  goHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  /**
   * 分享
   */
  onShareAppMessage() {
    return {
      title: '我在眼镜商城购买了商品',
      path: '/pages/home/<USER>'
    }
  }
})
