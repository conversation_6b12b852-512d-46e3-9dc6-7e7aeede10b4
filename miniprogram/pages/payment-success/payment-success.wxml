<!--pages/payment-success/payment-success.wxml-->
<view class="container">
  <!-- Loading -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-icon">⏳</view>
    <view class="loading-text">正在确认支付结果...</view>
  </view>

  <!-- 支付成功 -->
  <view wx:elif="{{paymentStatus === 'success'}}" class="success-container">
    <view class="success-icon">✅</view>
    <view class="success-title">支付成功</view>
    <view class="success-subtitle">您的订单已提交，我们将尽快为您处理</view>

    <view class="action-buttons">
      <button class="btn btn-primary" bindtap="continueShopping">继续购物</button>
    </view>
  </view>

  <!-- 支付失败 -->
  <view wx:elif="{{paymentStatus === 'notpay' || paymentStatus === 'payerror'}}" class="error-container">
    <view class="error-icon">❌</view>
    <view class="error-title">支付失败</view>
    <view class="error-subtitle">
      <text wx:if="{{paymentStatus === 'notpay'}}">订单尚未支付，请重新支付</text>
      <text wx:else>支付过程中出现错误，请重试</text>
    </view>

    <view class="action-buttons">
      <button class="btn btn-secondary" bindtap="goHome">返回首页</button>
      <button class="btn btn-primary" bindtap="retryPayment">重新支付</button>
    </view>
  </view>

  <!-- 查询错误 -->
  <view wx:elif="{{paymentStatus === 'error'}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <view class="error-title">查询异常</view>
    <view class="error-subtitle">支付状态查询异常，请稍后重试</view>

    <view class="action-buttons">
      <button class="btn btn-primary" bindtap="goHome">返回首页</button>
    </view>
  </view>

  <!-- 支付取消 -->
  <view wx:elif="{{paymentStatus === 'closed'}}" class="cancel-container">
    <view class="cancel-icon">⚠️</view>
    <view class="cancel-title">订单已关闭</view>
    <view class="cancel-subtitle">该订单已被取消或关闭</view>

    <view class="action-buttons">
      <button class="btn btn-primary" bindtap="goHome">返回首页</button>
    </view>
  </view>

  <!-- 其他状态 -->
  <view wx:else class="unknown-container">
    <view class="unknown-icon">❓</view>
    <view class="unknown-title">状态未知</view>
    <view class="unknown-subtitle">无法确认支付状态，请稍后重试</view>

    <view class="action-buttons">
      <button class="btn btn-primary" bindtap="goHome">返回首页</button>
    </view>
  </view>
</view>