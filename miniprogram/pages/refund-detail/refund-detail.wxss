/* pages/refund-detail/refund-detail.wxss */
.refund-detail-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #999;
  font-size: 28rpx;
}

/* 内容区域 */
.content {
  padding: 20rpx;
}

/* 复制按钮样式 */
.value-with-copy {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.copy-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  background-color: #f5f5f5;
  border-radius: 6rpx;
  transition: background-color 0.2s;
}

.copy-btn:active {
  background-color: #e0e0e0;
}

.copy-icon {
  font-size: 20rpx;
}

/* 商品信息区域 */
.product-info-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.product-list {
  margin-top: 16rpx;
}

.product-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.product-item:last-child {
  margin-bottom: 0;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: 120rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.product-spec-container {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  margin-bottom: 8rpx;
}

.product-spec {
  font-size: 24rpx;
  color: #999;
  line-height: 1.3;
}

/* 商品右侧信息 */
.product-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  height: 120rpx;
  margin-left: 16rpx;
}

.product-total-price {
  font-size: 28rpx;
  color: #ff3b30;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.product-qty {
  font-size: 24rpx;
  color: #999;
}

/* 通用区块样式 */
.refund-info-section,
.description-section,
.refund-items-section,
.evidence-section,
.shipping-section,
.return-address-section,
.shipping-form-section,
.return-actions,
.product-info-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

/* 带状态的标题样式 */
.section-title-with-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title-with-status .section-title {
  margin-bottom: 0;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-title-with-status .status-text {
  font-size: 28rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  display: inline-block;
  margin-bottom: 0;
  background-color: #007aff !important;
  color: #fff !important;
}

.edit-shipping-btn {
  padding: 8rpx 16rpx;
  background-color: #007aff;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.edit-text {
  color: #fff;
  font-size: 24rpx;
  font-weight: normal;
}

/* 复制地址按钮样式 */
.copy-address-btn {
  padding: 8rpx 16rpx;
  background-color: #007aff;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.copy-btn-text {
  color: #fff;
  font-size: 24rpx;
  font-weight: normal;
}

/* 退款状态 */
.status-header {
  text-align: center;
  padding: 40rpx 0;
}

.status-text {
  font-size: 36rpx;
  font-weight: 600;
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  display: inline-block;
  margin-bottom: 16rpx;
}

.status-text.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-text.status-approved {
  background-color: #d4edda;
  color: #155724;
}

.status-text.status-pending-refund {
  background-color: #e2e3ff;
  color: #383d41;
}

.status-text.status-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.status-text.status-shipping {
  background-color: #cce5ff;
  color: #004085;
}

.status-text.status-received {
  background-color: #e2e3ff;
  color: #383d41;
}

.status-text.status-refunded {
  background-color: #d4edda;
  color: #155724;
}

.status-text.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

.refund-number {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.review-notes {
  margin-top: 24rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}

.notes-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.notes-text {
  font-size: 28rpx;
  color: #333;
  margin-left: 16rpx;
}

/* 信息列表 */
.info-list,
.shipping-info,
.address-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item,
.shipping-item,
.address-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child,
.shipping-item:last-child,
.address-item:last-child {
  border-bottom: none;
}

.label {
  color: #666;
  font-size: 28rpx;
  flex-shrink: 0;
  width: 160rpx;
}

.value {
  color: #333;
  font-size: 28rpx;
  flex: 1;
  text-align: right;
  word-break: break-all;
}

.value.amount {
  color: #ff3b30;
  font-weight: 600;
}

.value.notes {
  text-align: left;
  margin-left: 20rpx;
}

/* 带复制按钮的值容器 */
.value-with-copy {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12rpx;
  flex: 1;
}

.value-with-copy .value {
  text-align: right;
  flex: 1;
}

/* 复制按钮样式 */
.copy-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.copy-btn:active {
  background-color: #e0e0e0;
}

.copy-icon {
  font-size: 24rpx;
  color: #666;
}

/* 退款单号区域样式 */
.refund-number-section {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 退款说明 */
.description-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
}

/* 退款商品 */
.items-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.item-card {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
}

.item-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-quantity {
  font-size: 24rpx;
  color: #666;
}

.item-amount {
  font-size: 26rpx;
  color: #ff3b30;
  font-weight: 600;
}

/* 证据图片 */
.images-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.evidence-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
}

/* 表单 */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.form-input {
  padding: 24rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
}

.form-input:focus {
  border-color: #007aff;
  background-color: #fff;
}

.form-textarea {
  padding: 24rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
  min-height: 120rpx;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #007aff;
  background-color: #fff;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background-color: #f8f9fa;
  color: #666;
  border: 2rpx solid #ddd;
}

.submit-btn {
  background-color: #ff3b30;
  color: #fff;
}

.action-btn[disabled] {
  background-color: #ccc;
  color: #999;
}

/* 物流信息提示区域 */
.shipping-tip-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

.tip-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.tip-text {
  color: #666;
  font-size: 28rpx;
  text-align: center;
}

.tip-btn {
  background-color: #007aff;
  color: #fff;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 16rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 30rpx;
  font-size: 28rpx;
  border: none;
  background-color: #fff;
}

.modal-btn.cancel-btn {
  color: #666;
  border-right: 2rpx solid #f0f0f0;
}

.modal-btn.confirm-btn {
  color: #007aff;
  font-weight: 600;
}

/* 退货操作区域样式 */
.return-actions .action-buttons {
  padding: 20rpx 0;
}

.action-btn {
  width: 100%;
  padding: 24rpx;
  font-size: 32rpx;
  border-radius: 12rpx;
  border: none;
  font-weight: 500;
}

.action-btn.primary {
  background-color: #ff3b30;
  color: #fff;
}

.action-btn.primary:active {
  background-color: #d32f2f;
}


/* 选择器展示样式 */
.picker-display {
  padding: 24rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  background-color: #fafafa;
}
.picker-text {
  font-size: 28rpx;
  color: #666;
}
