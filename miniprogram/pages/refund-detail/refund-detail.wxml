<!--pages/refund-detail/refund-detail.wxml-->
<wxs module="utils">
  function formatAmount(amount) {
    if (!amount || isNaN(amount)) {
      return '0.00';
    }
    return (amount / 100).toFixed(2);
  }



  // 检查数组是否包含指定元素
  function arrayIncludes(arr, item) {
    if (!arr || !arr.length) return false;
    for (var i = 0; i < arr.length; i++) {
      if (arr[i] === item) {
        return true;
      }
    }
    return false;
  }

  // 分割属性字符串为数组
  function splitAttributes(attributes) {
    if (!attributes) return [];
    return attributes.split('，').filter(function(item) {
      return item && item.trim();
    });
  }

  module.exports = {
    formatAmount: formatAmount,
    arrayIncludes: arrayIncludes,
    splitAttributes: splitAttributes
  };
</wxs>

<view class="refund-detail-page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="content">
    <!-- 退款信息 -->
    <view class="refund-info-section">
      <view class="section-title-with-status">
        <text class="section-title">退款信息</text>
        <text class="status-text status-{{getStatusClass(refundDetail.status)}}">{{refundDetail.statusDesc}}</text>
      </view>
      <view class="info-list">
        <!-- 订单号作为第一行 -->
        <view class="info-item">
          <text class="label">订单号：</text>
          <view class="value-with-copy">
            <text class="value">{{refundDetail.orderNumber}}</text>
            <view class="copy-btn" data-order-number="{{refundDetail.orderNumber}}" catchtap="copyOrderNumber">
              <text class="copy-icon">📋</text>
            </view>
          </view>
        </view>
        <view class="info-item">
          <text class="label">退款类型：</text>
          <text class="value">{{refundDetail.refundTypeDesc}}</text>
        </view>
        <view class="info-item">
          <text class="label">退款原因：</text>
          <text class="value">{{refundDetail.refundReasonDesc}}</text>
        </view>
        <view class="info-item">
          <text class="label">退款金额：</text>
          <text class="value amount">¥{{utils.formatAmount(refundDetail.refundAmount)}}</text>
        </view>
        <view class="info-item">
          <text class="label">申请时间：</text>
          <text class="value">{{refundDetail.createdAt}}</text>
        </view>
        <view wx:if="{{refundDetail.reviewedAt}}" class="info-item">
          <text class="label">审核时间：</text>
          <text class="value">{{refundDetail.reviewedAt}}</text>
        </view>
      </view>

      <!-- 退款单号 -->
      <view class="refund-number-section">
        <view class="info-item">
          <text class="label">退款单号：</text>
          <view class="value-with-copy">
            <text class="value">{{refundDetail.refundNumber}}</text>
            <view class="copy-btn" bindtap="copyText" data-text="{{refundDetail.refundNumber}}" data-type="退款单号">
              <text class="copy-icon">📋</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="product-info-section">
      <view class="section-title">商品信息</view>
      <view class="product-list">
        <view wx:for="{{refundDetail.productDetails}}" wx:for-item="product" wx:key="orderItemId" class="product-item">
          <image class="product-image" src="{{product.productMainImageUrl || product.productImageUrl || '/images/placeholder.png'}}" mode="aspectFill"></image>
          <view class="product-info">
            <text class="product-name">{{product.productName}}</text>
            <view class="product-spec-container">
              <text wx:if="{{product.skuNameExtension}}" wx:for="{{utils.splitAttributes(product.skuNameExtension)}}" wx:key="*this" class="product-spec">{{item}}</text>
            </view>
          </view>
          <view class="product-right">
            <text class="product-total-price">¥{{utils.formatAmount(product.refundAmount)}}</text>
            <text class="product-qty">×{{product.refundQuantity}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 管理员审核意见 -->
    <view wx:if="{{refundDetail.adminReviewNotes}}" class="description-section">
      <view class="section-title">审核意见</view>
      <text class="description-text">{{refundDetail.adminReviewNotes}}</text>
    </view>


    <!-- 退款说明 -->
    <view wx:if="{{refundDetail.refundDescription}}" class="description-section">
      <view class="section-title">售后原因详细</view>
      <text class="description-text">{{refundDetail.refundDescription}}</text>
    </view>

    <!-- 退货操作区域 -->
    <view wx:if="{{refundDetail.refundType === 'return_refund' && refundDetail.status === 'approved' && utils.arrayIncludes(refundDetail.availableActions, 'submit_shipping') && returnAddress}}" class="return-actions">
      <view class="section-title">退货操作</view>
      <view class="action-buttons">
        <button class="action-btn primary" bindtap="showShippingModal">
          填写快递单号
        </button>
      </view>
    </view>



    <!-- 退款商品
    <view class="refund-items-section">
      <view class="section-title">退款商品</view>
      <view class="items-list">
        <view wx:for="{{refundDetail.refundItems}}" wx:key="orderItemId" class="item-card">
          <image class="item-image" src="{{item.productImageUrl || '/images/default-product.png'}}" mode="aspectFill"></image>
          <view class="item-details">
            <text class="item-name">{{item.productName}}</text>
            <view class="item-info">
              <text class="item-quantity">退款数量：{{item.refundQuantity}}</text>
              <text class="item-amount">¥{{utils.formatAmount(item.refundAmount)}}</text>
            </view>
          </view>
        </view>
      </view>
    </view> -->

    <!-- 证据图片
    <view wx:if="{{refundDetail.evidenceImages.length > 0}}" class="evidence-section">
      <view class="section-title">上传凭证</view>
      <view class="images-container">
        <image wx:for="{{refundDetail.evidenceImages}}" wx:key="index"
               class="evidence-image"
               src="{{item}}"
               mode="aspectFill"
               bindtap="previewEvidenceImage"
               data-index="{{index}}"></image>
      </view>
    </view> -->

    <!-- 物流信息 -->
    <view wx:if="{{refundDetail.shippingInfo}}" class="shipping-section">
      <view class="section-title">
        <text>物流信息</text>
        <!-- 根据可用操作判断是否显示编辑按钮 -->
        <view wx:if="{{utils.arrayIncludes(refundDetail.availableActions, 'edit_shipping')}}" class="edit-shipping-btn" bindtap="showEditShippingModal">
          <text class="edit-text">修改</text>
        </view>
      </view>
      <view class="shipping-info">
        <view class="shipping-item">
          <text class="label">物流公司：</text>
          <text class="value">{{refundDetail.shippingInfo.shippingCompany}}</text>
        </view>
        <view class="shipping-item">
          <text class="label">物流单号：</text>
          <text class="value" bindtap="copyText" data-text="{{refundDetail.shippingInfo.trackingNumber}}">
            {{refundDetail.shippingInfo.trackingNumber}}
          </text>
        </view>
        <view wx:if="{{refundDetail.shippingInfo.shippingNotes}}" class="shipping-item">
          <text class="label">物流备注：</text>
          <text class="value">{{refundDetail.shippingInfo.shippingNotes}}</text>
        </view>
        <view class="shipping-item">
          <text class="label">更新时间：</text>
          <text class="value">{{refundDetail.shippingInfo.shippedAt}}</text>
        </view>
      </view>
    </view>

    <!-- 退货地址（需要填写物流时显示） -->
    <view wx:if="{{utils.arrayIncludes(refundDetail.availableActions, 'submit_shipping') && returnAddress}}" class="return-address-section">
      <view class="section-title">
        <text>退货地址</text>
        <view class="copy-address-btn" bindtap="copyFullAddress">
          <text class="copy-btn-text">复制地址</text>
        </view>
      </view>
      <view class="address-info">
        <view class="address-item">
          <text class="label">收货人：</text>
          <text class="value">{{returnAddress.name}}</text>
        </view>
        <view class="address-item">
          <text class="label">联系电话：</text>
          <text class="value" bindtap="copyText" data-text="{{returnAddress.phone}}">{{returnAddress.phone}}</text>
        </view>
        <view class="address-item">
          <text class="label">收货地址：</text>
          <text class="value" bindtap="copyText" data-text="{{returnAddress.address}}">{{returnAddress.address}}</text>
        </view>
        <view class="address-item">
          <text class="label">邮政编码：</text>
          <text class="value">{{returnAddress.postalCode}}</text>
        </view>
        <view wx:if="{{returnAddress.notes}}" class="address-item">
          <text class="label">备注：</text>
          <text class="value notes">{{returnAddress.notes}}</text>
        </view>
      </view>
    </view>



    <!-- 填写物流信息提示 -->
    <view wx:if="{{utils.arrayIncludes(refundDetail.availableActions, 'submit_shipping') && returnAddress}}" class="shipping-tip-section">
      <view class="tip-content">
        <text class="tip-text">请将商品寄回到指定地址，并填写快递单号</text>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view wx:if="{{refundDetail && refundDetail.availableActions.length > 0}}" class="bottom-bar">
    <button wx:if="{{utils.arrayIncludes(refundDetail.availableActions, 'cancel')}}"
            class="action-btn cancel-btn"
            bindtap="cancelRefund">
      取消申请
    </button>

    <!-- 只有在没有显示退货操作区域时才显示填写快递单号按钮 -->
    <button wx:if="{{utils.arrayIncludes(refundDetail.availableActions, 'submit_shipping') && !(refundDetail.refundType === 'return_refund' && refundDetail.status === 'approved' && returnAddress)}}"
            class="action-btn submit-btn"
            bindtap="showShippingModal">
      填写快递单号
    </button>
  </view>

  <!-- 物流信息填写弹窗 -->
  <view wx:if="{{showShippingModal}}" class="modal-overlay" bindtap="hideShippingModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">填写快递单号</text>
        <text class="modal-close" bindtap="hideShippingModal">×</text>
      </view>

      <view class="modal-body">
        <view class="form-item">
          <text class="form-label">物流公司 *</text>
          <picker mode="selector" range="{{shippingCompanies}}" range-key="name" value="{{shippingCompanyIndex}}" bindchange="onShippingCompanyChange">
            <view class="picker-display">
              <text class="picker-text">{{ shippingCompanyIndex >= 0 ? shippingCompanies[shippingCompanyIndex].name : (shippingForm.shippingCompany || '请选择物流公司') }}</text>
            </view>
          </picker>
        </view>

        <view class="form-item">
          <text class="form-label">物流单号 *</text>
          <input class="form-input"
                 placeholder="请输入物流单号"
                 value="{{shippingForm.trackingNumber}}"
                 bindinput="onTrackingNumberInput" />
        </view>

        <view class="form-item">
          <text class="form-label">物流备注</text>
          <textarea class="form-textarea"
                    placeholder="请输入物流备注（选填）"
                    maxlength="500"
                    value="{{shippingForm.shippingNotes}}"
                    bindinput="onShippingNotesInput"></textarea>
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="hideShippingModal">取消</button>
        <button class="modal-btn confirm-btn" bindtap="submitShippingInfo">提交</button>
      </view>
    </view>
  </view>
</view>

<!-- 编辑物流信息弹窗 -->
<view wx:if="{{showEditShippingModal}}" class="modal-overlay" bindtap="hideEditShippingModal">
  <view class="modal-container" bindtap="stopPropagation">
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">修改物流信息</text>
        <view class="modal-close" bindtap="hideEditShippingModal">
          <text class="close-icon">×</text>
        </view>
      </view>

      <view class="modal-body">
        <view class="form-item">
          <text class="form-label">物流公司 *</text>
          <picker mode="selector" range="{{shippingCompanies}}" range-key="name" value="{{editShippingCompanyIndex}}" bindchange="onEditShippingCompanyChange">
            <view class="picker-display">
              <text class="picker-text">{{ editShippingCompanyIndex >= 0 ? shippingCompanies[editShippingCompanyIndex].name : (editShippingForm.shippingCompany || '请选择物流公司') }}</text>
            </view>
          </picker>
        </view>

        <view class="form-item">
          <text class="form-label">物流单号 *</text>
          <input class="form-input"
                 placeholder="请输入物流单号"
                 value="{{editShippingForm.trackingNumber}}"
                 bindinput="onEditTrackingNumberInput" />
        </view>

        <view class="form-item">
          <text class="form-label">物流备注</text>
          <textarea class="form-textarea"
                    placeholder="请输入物流备注（选填）"
                    maxlength="500"
                    value="{{editShippingForm.shippingNotes}}"
                    bindinput="onEditShippingNotesInput"></textarea>
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="hideEditShippingModal">取消</button>
        <button class="modal-btn confirm-btn" bindtap="submitEditShippingInfo">保存</button>
      </view>
    </view>
  </view>
</view>
