// pages/refund-detail/refund-detail.ts
import RefundService from '../../services/refundService'
import {
  RefundDetailPageData,
  RefundDetail,
  ReturnAddress,
  RefundStatus,
  RefundAction,
  RefundShippingRequest,
  UpdateRefundShippingRequest
} from '../../types/refund'

Page<RefundDetailPageData>({
  data: {
    loading: true,
    refundDetail: null,
    returnAddress: null,
    showShippingModal: false,
    showEditShippingModal: false,
    // 支持的物流公司列表（与服务端支持一致）
    shippingCompanies: [
      { code: 'SF', name: '顺丰速运' },
      { code: 'YTO', name: '圆通速递' },
      { code: 'ZTO', name: '中通快递' },
      { code: 'STO', name: '申通快递' },
      { code: 'YD', name: '韵达速递' },
      { code: 'JTSD', name: '极兔速递' },
      { code: 'JD', name: '京东物流' },
      { code: 'EMS', name: '邮政EMS' },
      { code: 'DBL', name: '德邦快递' },
      { code: 'HTKY', name: '百世快递' },
      { code: 'OTHER', name: '其他快递公司' }
    ],
    shippingCompanyIndex: -1,
    editShippingCompanyIndex: -1,
    shippingForm: {
      shippingCompany: '',
      trackingNumber: '',
      shippingNotes: ''
    },
    editShippingForm: {
      shippingCompany: '',
      trackingNumber: '',
      shippingNotes: ''
    },
    // 退款操作常量，供模板使用
    RefundAction: {
      CANCEL: RefundAction.CANCEL,
      SUBMIT_SHIPPING: RefundAction.SUBMIT_SHIPPING,
      EDIT_SHIPPING: RefundAction.EDIT_SHIPPING
    }
  },

  onLoad(options: any) {
    console.log('退款详情页面加载，参数:', options)

    const refundId = parseInt(options.refundId)
    if (!refundId) {
      wx.showToast({
        title: '退款ID无效',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.loadRefundDetail(refundId)
  },

  /**
   * 加载退款详情
   */
  async loadRefundDetail(refundId: number) {
    try {
      this.setData({ loading: true })

      const refundDetail = await RefundService.getRefundDetail(refundId)

      this.setData({
        refundDetail,
        loading: false
      })

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '退款详情'
      })

      // 如果需要填写物流信息，加载退货地址
      if (refundDetail.availableActions.includes('submit_shipping')) {
        this.loadReturnAddress()
      }

      // 调试日志
      console.log('退款详情加载完成:', {
        refundId,
        status: refundDetail.status,
        refundType: refundDetail.refundType,
        availableActions: refundDetail.availableActions
      })

    } catch (error: any) {
      console.error('加载退款详情失败:', error)
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'error'
      })
      this.setData({ loading: false })
    }
  },

  /**
   * 加载退货收货地址
   */
  async loadReturnAddress() {
    try {
      const returnAddress = await RefundService.getReturnAddress()
      this.setData({ returnAddress })
    } catch (error: any) {
      console.error('加载退货地址失败:', error)
    }
  },

  /**
   * 显示物流信息填写弹窗
   */
  showShippingModal() {
    this.setData({
      showShippingModal: true
    })
  },

  /**
   * 隐藏物流信息填写弹窗
   */
  hideShippingModal() {
    this.setData({
      showShippingModal: false
    })
  },

  /**
   * 显示编辑物流信息弹窗
   */
  showEditShippingModal() {
    const { refundDetail, shippingCompanies } = this.data
    if (!refundDetail || !refundDetail.shippingInfo) {
      wx.showToast({
        title: '没有物流信息',
        icon: 'none'
      })
      return
    }

    // 计算编辑选择器的初始索引
    const idx = shippingCompanies.findIndex((c: any) => c.name === refundDetail.shippingInfo!.shippingCompany)

    // 预填充当前物流信息
    this.setData({
      showEditShippingModal: true,
      editShippingCompanyIndex: idx,
      'editShippingForm.shippingCompany': refundDetail.shippingInfo.shippingCompany,
      'editShippingForm.trackingNumber': refundDetail.shippingInfo.trackingNumber,
      'editShippingForm.shippingNotes': refundDetail.shippingInfo.shippingNotes || ''
    })
  },

  /**
   * 隐藏编辑物流信息弹窗
   */
  hideEditShippingModal() {
    this.setData({
      showEditShippingModal: false
    })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
  },

  /**
   * 物流公司输入
   */
  onShippingCompanyInput(e: any) {
    this.setData({
      'shippingForm.shippingCompany': e.detail.value
    })
  },

  /**
   * 物流单号输入
   */
  onTrackingNumberInput(e: any) {
    this.setData({
      'shippingForm.trackingNumber': e.detail.value
    })
  },

  /**
   * 选择物流公司（新增提交）
   */
  onShippingCompanyChange(e: any) {
    const index = Number(e.detail.value)
    const company = this.data.shippingCompanies[index]
    this.setData({
      shippingCompanyIndex: index,
      'shippingForm.shippingCompany': company ? company.name : ''
    })
  },

  /**
   * 选择物流公司（编辑）
   */
  onEditShippingCompanyChange(e: any) {
    const index = Number(e.detail.value)
    const company = this.data.shippingCompanies[index]
    this.setData({
      editShippingCompanyIndex: index,
      'editShippingForm.shippingCompany': company ? company.name : ''
    })
  },


  /**
   * 物流备注输入
   */
  onShippingNotesInput(e: any) {
    this.setData({
      'shippingForm.shippingNotes': e.detail.value
    })
  },

  /**
   * 编辑物流公司输入
   */
  onEditShippingCompanyInput(e: any) {
    this.setData({
      'editShippingForm.shippingCompany': e.detail.value
    })
  },

  /**
   * 编辑物流单号输入
   */
  onEditTrackingNumberInput(e: any) {
    this.setData({
      'editShippingForm.trackingNumber': e.detail.value
    })
  },

  /**
   * 编辑物流备注输入
   */
  onEditShippingNotesInput(e: any) {
    this.setData({
      'editShippingForm.shippingNotes': e.detail.value
    })
  },

  /**
   * 提交物流信息
   */
  async submitShippingInfo() {
    try {
      const { refundDetail, shippingForm } = this.data

      if (!refundDetail) {
        wx.showToast({
          title: '退款信息不存在',
          icon: 'error'
        })
        return
      }

      // 验证表单
      if (!shippingForm.shippingCompany.trim()) {
        wx.showToast({
          title: '请输入物流公司',
          icon: 'none'
        })
        return
      }

      if (!shippingForm.trackingNumber.trim()) {
        wx.showToast({
          title: '请输入物流单号',
          icon: 'none'
        })
        return
      }

      wx.showLoading({ title: '提交中...' })

      const request: RefundShippingRequest = {
        refundRequestId: refundDetail.id,
        shippingCompany: shippingForm.shippingCompany.trim(),
        trackingNumber: shippingForm.trackingNumber.trim(),
        shippingNotes: shippingForm.shippingNotes.trim()
      }

      await RefundService.submitShippingInfo(request)

      wx.hideLoading()
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      })

      // 关闭弹窗并清空表单
      this.setData({
        showShippingModal: false,
        'shippingForm.shippingCompany': '',
        'shippingForm.trackingNumber': '',
        'shippingForm.shippingNotes': ''
      })

      // 重新加载详情
      setTimeout(() => {
        this.loadRefundDetail(refundDetail.id)
      }, 1500)

    } catch (error: any) {
      wx.hideLoading()
      console.error('提交物流信息失败:', error)
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'error'
      })
    }
  },

  /**
   * 提交修改物流信息
   */
  async submitEditShippingInfo() {
    try {
      const { refundDetail, editShippingForm } = this.data

      if (!refundDetail || !refundDetail.shippingInfo) {
        wx.showToast({
          title: '物流信息不存在',
          icon: 'error'
        })
        return
      }

      // 验证表单
      if (!editShippingForm.shippingCompany.trim()) {
        wx.showToast({
          title: '请输入物流公司',
          icon: 'none'
        })
        return
      }

      if (!editShippingForm.trackingNumber.trim()) {
        wx.showToast({
          title: '请输入物流单号',
          icon: 'none'
        })
        return
      }

      wx.showLoading({ title: '修改中...' })

      const request: UpdateRefundShippingRequest = {
        shippingCompany: editShippingForm.shippingCompany.trim(),
        trackingNumber: editShippingForm.trackingNumber.trim(),
        shippingNotes: editShippingForm.shippingNotes.trim()
      }

      await RefundService.updateShippingInfo(refundDetail.shippingInfo.id, request)

      wx.hideLoading()
      wx.showToast({
        title: '修改成功',
        icon: 'success'
      })

      // 关闭弹窗并清空表单
      this.setData({
        showEditShippingModal: false,
        'editShippingForm.shippingCompany': '',
        'editShippingForm.trackingNumber': '',
        'editShippingForm.shippingNotes': ''
      })

      // 重新加载详情
      setTimeout(() => {
        this.loadRefundDetail(refundDetail.id)
      }, 1500)

    } catch (error: any) {
      wx.hideLoading()
      console.error('修改物流信息失败:', error)
      wx.showToast({
        title: error.message || '修改失败',
        icon: 'error'
      })
    }
  },

  /**
   * 取消退款申请
   */
  async cancelRefund() {
    try {
      const { refundDetail } = this.data

      if (!refundDetail) return

      const result = await new Promise<boolean>((resolve) => {
        wx.showModal({
          title: '确认取消',
          content: `确定要取消退款申请 ${refundDetail.refundNumber} 吗？`,
          success: (res) => resolve(res.confirm),
          fail: () => resolve(false)
        })
      })

      if (!result) return

      wx.showLoading({ title: '取消中...' })

      await RefundService.cancelRefundRequest(refundDetail.id)

      wx.hideLoading()
      wx.showToast({
        title: '取消成功',
        icon: 'success'
      })

      // 重新加载详情
      setTimeout(() => {
        this.loadRefundDetail(refundDetail.id)
      }, 1500)

    } catch (error: any) {
      wx.hideLoading()
      console.error('取消退款申请失败:', error)
      wx.showToast({
        title: error.message || '取消失败',
        icon: 'error'
      })
    }
  },

  /**
   * 预览证据图片
   */
  previewEvidenceImage(e: any) {
    const index = e.currentTarget.dataset.index
    const { refundDetail } = this.data

    if (!refundDetail) return

    wx.previewImage({
      current: refundDetail.evidenceImages[index],
      urls: refundDetail.evidenceImages
    })
  },

  /**
   * 复制文本到剪贴板
   * 支持复制退款单号、地址等文本信息
   */
  copyText(e: any) {
    const text = e.currentTarget.dataset.text
    const type = e.currentTarget.dataset.type || '文本'

    if (!text) {
      wx.showToast({
        title: '复制内容为空',
        icon: 'error'
      })
      return
    }

    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: `${type}已复制`,
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    })
  },

  /**
   * 复制订单号
   * @param e 事件对象
   */
  copyOrderNumber(e: any) {
    const orderNumber = e.currentTarget.dataset.orderNumber
    if (!orderNumber) {
      wx.showToast({
        title: '订单号不存在',
        icon: 'none'
      })
      return
    }

    wx.setClipboardData({
      data: orderNumber,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success',
          duration: 1500
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 复制完整退货地址
   * 将收货人、联系电话、收货地址、邮政编码组合成完整地址进行复制
   */
  copyFullAddress() {
    const { returnAddress } = this.data

    if (!returnAddress) {
      wx.showToast({
        title: '地址信息不完整',
        icon: 'error'
      })
      return
    }

    // 组合完整地址信息
    const fullAddressText = `收货人：${returnAddress.name}\n联系电话：${returnAddress.phone}\n收货地址：${returnAddress.address}${returnAddress.postalCode ? `\n邮政编码：${returnAddress.postalCode}` : ''}${returnAddress.notes ? `\n备注：${returnAddress.notes}` : ''}`

    wx.setClipboardData({
      data: fullAddressText,
      success: () => {
        wx.showToast({
          title: '地址已复制',
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    })
  },

  /**
   * 格式化退款状态
   */
  formatStatus(status: RefundStatus): string {
    return RefundService.formatRefundStatus(status)
  },

  /**
   * 获取状态样式类名
   */
  getStatusClass(status: RefundStatus): string {
    const classMap: Record<RefundStatus, string> = {
      [RefundStatus.PENDING_REVIEW]: 'pending',
      [RefundStatus.APPROVED]: 'approved',
      [RefundStatus.PENDING_REFUND]: 'pending-refund',
      [RefundStatus.REJECTED]: 'rejected',
      [RefundStatus.USER_SHIPPING]: 'shipping',
      [RefundStatus.MERCHANT_RECEIVED]: 'pending-refund', // 使用与PENDING_REFUND相同的样式
      [RefundStatus.REFUNDED]: 'refunded',
      [RefundStatus.CANCELLED]: 'cancelled'
    }
    return classMap[status] || 'default'
  },

  /**
   * 获取可用操作
   */
  getAvailableActions(status: RefundStatus): string[] {
    return RefundService.getAvailableActions(status)
  }
})
