<!--pages/cart/cart.wxml-->
<view class="container">
  <!-- Cart Items -->
  <view class="cart-content">
    <view wx:if="{{cartItems.length === 0}}" class="empty-cart">
      <image class="empty-icon" src="/images/empty-cart.png"></image>
      <text class="empty-text">购物车空空如也</text>

      

      <view class="go-shopping-btn" bindtap="goShopping">
        去逛逛
      </view>
    </view>

    <view wx:else class="cart-items">
      <view class="cart-item" wx:for="{{cartItems}}" wx:key="id">
        <view class="item-checkbox" bindtap="toggleSelectItem" data-id="{{item.id}}">
          <view class="checkbox {{item.selected ? 'checked' : ''}}">
            <text wx:if="{{item.selected}}" class="checkmark">✓</text>
          </view>
        </view>

        <image class="item-image"
               src="{{item.image}}"
               mode="aspectFill"
               binderror="onImageError"
               data-id="{{item.id}}"
               lazy-load="true"></image>

        <view class="item-info">
          <view class="item-header">
            <text class="item-name">{{item.name}}</text>
            <view class="delete-btn" bindtap="removeItem" data-id="{{item.id}}">
              <image src="/images/delete.png"></image>
            </view>
          </view>

          <view wx:if="{{item.needPrescription}}" class="prescription-tag">
            需处方
          </view>

          <text class="item-sku">{{item.sku}}</text>

          <view class="item-footer">
            <text class="item-price">¥{{item.price}}</text>
            
            <view class="quantity-controls">
              <view class="quantity-btn" bindtap="updateQuantity" data-id="{{item.id}}" data-delta="-1">
                -
              </view>
              <text class="quantity">{{item.quantity}}</text>
              <view class="quantity-btn" bindtap="updateQuantity" data-id="{{item.id}}" data-delta="1">
                +
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- Bottom Checkout Bar -->
  <view wx:if="{{cartItems.length > 0}}" class="checkout-bar">
    <view class="select-all" bindtap="toggleSelectAll">
      <view class="checkbox {{allSelected ? 'checked' : ''}}">
        <text wx:if="{{allSelected}}" class="checkmark">✓</text>
      </view>
      <text class="select-all-text">全选</text>
    </view>

    <view class="checkout-info">
      <view class="total-section">
        <text class="total-label">合计:</text>
        <text class="total-price">¥{{totalPrice}}</text>
        <text class="shipping-note">不含运费</text>
      </view>

      <view class="checkout-btn {{selectedCount > 0 ? 'active' : ''}}" bindtap="checkout">
        结算({{selectedCount}})
      </view>
    </view>
  </view>
</view>
