// pages/cart/cart.ts
import LoginHelper from '../../utils/loginHelper'
import CartService from '../../services/cartService'
import { CartItem, CartPageData } from '../../types/cart'
import { CONFIG } from '../../utils/config'

Page({
  data: {
    cartItems: [] as CartItem[],
    allSelected: false,
    totalPrice: 0,
    selectedCount: 0,
    loading: false,
    refreshing: false
  } as CartPageData,

  onLoad() {
    this.loadCartData()
  },

  onShow() {
    this.loadCartData()
  },

  /**
   * 加载购物车数据
   */
  async loadCartData() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const cartItemDTOs = await CartService.getCartItems()
      console.log('购物车原始数据:', cartItemDTOs)

      const cartItemsConverted = CartService.convertDTOsToCartItems(cartItemDTOs)
      console.log('转换后的购物车数据:', cartItemsConverted)

      // 从本地和全局内存中恢复选中状态（默认不选中）
      const localSelected: { [id: number]: boolean } = wx.getStorageSync(CONFIG.STORAGE_KEYS.CART_SELECTED_MAP) || {}
      const app = getApp<IAppOption>()
      const memorySelected: { [id: number]: boolean } = app.globalData.cartSelectedMap || {}
      const selectedMap: { [id: number]: boolean } = { ...localSelected, ...memorySelected }
      console.log('选中映射（本地+内存合并）:', selectedMap)

      // 仅保留当前购物车中的键，避免脏数据
      const currentIds = new Set(cartItemsConverted.map(item => item.id))
      Object.keys(selectedMap).forEach(key => {
        const idNum = parseInt(key, 10)
        if (!currentIds.has(idNum)) delete selectedMap[idNum]
      })

      const cartItems = cartItemsConverted.map(item => ({
        ...item,
        selected: !!selectedMap[item.id]
      }))

      // 回写清理后的映射到内存和本地
      app.globalData.cartSelectedMap = selectedMap
      wx.setStorageSync(CONFIG.STORAGE_KEYS.CART_SELECTED_MAP, selectedMap)

      console.log('页面合成后的购物车数据(含selected):', cartItems)

      this.setData({
        cartItems,
        allSelected: cartItems.length > 0 && cartItems.every(item => item.selected)
      })

      this.calculateTotal()
    } catch (error) {
      console.error('加载购物车数据失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 下拉刷新
   */
  async onRefresh() {
    if (this.data.refreshing) return

    this.setData({ refreshing: true })

    try {
      await this.loadCartData()
    } finally {
      this.setData({ refreshing: false })
    }
  },

  toggleSelectAll() {
    console.log('toggleSelectAll tap, 当前allSelected:', this.data.allSelected, '当前items:', this.data.cartItems)
    const allSelected = !this.data.allSelected
    const cartItems = this.data.cartItems.map(item => ({
      ...item,
      selected: allSelected
    }))

    // 同步到内存与本地
    const app = getApp<IAppOption>()
    const selectedMap: { [id: number]: boolean } = app.globalData.cartSelectedMap || {}
    cartItems.forEach(item => {
      selectedMap[item.id] = item.selected
    })
    app.globalData.cartSelectedMap = selectedMap
    wx.setStorageSync(CONFIG.STORAGE_KEYS.CART_SELECTED_MAP, selectedMap)
    console.log('toggleSelectAll 后映射(已持久化):', selectedMap)

    this.setData({
      allSelected,
      cartItems
    })

    this.calculateTotal()
  },

  toggleSelectItem(e: any) {
    console.log('toggleSelectItem tap event:', e)
    console.log('toggleSelectItem dataset:', e.currentTarget && e.currentTarget.dataset)
    const idRaw = e.currentTarget.dataset.id
    const id: number = typeof idRaw === 'string' ? parseInt(idRaw, 10) : Number(idRaw)
    console.log('toggleSelectItem idRaw:', idRaw, 'parsed id:', id)

    const beforeItem = this.data.cartItems.find(i => i.id === id)
    console.log('toggleSelectItem beforeItem:', beforeItem)

    const cartItems = this.data.cartItems.map(item =>
      item.id === id ? { ...item, selected: !item.selected } : item
    )

    const afterItem = cartItems.find(i => i.id === id)
    console.log('toggleSelectItem afterItem:', afterItem)

    const allSelected = cartItems.every(item => item.selected)

    // 同步到内存与本地
    const app = getApp<IAppOption>()
    const selectedMap: { [id: number]: boolean } = app.globalData.cartSelectedMap || {}
    const changed = cartItems.find(item => item.id === id)
    if (changed) selectedMap[id] = !!changed.selected
    app.globalData.cartSelectedMap = selectedMap
    wx.setStorageSync(CONFIG.STORAGE_KEYS.CART_SELECTED_MAP, selectedMap)
    console.log('toggleSelectItem 后映射(已持久化):', selectedMap)

    this.setData({
      cartItems,
      allSelected
    })

    this.calculateTotal()
  },

  async updateQuantity(e: any) {
    const { id, delta } = e.currentTarget.dataset
    const cartItemId = parseInt(id)
    const deltaValue = parseInt(delta)

    // 找到对应的购物车项
    const targetItem = this.data.cartItems.find(item => item.id === cartItemId)
    if (!targetItem) return

    const newQuantity = Math.max(1, targetItem.quantity + deltaValue)

    // 如果数量没有变化，直接返回
    if (newQuantity === targetItem.quantity) return

    try {
      // 调用API更新数量
      await CartService.updateCartItem(cartItemId, { quantity: newQuantity })

      // 更新本地数据
      const cartItems = this.data.cartItems.map(item =>
        item.id === cartItemId ? { ...item, quantity: newQuantity } : item
      )

      this.setData({ cartItems })
      this.calculateTotal()

    } catch (error) {
      console.error('更新商品数量失败:', error)
      wx.showToast({
        title: '更新失败，请重试',
        icon: 'none'
      })
    }
  },

  async removeItem(e: any) {
    const idRaw = e.currentTarget.dataset.id
    const cartItemId: number = typeof idRaw === 'string' ? parseInt(idRaw, 10) : Number(idRaw)

    try {
      // 调用API删除购物车项
      await CartService.removeCartItem(cartItemId)

      // 更新本地数据
      const cartItems = this.data.cartItems.filter(item => item.id !== cartItemId)

      // 从内存中移除对应 id
      const app = getApp<IAppOption>()
      const selectedMap: { [id: number]: boolean } = app.globalData.cartSelectedMap || {}
      delete selectedMap[cartItemId]
      app.globalData.cartSelectedMap = selectedMap
      wx.setStorageSync(CONFIG.STORAGE_KEYS.CART_SELECTED_MAP, selectedMap)

      this.setData({ cartItems })
      this.calculateTotal()

      wx.showToast({
        title: '已移除商品',
        icon: 'success'
      })
    } catch (error) {
      console.error('删除商品失败:', error)
      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      })
    }
  },

  calculateTotal() {
    const { cartItems } = this.data
    const selectedItems = cartItems.filter(item => item.selected)
    // 使用Math.round避免浮点数精度问题
    const totalPrice = selectedItems.reduce((sum, item) => sum + Math.round(item.price * item.quantity * 100) / 100, 0)
    const selectedCount = selectedItems.reduce((sum, item) => sum + item.quantity, 0)

    this.setData({
      totalPrice,
      selectedCount
    })
  },

  checkout() {
    console.log('checkout tap, 当前selectedCount:', this.data.selectedCount, 'items:', this.data.cartItems)
    const { selectedCount, cartItems } = this.data
    const selectedItems = cartItems.filter(item => item.selected)

    if (selectedCount === 0) {
      wx.showToast({
        title: '请选择商品',
        icon: 'none'
      })
      return
    }

    // 检查选中商品是否都有库存
    const outOfStockItems = selectedItems.filter(item => !item.inStock || (item.stockQuantity && item.quantity > item.stockQuantity))

    if (outOfStockItems.length > 0) {
      wx.showToast({
        title: '部分商品库存不足，请调整数量',
        icon: 'none'
      })
      return
    }

    // 将选中商品的 skuId 保存到本地存储
    const selectedSkuIds = selectedItems
      .map(item => item.skuId || 0)
      .filter(id => !!id)
    console.log('checkout 选中skuIds:', selectedSkuIds)
    wx.setStorageSync(CONFIG.STORAGE_KEYS.CHECKOUT_SELECTED_SKU_IDS, selectedSkuIds)

    // 检查登录状态，未登录则提示登录
    LoginHelper.requireLogin(() => {
      // 登录成功后执行结算
      wx.navigateTo({
        url: '/pages/checkout/checkout?from=cart'
      })
    }, () => {
      // 用户取消登录
      console.log('用户取消登录，无法进行结算')
    })
  },

  /**
   * 批量删除选中的商品
   */
  async batchRemoveSelected() {
    const selectedItems = this.data.cartItems.filter(item => item.selected)

    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要删除的商品',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的${selectedItems.length}件商品吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            const cartItemIds = selectedItems.map(item => item.id)
            await CartService.batchRemoveCartItems(cartItemIds)

            // 更新本地数据
            const cartItems = this.data.cartItems.filter(item => !item.selected)

            // 从内存中移除对应 id
            const app = getApp<IAppOption>()
            const selectedMap: { [id: number]: boolean } = app.globalData.cartSelectedMap || {}
            cartItemIds.forEach(id => delete selectedMap[id])
            app.globalData.cartSelectedMap = selectedMap
            wx.setStorageSync(CONFIG.STORAGE_KEYS.CART_SELECTED_MAP, selectedMap)

            this.setData({ cartItems })
            this.calculateTotal()

            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
          } catch (error) {
            console.error('批量删除失败:', error)
            wx.showToast({
              title: '删除失败，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  /**
   * 清空购物车
   */
  async clearAllItems() {
    if (this.data.cartItems.length === 0) {
      wx.showToast({
        title: '购物车已经是空的',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '确认清空',
      content: '确定要清空购物车吗？此操作不可恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            await CartService.clearCart()

            this.setData({
              cartItems: [],
              allSelected: false,
              totalPrice: 0,
              selectedCount: 0
            })

            // 清空内存中的选中状态
            const app = getApp<IAppOption>()
            app.globalData.cartSelectedMap = {}
            wx.removeStorageSync(CONFIG.STORAGE_KEYS.CART_SELECTED_MAP)

            wx.showToast({
              title: '购物车已清空',
              icon: 'success'
            })
          } catch (error) {
            console.error('清空购物车失败:', error)
            wx.showToast({
              title: '清空失败，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  goShopping() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  

  onShareAppMessage() {
    return {
      title: '购物车',
      path: '/pages/cart/cart'
    }
  },

  /**
   * 处理图片加载错误
   */
  onImageError(e: any) {
    const idRaw = e.currentTarget.dataset.id
    const id = typeof idRaw === 'string' ? parseInt(idRaw, 10) : idRaw
    const cartItems = this.data.cartItems.map(item => {
      if (item.id === id) {
        return { ...item, image: '/images/default-avatar.png' }
      }
      return item
    })

    this.setData({ cartItems })
    console.warn('商品图片加载失败，已替换为默认图片', id)
  }
})
