/* pages/cart/cart.wxss */
.container {
  padding-top: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 128rpx;
}

/* Cart Content */
.cart-content {
  flex: 1;
  padding: 24rpx;
  overflow-y: auto;
  width: 96%;
}

/* Empty Cart */
.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 32rpx;
}

.go-shopping-btn {
  padding: 16rpx 48rpx;
  background-color: #000000;
  color: #ffffff;
  border-radius: 50rpx;
  font-size: 28rpx;
}

/* Cart Items */
.cart-items {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.cart-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: flex-start;
}

.item-checkbox {
  margin-right: 24rpx;
  align-self: center;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox.checked {
  border-color: #000000;
  background-color: #000000;
}

.checkmark {
  color: #ffffff;
  font-size: 24rpx;
}

.item-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
}

.item-info {
  flex: 1;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
}

.item-name {
  font-size: 28rpx;
  color: #000000;
  line-height: 1.4;
  flex: 1;
  margin-right: 16rpx;
}

.delete-btn {
  width: 32rpx;
  height: 32rpx;
}

.delete-btn image {
  width: 100%;
  height: 100%;
}

.prescription-tag {
  display: inline-block;
  background-color: #f3f4f6;
  color: #374151;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  margin: 8rpx 0;
}

.item-sku {
  font-size: 24rpx;
  color: #6b7280;
  margin: 8rpx 0;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}

.item-price {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
}

.quantity-controls {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 48rpx;
  height: 48rpx;
  border: 1rpx solid #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #374151;
}

.quantity-btn:first-child {
  border-radius: 8rpx 0 0 8rpx;
}

.quantity-btn:last-child {
  border-radius: 0 8rpx 8rpx 0;
}

.quantity {
  width: 64rpx;
  height: 48rpx;
  border-top: 1rpx solid #d1d5db;
  border-bottom: 1rpx solid #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

/* Checkout Bar */
.checkout-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 112rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e7eb;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  max-width: 750rpx;
  margin: 0 auto;
}

.select-all {
  display: flex;
  align-items: center;
}

.select-all-text {
  font-size: 28rpx;
  margin-left: 16rpx;
}

.checkout-info {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.total-section {
  margin-right: 32rpx;
}

.total-label {
  font-size: 28rpx;
}

.total-price {
  font-size: 36rpx;
  font-weight: 500;
  color: #000000;
  margin-left: 8rpx;
}

.shipping-note {
  font-size: 24rpx;
  color: #9ca3af;
  display: block;
}

.checkout-btn {
  padding: 16rpx 48rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #ffffff;
  background-color: #d1d5db;
}

.checkout-btn.active {
  background-color: #000000;
}


