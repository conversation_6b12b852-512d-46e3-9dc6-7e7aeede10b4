// pages/webview/webview.ts
Page({
  data: {
    url: '',
    title: ''
  },

  onLoad(options: any) {
    console.log('Webview page loaded with options:', options)
    
    const { url, title } = options
    
    if (url) {
      const decodedUrl = decodeURIComponent(url)
      const decodedTitle = title ? decodeURIComponent(title) : '详情'
      
      this.setData({
        url: decodedUrl,
        title: decodedTitle
      })
      
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: decodedTitle
      })
      
      console.log('Loading URL:', decodedUrl)
    } else {
      console.error('No URL provided')
      wx.showToast({
        title: '页面地址无效',
        icon: 'none'
      })
    }
  },

  onMessage(e: any) {
    console.log('Webview message:', e.detail.data)
  },

  onError(e: any) {
    console.error('Webview error:', e.detail)
    wx.showToast({
      title: '页面加载失败',
      icon: 'none'
    })
  }
})
