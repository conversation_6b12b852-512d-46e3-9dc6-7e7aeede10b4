/* pages/order-detail-test/order-detail-test.wxss */
.test-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

.loading {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
}

.content {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.data-container {
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  max-height: 800rpx;
  overflow-y: scroll;
}

.data-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1.5;
  word-break: break-all;
}

.copy-btn {
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.no-data {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
}
