<!--pages/order-detail-test/order-detail-test.wxml-->
<view class="test-page">
  <view class="header">
    <text class="title">订单详情接口测试</text>
    <text class="subtitle">订单ID: {{orderId}}</text>
  </view>

  <view wx:if="{{loading}}" class="loading">
    <text>正在加载...</text>
  </view>

  <view wx:else class="content">
    <view wx:if="{{rawData}}" class="data-section">
      <view class="section-title">接口返回数据:</view>
      <view class="data-container">
        <text class="data-text">{{rawData}}</text>
      </view>
      <button class="copy-btn" bindtap="copyData">复制数据</button>
    </view>

    <view wx:else class="no-data">
      <text>暂无数据</text>
    </view>
  </view>
</view>
