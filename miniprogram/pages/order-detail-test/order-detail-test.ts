// pages/order-detail-test/order-detail-test.ts
// 订单详情测试页面

import RequestManager from '../../utils/request'

Page({
  data: {
    orderId: 64,
    rawData: null,
    loading: false
  },

  onLoad() {
    this.testOrderDetailAPI()
  },

  async testOrderDetailAPI() {
    try {
      this.setData({ loading: true })
      
      console.log('开始测试订单详情接口...')
      
      // 直接调用接口
      const response = await RequestManager.get('/api/user/orders/64')
      
      console.log('接口原始响应:', response)
      
      this.setData({
        rawData: response,
        loading: false
      })
      
    } catch (error) {
      console.error('接口调用失败:', error)
      this.setData({ loading: false })
      
      wx.showToast({
        title: '接口调用失败',
        icon: 'error'
      })
    }
  },

  copyData() {
    if (this.data.rawData) {
      wx.setClipboardData({
        data: JSON.stringify(this.data.rawData, null, 2),
        success: () => {
          wx.showToast({
            title: '数据已复制',
            icon: 'success'
          })
        }
      })
    }
  }
})
