/* pages/coupon-select/coupon-select.wxss */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  padding-top: 20rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.coupon-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 120rpx;
  width: 98%;
}

/* 订单金额信息 */
.order-info {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-amount-label {
  font-size: 28rpx;
  color: #666;
}

.order-amount {
  font-size: 32rpx;
  color: #ff4444;
  font-weight: bold;
}

/* 优惠券区域 */
.coupon-section {
  margin-bottom: 20rpx;
}

.section-header {
  background: white;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.section-title {
  display: flex;
  align-items: center;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.title-count {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.select-all-btn {
  background: #ff4444;
  color: white;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  border: none;
}

.select-all-btn::after {
  border: none;
}

/* 优惠券列表 */
.coupon-list {
  background: white;
}

.coupon-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.coupon-item:last-child {
  border-bottom: none;
}

.coupon-item.selected {
  background-color: #fff5f5;
}

.coupon-item.disabled {
  opacity: 0.5;
}

.coupon-checkbox {
  margin-right: 20rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s;
}

.checkbox.checked {
  background-color: #ff4444;
  border-color: #ff4444;
}

.checkmark {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.coupon-info {
  flex: 1;
}

.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.coupon-amount {
  font-size: 36rpx;
  color: #ff4444;
  font-weight: bold;
}

.coupon-type {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.coupon-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.min-amount {
  font-size: 24rpx;
  color: #666;
}

.expiry {
  font-size: 24rpx;
  color: #999;
}

.coupon-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 无优惠券状态 */
.no-coupons {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;
  background: white;
  margin: 20rpx;
  border-radius: 10rpx;
}

.no-coupons-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.no-coupons-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

.selected-info {
  display: flex;
  flex-direction: column;
}

.selected-count {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.discount-amount {
  font-size: 24rpx;
  color: #ff4444;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  border: none;
}

.cancel-btn::after {
  border: none;
}

.confirm-btn {
  background: #ff4444;
  color: white;
  font-size: 28rpx;
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  border: none;
}

.confirm-btn::after {
  border: none;
}
