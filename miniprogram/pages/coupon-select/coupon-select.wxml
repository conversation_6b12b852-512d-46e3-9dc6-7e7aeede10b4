<!--pages/coupon-select/coupon-select.wxml-->
<view class="container">
  <!-- Loading -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <view wx:else class="coupon-content">
    <!-- 订单金额信息 -->
    <view class="order-info">
      <text class="order-amount-label">订单金额：</text>
      <text class="order-amount">¥{{orderAmountDisplay}}</text>
    </view>

    <!-- 邀请奖励优惠券区域 -->
    <view wx:if="{{invitationCoupons.length > 0}}" class="coupon-section">
      <view class="section-header">
        <view class="section-title">
          <text class="title-text">邀请奖励优惠券</text>
          <text class="title-count">({{invitationCoupons.length}}张)</text>
        </view>
        <view class="select-all-btn" bindtap="toggleInvitationCouponsAll" role="button">
          {{allInvitationSelected ? '取消全选' : '全选'}}
        </view>
      </view>
      
      <view class="coupon-list">
        <view
          class="coupon-item {{item.selected ? 'selected' : ''}} {{item.disabled ? 'disabled' : ''}}"
          wx:for="{{invitationCoupons}}"
          wx:key="coupon.id"
          data-coupon-id="{{item.coupon.id}}"
          bindtap="toggleCouponSelection"
        >
          <view class="coupon-checkbox">
            <view class="checkbox {{item.selected ? 'checked' : ''}}">
              <text wx:if="{{item.selected}}" class="checkmark">✓</text>
            </view>
          </view>
          
          <view class="coupon-info">
            <view class="coupon-header">
              <view class="coupon-amount">¥{{item.couponAmountDisplay}}</view>
              <view class="coupon-type">{{item.coupon.couponName}}</view>
            </view>
            
            <view class="coupon-details">
              <text wx:if="{{item.coupon.minOrderAmount}}" class="min-amount">
                满¥{{item.minOrderAmountDisplay}}可用
              </text>
              <text class="expiry">{{item.expiryDisplay}}</text>
            </view>
            
            <view wx:if="{{item.coupon.description}}" class="coupon-desc">
              {{item.coupon.description}}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 普通优惠券区域 -->
    <view wx:if="{{regularCoupons.length > 0}}" class="coupon-section">
      <view class="section-header">
        <view class="section-title">
          <text class="title-text">优惠券</text>
          <text class="title-count">({{regularCoupons.length}}张)</text>
        </view>
      </view>
      
      <view class="coupon-list">
        <view
          class="coupon-item {{item.selected ? 'selected' : ''}} {{item.disabled ? 'disabled' : ''}}"
          wx:for="{{regularCoupons}}"
          wx:key="coupon.id"
          data-coupon-id="{{item.coupon.id}}"
          bindtap="toggleCouponSelection"
        >
          <view class="coupon-checkbox">
            <view class="checkbox {{item.selected ? 'checked' : ''}}">
              <text wx:if="{{item.selected}}" class="checkmark">✓</text>
            </view>
          </view>
          
          <view class="coupon-info">
            <view class="coupon-header">
              <view class="coupon-amount">¥{{item.couponAmountDisplay}}</view>
              <view class="coupon-type">{{item.coupon.couponName}}</view>
            </view>
            
            <view class="coupon-details">
              <text wx:if="{{item.coupon.minOrderAmount}}" class="min-amount">
                满¥{{item.minOrderAmountDisplay}}可用
              </text>
              <text class="expiry">{{item.expiryDisplay}}</text>
            </view>
            
            <view wx:if="{{item.coupon.description}}" class="coupon-desc">
              {{item.coupon.description}}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 无可用优惠券 -->
    <view wx:if="{{availableCoupons.length === 0 && !loading}}" class="no-coupons">
      <view class="no-coupons-icon">🎫</view>
      <view class="no-coupons-text">暂无可用优惠券</view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <view class="selected-info">
      <text class="selected-count">已选{{selectedCoupons.length}}张</text>
      <text wx:if="{{selectedCoupons.length > 0}}" class="discount-amount">
        预计优惠¥{{previewTotalDiscountDisplay}}
      </text>
    </view>
    <view class="action-buttons">
      <view class="cancel-btn" bindtap="cancelSelection" role="button">取消</view>
      <view class="confirm-btn" bindtap="confirmSelection" role="button">确定</view>
    </view>
  </view>
</view>
