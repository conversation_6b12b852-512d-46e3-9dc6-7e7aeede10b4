// pages/coupon-select/coupon-select.ts
import CouponService from '../../services/couponService'
import {
  Coupon,
  CouponSelection,
  CouponSelectPageData,
  CouponSelectResult,
  PriceCalculation
} from '../../types/coupon'

// 扩展的优惠券选择状态
interface CouponSelectionExtended extends CouponSelection {
  disabled: boolean
  couponAmountDisplay: string
  minOrderAmountDisplay?: string
  expiryDisplay: string
}

Page({
  data: {
    availableCoupons: [] as CouponSelectionExtended[],
    selectedCoupons: [] as CouponSelectionExtended[],
    orderAmount: 0,
    orderAmountDisplay: '0.00',
    previewTotalDiscount: 0,
    previewTotalDiscountDisplay: '0.00',
    loading: false,
    invitationCoupons: [] as CouponSelectionExtended[],
    regularCoupons: [] as CouponSelectionExtended[],
    allInvitationSelected: false
  } as CouponSelectPageData & {
    availableCoupons: CouponSelectionExtended[],
    selectedCoupons: CouponSelectionExtended[],
    invitationCoupons: CouponSelectionExtended[],
    regularCoupons: CouponSelectionExtended[],
    allInvitationSelected: boolean,
    orderAmountDisplay: string,
    previewTotalDiscount: number,
    previewTotalDiscountDisplay: string
  },

  onLoad(options: any) {
    const { orderAmount, selectedCouponIds } = options
    
    const amountInFen = parseInt(orderAmount) || 0
    this.setData({
      orderAmount: amountInFen,
      orderAmountDisplay: CouponService.formatCouponAmount(amountInFen)
    })

    // 解析已选择的优惠券ID
    let preSelectedIds: number[] = []
    if (selectedCouponIds) {
      try {
        preSelectedIds = JSON.parse(selectedCouponIds)
      } catch (error) {
        console.error('解析已选优惠券ID失败:', error)
      }
    }

    this.loadCoupons(preSelectedIds)
  },

  /**
   * 加载优惠券列表
   */
  async loadCoupons(preSelectedIds: number[] = []) {
    this.setData({ loading: true })

    try {
      const coupons = await CouponService.getCouponsForOrder(this.data.orderAmount)
      
      // 分类优惠券
      const { invitationCoupons, regularCoupons } = CouponService.categorizeCoupons(coupons)
      
      // 创建选择状态对象
      const createSelections = (coupons: Coupon[]): CouponSelectionExtended[] => {
        return coupons.map(coupon => ({
          coupon,
          selected: preSelectedIds.includes(coupon.id),
          disabled: !CouponService.isCouponUsable(coupon, this.data.orderAmount),
          couponAmountDisplay: CouponService.formatCouponAmount(coupon.amount),
          minOrderAmountDisplay: coupon.minOrderAmount ? CouponService.formatCouponAmount(coupon.minOrderAmount) : undefined,
          expiryDisplay: this.getExpiryDisplay(coupon.expiresAt)
        }))
      }

      const invitationSelections = createSelections(invitationCoupons)
      const regularSelections = createSelections(regularCoupons)
      const allSelections = [...invitationSelections, ...regularSelections]

      const selected = allSelections.filter(item => item.selected)

      this.setData({
        availableCoupons: allSelections,
        invitationCoupons: invitationSelections,
        regularCoupons: regularSelections,
        selectedCoupons: selected,
        allInvitationSelected: invitationSelections.length > 0 && invitationSelections.every(item => item.selected),
        previewTotalDiscount: this.calculateDiscountedPrice().totalDiscount,
        previewTotalDiscountDisplay: CouponService.formatCouponAmount(this.calculateDiscountedPrice().totalDiscount)
      })

    } catch (error) {
      console.error('加载优惠券失败:', error)
      wx.showToast({
        title: '加载优惠券失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 预览底部预计优惠金额
   */
  previewDiscount() {
    const calculation = this.calculateDiscountedPrice()
    this.setData({
      previewTotalDiscount: calculation.totalDiscount,
      previewTotalDiscountDisplay: CouponService.formatCouponAmount(calculation.totalDiscount)
    })
  },

  /**
   * 切换单个优惠券选择状态
   * 要求：邀请奖励券分组内要么全选，要么都不选
   */
  toggleCouponSelection(e: any) {
    const { couponId } = e.currentTarget.dataset
    const { availableCoupons, invitationCoupons, regularCoupons } = this.data

    // 判断点击的是否为邀请奖励券分组中的券
    const isInvitation = invitationCoupons.some(item => item.coupon.id === couponId)

    if (isInvitation) {
      // 组切换：如果当前已全选，则改为全不选；否则全选
      const allInvitationSelected = invitationCoupons.every(item => item.selected)
      const newSelectedState = !allInvitationSelected

      const updatedInvitation = invitationCoupons.map(item => ({
        ...item,
        selected: newSelectedState
      }))

      // 同步更新可用券数组中的对应项
      const updatedAvailable = availableCoupons.map(item => {
        const inv = updatedInvitation.find(x => x.coupon.id === item.coupon.id)
        return inv || item
      })

      const updatedSelected = updatedAvailable.filter(item => item.selected)

      this.setData({
        availableCoupons: updatedAvailable,
        invitationCoupons: updatedInvitation,
        regularCoupons, // 普通券不变
        selectedCoupons: updatedSelected,
        allInvitationSelected: newSelectedState
      })

      // 预览底部预计优惠
      this.previewDiscount()
      return
      return
    }

    // 普通券仍按单张切换
    const update = (selections: CouponSelectionExtended[]): CouponSelectionExtended[] =>
      selections.map(item => (item.coupon.id === couponId ? { ...item, selected: !item.selected } : item))

    const updatedRegular = update(regularCoupons)
    const updatedAvailable = update(availableCoupons)

    const updatedSelected = updatedAvailable.filter(item => item.selected)

    this.setData({
      availableCoupons: updatedAvailable,
      invitationCoupons, // 邀请券不变
      regularCoupons: updatedRegular,
      selectedCoupons: updatedSelected,
      allInvitationSelected: invitationCoupons.length > 0 && invitationCoupons.every(item => item.selected)
    })

    // 预览底部预计优惠
    this.previewDiscount()
  },

  /**
   * 邀请奖励优惠券全选/取消全选
   */
  toggleInvitationCouponsAll() {
    const { invitationCoupons, availableCoupons, regularCoupons } = this.data
    
    // 检查是否已全选邀请奖励优惠券
    const allInvitationSelected = invitationCoupons.every(item => item.selected)
    const newSelectedState = !allInvitationSelected

    // 更新邀请奖励优惠券选择状态
    const updatedInvitation = invitationCoupons.map(item => ({
      ...item,
      selected: newSelectedState
    }))

    // 更新总的可用优惠券列表
    const updatedAvailable = availableCoupons.map(item => {
      const invitationItem = updatedInvitation.find(inv => inv.coupon.id === item.coupon.id)
      return invitationItem || item
    })

    const updatedSelected = updatedAvailable.filter(item => item.selected)

    this.setData({
      availableCoupons: updatedAvailable,
      invitationCoupons: updatedInvitation,
      selectedCoupons: updatedSelected,
      allInvitationSelected: newSelectedState
    })

    wx.showToast({
      title: newSelectedState ? '已全选邀请奖励优惠券' : '已取消全选',
      icon: 'success',
      duration: 1500
    })
  },

  /**
   * 计算优惠后价格
   */
  calculateDiscountedPrice(): PriceCalculation {
    const selectedCoupons = this.data.selectedCoupons.map(item => item.coupon)
    return CouponService.calculatePriceWithCoupons(this.data.orderAmount, selectedCoupons)
  },

  /**
   * 确认选择优惠券
   */
  confirmSelection() {
    const calculation = this.calculateDiscountedPrice()
    const selectedCoupons = this.data.selectedCoupons.map(item => item.coupon)

    const result: CouponSelectResult = {
      selectedCoupons,
      totalDiscount: calculation.totalDiscount,
      finalAmount: calculation.finalAmount
    }

    // 将结果存储到全局数据中
    const app = getApp<IAppOption>()
    app.globalData.selectedCoupons = result

    // 返回上一页
    wx.navigateBack()
  },

  /**
   * 取消选择，返回上一页
   */
  cancelSelection() {
    wx.navigateBack()
  },

  /**
   * 格式化金额显示
   */
  formatAmount(amount: number): string {
    return CouponService.formatCouponAmount(amount)
  },

  /**
   * 检查优惠券是否可用
   */
  isCouponUsable(coupon: Coupon): boolean {
    return CouponService.isCouponUsable(coupon, this.data.orderAmount)
  },

  /**
   * 获取优惠券到期时间显示
   */
  getExpiryDisplay(expiresAt: string): string {
    const expiryDate = new Date(expiresAt)
    const now = new Date()
    const diffTime = expiryDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays < 0) {
      return '已过期'
    } else if (diffDays === 0) {
      return '今天到期'
    } else if (diffDays === 1) {
      return '明天到期'
    } else if (diffDays <= 7) {
      return `${diffDays}天后到期`
    } else {
      return expiryDate.toLocaleDateString()
    }
  }
})
