// pages/products/products.ts
import ProductService from '../../services/productService'
import { ProductDTO, ProductItem } from '../../types/product'

Page({
  data: {
    loading: true,
    categoryId: 0,
    categoryName: '',
    keyword: '', // 搜索关键词
    searchResults: false, // 是否为搜索结果页面
    products: [] as ProductItem[],
    hasMore: true,
    page: 1,
    pageSize: 20,
    showEmptySearchMessage: false // 是否显示搜索为空的特定消息
  },

  onLoad(options: any) {
    console.log('Products page loaded with options:', options)

    // 获取页面参数
    const categoryId = parseInt(options.categoryId) || 0
    const categoryName = options.categoryName || '商品列表'
    const searchResults = options.searchResults === 'true'

    // 处理搜索关键词
    let keyword = ''
    if (searchResults) {
      // 从全局数据中获取搜索关键词，避免URL编码问题
      keyword = getApp().globalData.searchKeyword || ''
      console.log('从全局数据获取的keyword:', keyword)

      // 清除全局数据中的搜索关键词
      getApp().globalData.searchKeyword = ''
    } else {
      // 分类模式，从URL参数获取
      keyword = options.keyword || ''
      if (keyword.includes('%')) {
        try {
          keyword = decodeURIComponent(keyword)
        } catch (e) {
          console.error('URL解码失败:', e)
        }
      }
    }

    this.setData({
      categoryId,
      categoryName,
      keyword,
      searchResults
    })

    console.log('最终使用的keyword:', keyword)

    // 设置页面标题
    const title = searchResults ? `搜索"${keyword}"` : categoryName
    wx.setNavigationBarTitle({
      title: title
    })

    // 加载商品数据
    this.loadProductData()
  },

  /**
   * 加载商品数据
   */
  async loadProductData(refresh = false) {
    try {
      if (refresh) {
        this.setData({
          loading: true,
          page: 1,
          products: [],
          hasMore: true
        })
      } else {
        this.setData({ loading: true })
      }

      const { categoryId, keyword, searchResults, page, pageSize } = this.data
      let productList: ProductDTO[] = []
      let hasMore = true

      if (searchResults && keyword) {
        // 搜索模式
        console.log('开始调用搜索接口:', `/api/public/products/search?keyword=${keyword}`)
        const searchResult = await ProductService.searchProducts(keyword, {
          page,
          pageSize
        })
        productList = searchResult.records || []
        hasMore = searchResult.hasNext || false
      } else {
        // 分类模式
        console.log('开始调用商品接口:', `/api/public/categories/${categoryId}/products`)
        productList = await ProductService.getProductsByCategoryId(categoryId, {
          page,
          pageSize
        })
        hasMore = productList.length >= pageSize
      }

      console.log('获取到的商品数据:', productList)
      console.log('商品数据长度:', productList.length)

      // 转换数据格式
      const products = this.transformProductData(productList)

      // 更新数据
      const newProducts = refresh ? products : [...this.data.products, ...products]

      this.setData({
        products: newProducts,
        hasMore,
        page: refresh ? 2 : this.data.page + 1,
        loading: false
      })

      if (refresh) {
        wx.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 1500
        })
      }

      // 如果是搜索模式且没有结果，显示提示
      if (searchResults && keyword && newProducts.length === 0) {
        this.setData({ showEmptySearchMessage: true });
      } else {
        this.setData({ showEmptySearchMessage: false });
      }

    } catch (error) {
      console.error('加载商品数据失败:', error)
      this.setData({ loading: false })

      // 如果API调用失败，使用模拟数据
      this.loadMockData()

      wx.showToast({
        title: '使用本地数据',
        icon: 'none',
        duration: 2000
      })
    }
  },

  /**
   * 转换API数据为页面所需格式
   */
  transformProductData(productList: ProductDTO[]): ProductItem[] {
    const { searchResults } = this.data

    return productList.map(product => ({
      id: product.id,
      name: product.name,
      brandName: product.brandName,
      shortDescription: product.shortDescription,
      mainImageUrl: product.mainImageUrl || this.getDefaultProductImage(product.name),
      minPrice: product.minPrice || '0',
      maxPrice: product.maxPrice || '0',
      totalStock: product.totalStock,
      isFeatured: product.isFeatured,
      isSearchResult: searchResults // 标记是否为搜索结果
    }))
  },

  /**
   * 获取默认商品图片
   */
  getDefaultProductImage(productName: string): string {
    // 根据商品名称返回对应的默认图片
    if (productName.includes('蔡司') || productName.includes('镜片')) {
      return '/images/products/lens-default.png'
    } else if (productName.includes('镜框') || productName.includes('眼镜')) {
      return '/images/products/frame-default.png'
    }
    return '/images/products/product-default.png'
  },



  /**
   * 商品点击事件
   */
  onProductTap(e: any) {
    const product = e.currentTarget.dataset.product
    console.log('Product tapped:', product)
    
    // 跳转到商品详情页
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?productId=${product.id}&productName=${product.name}`
    })
  },



  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadProductData(true)
    wx.stopPullDownRefresh()
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadProductData()
    }
  },

  /**
   * 分享
   */
  onShareAppMessage() {
    const { categoryName } = this.data
    return {
      title: `${categoryName} - 专业眼镜品牌`,
      path: `/pages/products/products?categoryId=${this.data.categoryId}&categoryName=${categoryName}`
    }
  }
})
