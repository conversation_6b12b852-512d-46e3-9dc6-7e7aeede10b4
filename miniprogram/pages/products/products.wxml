<!--pages/products/products.wxml-->
<view class="container">

  <!-- Loading State -->
  <view wx:if="{{loading && products.length === 0}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- Product List -->
  <view wx:else class="product-list">
    <!-- Empty State -->
    <view wx:if="{{products.length === 0}}" class="empty-state">
      <!-- Custom empty search result message -->
      <view wx:if="{{showEmptySearchMessage}}" class="empty-search-contact">
        <text class="empty-text">搜不到想要的 镜片 / 镜框？</text>
        <button
          class="contact-button-style"
          open-type="contact"
          session-from="miniprogram"
          send-message-title="眼镜商城咨询"
          send-message-path="/pages/home/<USER>"
          send-message-img="/images/service-avatar.png"
          show-message-card="true"
        >
          <text class="empty-desc">
            <text class="red-text">点这里 </text>联系人工客服
          </text>
        </button>
      </view>

      <!-- Default empty state -->
      <view wx:else>
        <image class="empty-icon" src="/images/empty-product.png" mode="aspectFit"></image>
        <text class="empty-text">{{searchResults ? '未找到相关商品' : '暂无商品'}}</text>
        <text class="empty-desc">{{searchResults ? '试试其他关键词吧' : '该分类下还没有商品，去看看其他分类吧'}}</text>
      </view>
    </view>
    
    <!-- Product Grid -->
    <view wx:else class="product-grid">
      <view
        class="product-item"
        wx:for="{{products}}"
        wx:key="id"
        bindtap="onProductTap"
        data-product="{{item}}"
      >
        <view class="product-item-inner">
          <!-- Product Image -->
          <view class="product-image-container">
            <image class="product-image" src="{{item.mainImageUrl}}" mode="aspectFill"></image>
            <view wx:if="{{item.isFeatured}}" class="featured-badge">推荐</view>
          </view>

          <!-- Product Info -->
          <view class="product-info">
            <text class="product-name">{{item.name}}</text>
            <!-- 搜索结果页面不显示价格 -->
            <view wx:if="{{!searchResults}}" class="product-price">
              <text wx:if="{{item.minPrice === item.maxPrice}}" class="price">¥{{item.minPrice}}</text>
              <text wx:else class="price">¥{{item.minPrice}} - ¥{{item.maxPrice}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- More products contact -->
    <view wx:if="{{searchResults && products.length > 0}}" class="more-products-contact">
      <button
        class="contact-button-style"
        open-type="contact"
        session-from="miniprogram"
        send-message-title="眼镜商城咨询"
        send-message-path="/pages/home/<USER>"
        send-message-img="/images/service-avatar.png"
        show-message-card="true"
      >
        <text>
          更多镜片 / 镜框 <text class="red-text">点这里 ！</text>
        </text>
      </button>
    </view>
  </view>

  <!-- Load More -->
  <view wx:if="{{products.length > 0}}" class="load-more">
    <view wx:if="{{loading}}" class="loading-more">
      <view class="loading-spinner-small"></view>
      <text>加载中...</text>
    </view>
    <view wx:elif="{{hasMore}}" class="has-more">
      <text>上拉加载更多</text>
    </view>

  </view>
</view>
