/* pages/products/products.wxss */
.container {
  min-height: 100vh;
  background-color: #f9fafb;
  padding: 0;
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
}



/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  padding: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #6b7280;
}

/* Product List */
.product-list {
  width: 96%;
  flex: 1;
  padding: 12rpx;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  padding: 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.5;
}

.empty-text {
  margin-top: 20rpx;
  font-size: 32rpx;
  color: #6b7280;
  font-weight: 500;
}

.empty-desc {
  margin-top: 12rpx;
  font-size: 28rpx;
  color: #6b7280;
  text-align: center;
}

.red-text {
  color: red;
  font-size: 32rpx;
}

/* Product Grid */
.product-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx; /* 负边距抵消子元素的padding */
  width: 100%;
}

.product-item {
  width: 50%; /* 固定50%宽度确保每行2个 */
  padding: 0 8rpx; /* 左右padding创建间距 */
  margin-bottom: 16rpx; /* 下边距 */
  box-sizing: border-box; /* 确保padding不影响宽度计算 */
}

.product-item-inner {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #f3f4f6;
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Product Image */
.product-image-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%; /* 创建正方形容器 */
  overflow: hidden;
}

.product-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.featured-badge {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  background-color: #ef4444;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

/* Product Info */
.product-info {
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.product-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  min-height: 76rpx; /* 确保两行文字的最小高度 */
}

.product-price {
  display: flex;
  align-items: center;
}

.price {
  font-size: 32rpx;
  font-weight: 600;
  color: #ef4444;
}

/* 移除价格和库存相关样式 */

/* Load More */
.load-more {
  padding: 40rpx 12rpx;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #6b7280;
}

.loading-spinner-small {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #e5e7eb;
  border-top: 2rpx solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

.has-more {
  font-size: 24rpx;
  color: #6b7280;
}

.no-more {
  font-size: 24rpx;
  color: #9ca3af;
}

/* Empty search result custom message */
.empty-search-contact {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 40rpx;
}

.contact-button-style {
  background-color: transparent;
  border: none;
  padding: 0;
  margin: 24rpx 0 0 0;
  line-height: inherit;
  color: inherit;
  font-size: inherit;
  text-align: center;
  text-decoration: none;
}

.contact-button-style::after {
  border: none;
}

.more-products-contact {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #6b7280;
}
