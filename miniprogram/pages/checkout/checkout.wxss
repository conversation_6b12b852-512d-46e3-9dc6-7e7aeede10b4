/* pages/checkout/checkout.wxss */
.container {
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-top: 0%;
}

/* Loading */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* Content */
.checkout-content {
  padding: 24rpx 0;
  width: 100%;
}

/* 收货地址 */
.address-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin: 0 24rpx 24rpx 24rpx;
  overflow: hidden;
}

.address-card {
  padding: 32rpx;
}

.address-header {
  display: flex;
  align-items: flex-start;
}

.address-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.address-info {
  flex: 1;
}

.address-name-phone {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.recipient-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-right: 24rpx;
}

.recipient-phone {
  font-size: 28rpx;
  color: #6b7280;
}

.address-detail {
  font-size: 28rpx;
  color: #4b5563;
  line-height: 1.5;
}

.address-arrow {
  font-size: 28rpx;
  color: #9ca3af;
  margin-left: 16rpx;
}

.no-address {
  padding: 32rpx;
  display: flex;
  align-items: center;
}

.no-address-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.no-address-text {
  flex: 1;
  font-size: 32rpx;
  color: #6b7280;
}

/* 优惠券选择 */
.coupon-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin: 0 24rpx 24rpx 24rpx;
  overflow: hidden;
}

.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.coupon-title {
  display: flex;
  align-items: center;
}

.coupon-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.coupon-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.coupon-info {
  display: flex;
  align-items: center;
}

.coupon-placeholder {
  font-size: 28rpx;
  color: #999;
  margin-right: 8rpx;
}

.coupon-count {
  font-size: 28rpx;
  color: #ff4444;
  margin-right: 8rpx;
}

.coupon-arrow {
  font-size: 24rpx;
  color: #999;
}

.selected-coupons {
  padding: 0 32rpx 16rpx 32rpx;
}

.selected-coupon-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.selected-coupon-item:last-child {
  border-bottom: none;
}

.coupon-item-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-name {
  font-size: 28rpx;
  color: #333;
}

.coupon-amount {
  font-size: 28rpx;
  color: #ff4444;
  font-weight: 500;
}

.coupon-remove {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50%;
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

/* 商品列表 */
.goods-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin: 0 24rpx 24rpx 24rpx;
  overflow: hidden;
}

.section-title {
  padding: 32rpx 32rpx 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  border-bottom: 1rpx solid #f3f4f6;
}

.goods-list {
  padding: 0 32rpx 32rpx;
}

/* 商品样式已移至 product-item 组件 */

/* 订单备注 */
.remark-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin: 0 24rpx 24rpx 24rpx;
  overflow: hidden;
}

.remark-input {
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  min-height: 120rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 费用明细 */
.cost-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 0 24rpx 24rpx 24rpx;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.cost-label {
  font-size: 28rpx;
  color: #4b5563;
}

.cost-value {
  font-size: 28rpx;
  color: #1f2937;
}

.cost-divider {
  height: 1rpx;
  background-color: #e5e7eb;
  margin: 16rpx 0;
}

.cost-item.total {
  padding-top: 24rpx;
}

.cost-item.total .cost-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.cost-item.discount .cost-label {
  color: #ff4444;
}

.discount-amount {
  color: #ff4444;
  font-weight: 500;
}

.total-amount {
  font-size: 36rpx;
  font-weight: 600;
  color: #ef4444;
}

/* 底部提交栏 */
.submit-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e7eb;
  padding: 24rpx;
  display: flex;
  align-items: center;
  z-index: 100;
}

.submit-info {
  flex: 1;
  margin-right: 24rpx;
}

.submit-total {
  text-align: right;
}

.submit-discount {
  text-align: right;
  margin-top: 4rpx;
}

.discount-text {
  font-size: 24rpx;
  color: #ff4444;
}

.submit-label {
  font-size: 28rpx;
  color: #4b5563;
}

.submit-amount {
  font-size: 36rpx;
  font-weight: 600;
  color: #ef4444;
}

.submit-btn {
  background-color: #000000;
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  min-width: 200rpx;
}

.submit-btn.submitting {
  background-color: #9ca3af;
}

.submit-btn[disabled] {
  background-color: #9ca3af;
}
