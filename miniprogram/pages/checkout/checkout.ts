// pages/checkout/checkout.ts
import CartService from '../../services/cartService'
import PaymentService from '../../services/paymentService'
import AddressService from '../../services/addressService'
import CouponService from '../../services/couponService'
import { CartItem } from '../../types/cart'
import { CheckoutPageData, OrderItem, CreatePaymentRequest } from '../../types/payment'
import { Address } from '../../types/address'
import { Coupon, CouponSelectResult, PriceCalculation } from '../../types/coupon'
import { CONFIG } from '../../utils/config'

Page({
  data: {
    selectedItems: [] as OrderItem[],
    totalAmount: 0,
    totalQuantity: 0,
    selectedAddress: null as Address | null,
    loading: false,
    submitting: false,
    remark: '',
    from: '', // 来源：'buyNow' 表示立即购买，空字符串表示购物车结算
    // 优惠券相关
    selectedCoupons: [] as Coupon[],
    originalAmount: 0, // 原始金额（未使用优惠券）
    totalDiscount: 0, // 总优惠金额
    finalAmount: 0 // 最终支付金额
  } as CheckoutPageData & {
    remark: string,
    from: string,
    selectedCoupons: Coupon[],
    originalAmount: number,
    totalDiscount: number,
    finalAmount: number
  },

  // 将 selectedCoupons 注入各自的实际抵扣金额显示（按“面值从大到小叠加至0元”计算）
  applyPerCouponDiscountDisplays() {
    const { originalAmount, selectedCoupons } = this.data as any
    if (!selectedCoupons || selectedCoupons.length === 0) return

    const calculation = CouponService.calculatePriceWithCoupons(originalAmount, selectedCoupons)
    // 构建 couponCode -> discountAmount 映射
    const discountMap: Record<string, number> = {}
    calculation.usedCoupons.forEach(u => {
      discountMap[u.couponCode] = (discountMap[u.couponCode] || 0) + u.discountAmount
    })

    const updated = selectedCoupons.map((c: any) => ({
      ...c,
      amountDisplay: CouponService.formatCouponAmount(discountMap[c.couponCode] || 0)
    }))

    this.setData({ selectedCoupons: updated })
  },

  onLoad(options: any) {
    const { from } = options
    this.setData({ from })
    this.loadCheckoutData()
  },

  /**
   * 加载结算数据
   */
  async loadCheckoutData() {
    this.setData({ loading: true })

    try {
      let selectedItems: OrderItem[] = []

      // 检查是否是立即购买
      const app = getApp<IAppOption>()
      if (this.data.from === 'buyNow' && app.globalData.buyNowItem) {
        // 立即购买场景：使用全局数据中的商品信息
        selectedItems = [app.globalData.buyNowItem]

        // 清除全局数据
        delete app.globalData.buyNowItem
      } else {
        // 购物车结算场景：根据已选商品过滤
        const selectedSkuIds: number[] = wx.getStorageSync(CONFIG.STORAGE_KEYS.CHECKOUT_SELECTED_SKU_IDS) || []
        // 读取后立即清除，避免脏数据影响后续流程
        wx.removeStorageSync(CONFIG.STORAGE_KEYS.CHECKOUT_SELECTED_SKU_IDS)

        const cartSummary = await CartService.getCartSummary()

        const itemsToUse = cartSummary.items
          .filter(item => selectedSkuIds.length === 0 ? true : selectedSkuIds.includes(item.skuId))
          .filter(item => item.inStock)

        selectedItems = itemsToUse.map(item => ({
          skuId: item.skuId,
          skuCode: item.skuCode,
          productName: item.productName,
          skuAttributes: item.skuAttributes,
          unitPrice: PaymentService.yuanToFen(item.unitPrice),
          quantity: item.quantity,
          subtotal: PaymentService.yuanToFen(item.subtotal),
          productImageUrl: item.productImageUrl
        }))
      }

      const totalAmount = selectedItems.reduce((sum, item) => sum + item.subtotal, 0)
      const totalQuantity = selectedItems.reduce((sum, item) => sum + item.quantity, 0)

      // 获取默认地址
      const addresses = await AddressService.getAllAddresses()
      const defaultAddress = addresses.find(addr => addr.isDefault) || addresses[0] || null

      this.setData({
        selectedItems,
        totalAmount,
        totalQuantity,
        selectedAddress: defaultAddress,
        originalAmount: totalAmount,
        finalAmount: totalAmount
      })

      // 重新计算优惠后价格
      this.recalculatePrice()

    } catch (error) {
      console.error('加载结算数据失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 选择收货地址
   */
  selectAddress() {
    wx.navigateTo({
      url: '/pages/addresses/addresses?mode=select'
    })
  },

  /**
   * 输入备注
   */
  onRemarkInput(e: any) {
    this.setData({
      remark: e.detail.value
    })
  },

  /**
   * 选择优惠券
   */
  selectCoupons() {
    const { originalAmount, selectedCoupons } = this.data
    const selectedCouponIds = selectedCoupons.map(coupon => coupon.id)

    wx.navigateTo({
      url: `/pages/coupon-select/coupon-select?orderAmount=${originalAmount}&selectedCouponIds=${JSON.stringify(selectedCouponIds)}`
    })
  },

  /**
   * 重新计算价格（使用优惠券后）
   */
  recalculatePrice() {
    const { originalAmount, selectedCoupons } = this.data

    if (selectedCoupons.length === 0) {
      this.setData({
        totalDiscount: 0,
        finalAmount: originalAmount
      })
      return
    }

    const calculation = CouponService.calculatePriceWithCoupons(originalAmount, selectedCoupons)

    this.setData({
      totalDiscount: calculation.totalDiscount,
      finalAmount: calculation.finalAmount
    })
  },

  /**
   * 移除优惠券
   */
  removeCoupon(e: any) {
    const { couponId } = e.currentTarget.dataset
    const { selectedCoupons } = this.data

    const updatedCoupons = selectedCoupons.filter(coupon => coupon.id !== couponId)

    this.setData({
      selectedCoupons: updatedCoupons
    })

    // 更新总价与每券显示
    this.recalculatePrice()
    this.applyPerCouponDiscountDisplays()

    wx.showToast({
      title: '已移除优惠券',
      icon: 'success',
      duration: 1500
    })
  },

  /**
   * 格式化金额显示
   */
  formatAmount(amount: number): string {
    return CouponService.formatCouponAmount(amount)
  },

  /**
   * 提交订单并支付
   */
  async submitOrder() {
    const { selectedItems, selectedAddress, finalAmount, remark, submitting, selectedCoupons } = this.data

    if (submitting) return

    // 验证数据
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '没有可结算的商品',
        icon: 'none'
      })
      return
    }

    if (!selectedAddress) {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none'
      })
      return
    }

    this.setData({ submitting: true })

    try {
      // 1. 先创建订单
      // 处理备注信息，如果是模拟器则添加标识
      let processedRemark = remark || ''
      if (this.isWechatSimulator()) {
        processedRemark = processedRemark + '(微信模拟器下单标志)'
        console.log('检测到微信模拟器，添加标识后的备注:', processedRemark)
      }

      const orderData = {
        addressId: selectedAddress.id,
        items: selectedItems,
        totalAmount: finalAmount, // 使用优惠后的最终金额
        notesToSeller: processedRemark,  // 修正字段名，与后端保持一致
        coupons: selectedCoupons.map(coupon => ({
          couponId: coupon.id,
          couponCode: coupon.couponCode
        })) // 添加使用的优惠券信息
      }

      const orderInfo = await PaymentService.createOrder(orderData)

      // 调试日志：检查订单创建结果
      console.log('订单创建成功:', orderInfo)

      // 验证订单号是否存在
      if (!orderInfo || !orderInfo.orderNumber) {
        throw new Error('订单创建失败：未获取到订单号')
      }

      // 订单创建成功后，如果是购物车结算，删除购物车中对应的商品
      if (this.data.from !== 'buyNow') {
        try {
          const cartItemIds = selectedItems.map(item => item.skuId)
          console.log('准备删除购物车商品，SKU IDs:', cartItemIds)

          // 获取购物车汇总信息，找到对应的购物车项ID
          const cartSummary = await CartService.getCartSummary()
          const cartItemIdsToDelete = cartSummary.items
            .filter(cartItem => cartItemIds.includes(cartItem.skuId))
            .map(cartItem => cartItem.id)

          if (cartItemIdsToDelete.length > 0) {
            await CartService.batchRemoveCartItems(cartItemIdsToDelete)
            console.log('成功删除购物车商品:', cartItemIdsToDelete)
          }
        } catch (cartError) {
          // 删除购物车商品失败不影响订单创建和支付流程
          console.error('删除购物车商品失败:', cartError)
        }
      }

      // 2. 使用创建的订单号发起支付
      const paymentData: CreatePaymentRequest = {
        orderNo: orderInfo.orderNumber,
        totalAmount: finalAmount, // 使用优惠后的最终金额
        description: this.generateOrderDescription(),
        attach: JSON.stringify({
          orderId: orderInfo.id,
          addressId: selectedAddress.id,
          remark,
          coupons: selectedCoupons.map(coupon => coupon.id) // 记录使用的优惠券
        })
      }

      // 调试日志：检查支付数据
      console.log('支付数据:', paymentData)
      console.log('orderNo 类型:', typeof paymentData.orderNo)
      console.log('orderNo 值:', paymentData.orderNo)
      console.log('orderNo 长度:', paymentData.orderNo ? paymentData.orderNo.length : 'undefined')

      // 额外验证
      if (!paymentData.orderNo || paymentData.orderNo.trim() === '') {
        throw new Error('支付数据中订单号为空')
      }

      // 3. 执行支付流程
      await PaymentService.processPayment(paymentData)

    } catch (error: any) {
      console.error('提交订单失败:', error)

      // 根据错误类型显示不同的提示信息
      if (error.message === '用户取消支付') {
        wx.showToast({
          title: '支付已取消',
          icon: 'none'
        })
      } else if (error.message && error.message.includes('订单')) {
        wx.showToast({
          title: '订单创建失败，请重试',
          icon: 'error'
        })
      } else if (error.message && error.message.includes('支付')) {
        wx.showToast({
          title: '支付失败，请重试',
          icon: 'error'
        })
      } else {
        wx.showToast({
          title: error.message || '提交失败，请重试',
          icon: 'error'
        })
      }
    } finally {
      this.setData({ submitting: false })
    }
  },

  /**
   * 生成订单描述
   */
  generateOrderDescription(): string {
    const { selectedItems } = this.data

    if (selectedItems.length === 1) {
      return selectedItems[0].productName
    } else if (selectedItems.length <= 3) {
      return selectedItems.map(item => item.productName).join('、')
    } else {
      return `${selectedItems[0].productName}等${selectedItems.length}件商品`
    }
  },

  /**
   * 返回购物车
   */
  goBack() {
    wx.navigateBack()
  },

  /**
   * 页面显示时处理地址选择结果和优惠券选择结果
   */
  onShow() {
    // 检查是否有选择的地址
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]

    if (currentPage.data && currentPage.data.selectedAddressId) {
      this.loadSelectedAddress(currentPage.data.selectedAddressId)
      // 清除临时数据
      currentPage.setData({ selectedAddressId: null })
    }

    // 检查是否有选择的优惠券
    const app = getApp<IAppOption>()
    if (app.globalData.selectedCoupons) {
      const couponResult: CouponSelectResult = app.globalData.selectedCoupons

      this.setData({
        selectedCoupons: couponResult.selectedCoupons,
        totalDiscount: couponResult.totalDiscount,
        finalAmount: couponResult.finalAmount
      })

      // 为每张券注入实际抵扣显示
      this.applyPerCouponDiscountDisplays()

      // 清除全局数据
      delete app.globalData.selectedCoupons

      // 显示优惠券选择结果
      if (couponResult.selectedCoupons.length > 0) {
        wx.showToast({
          title: `已选择${couponResult.selectedCoupons.length}张优惠券`,
          icon: 'success',
          duration: 1500
        })
      }
    }
  },

  /**
   * 加载选中的地址
   */
  async loadSelectedAddress(addressId: number) {
    try {
      const addresses = await AddressService.getAllAddresses()
      const selectedAddress = addresses.find(addr => addr.id === addressId)

      if (selectedAddress) {
        this.setData({ selectedAddress })
      }
    } catch (error) {
      console.error('加载地址失败:', error)
    }
  },

  /**
   * 检测是否为微信模拟器
   */
  isWechatSimulator(): boolean {
    try {
      // 方法1: 通过系统信息检测
      const systemInfo = wx.getSystemInfoSync()

      console.log("isWechatSimulator", systemInfo)

      const isPCWeChat = systemInfo.platform === 'windows' || systemInfo.platform === 'mac' || systemInfo.platform === "devtools";
      const isMobileDevice = !isPCWeChat && (systemInfo.platform === 'ios' || systemInfo.platform === 'android');

      if (isPCWeChat) {
        // 模拟器环境特定代码
        return true;
      } else if (isMobileDevice) {
        // 真机环境特定代码
        return false;
      } else {
        return false;
      }

    } catch (error) {
      console.error('检测模拟器时发生错误:', error)
      return false
    }
  }
})
