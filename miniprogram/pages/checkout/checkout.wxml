<!--pages/checkout/checkout.wxml-->
<view class="container">
  <!-- Loading -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <view wx:else class="checkout-content">
    <!-- 收货地址 -->
    <view class="address-section">
      <view wx:if="{{selectedAddress}}" class="address-card" bindtap="selectAddress">
        <view class="address-header">
          <view class="address-icon">📍</view>
          <view class="address-info">
            <view class="address-name-phone">
              <text class="recipient-name">{{selectedAddress.recipientName}}</text>
              <text class="recipient-phone">{{selectedAddress.phoneNumber}}</text>
            </view>
            <view class="address-detail">
              {{selectedAddress.regionProvince}} {{selectedAddress.regionCity}} {{selectedAddress.regionDistrict}} {{selectedAddress.streetAddress}}
            </view>
          </view>
          <view class="address-arrow">></view>
        </view>
      </view>

      <view wx:else class="no-address" bindtap="selectAddress">
        <view class="no-address-icon">📍</view>
        <view class="no-address-text">请选择收货地址</view>
        <view class="address-arrow">></view>
      </view>
    </view>

    <!-- 商品列表 -->
    <view class="goods-section">
      <view class="section-title">商品清单</view>
      <view class="goods-list">
        <product-item
          wx:for="{{selectedItems}}"
          wx:key="skuId"
          product="{{item}}"
          mode="checkout"
        />
      </view>
    </view>

    <!-- 优惠券选择 -->
    <view class="coupon-section">
      <view class="coupon-header" bindtap="selectCoupons">
        <view class="coupon-title">
          <!-- <text class="coupon-icon">🎫</text> -->
          <text class="coupon-text">优惠券</text>
        </view>
        <view class="coupon-info">
          <text wx:if="{{selectedCoupons.length === 0}}" class="coupon-placeholder">选择优惠券</text>
          <text wx:else class="coupon-count">已选{{selectedCoupons.length}}张</text>
          <text class="coupon-arrow">></text>
        </view>
      </view>

      <!-- 已选优惠券列表 -->
      <view wx:if="{{selectedCoupons.length > 0}}" class="selected-coupons">
        <view
          class="selected-coupon-item"
          wx:for="{{selectedCoupons}}"
          wx:key="id"
        >
          <view class="coupon-item-info">
            <text class="coupon-name">{{item.couponName}}</text>
            <text class="coupon-amount">-¥{{item.amountDisplay}}</text>
          </view>
          <view class="coupon-remove" data-coupon-id="{{item.id}}" bindtap="removeCoupon">×</view>
        </view>
      </view>
    </view>

    <!-- 订单备注 -->
    <view class="remark-section">
      <view class="section-title">订单备注</view>
      <textarea
        class="remark-input"
        placeholder="选填，请输入订单备注信息"
        value="{{remark}}"
        bindinput="onRemarkInput"
        maxlength="200"
      ></textarea>
    </view>

    <!-- 费用明细 -->
    <view class="cost-section">
      <view class="cost-item">
        <text class="cost-label">商品金额</text>
        <text class="cost-value">¥{{originalAmount / 100}}</text>
      </view>
      <view class="cost-item">
        <text class="cost-label">运费</text>
        <text class="cost-value">免运费</text>
      </view>
      <view wx:if="{{totalDiscount > 0}}" class="cost-item discount">
        <text class="cost-label">优惠券优惠</text>
        <text class="cost-value discount-amount">-¥{{totalDiscount / 100}}</text>
      </view>
      <view class="cost-divider"></view>
      <view class="cost-item total">
        <text class="cost-label">实付款</text>
        <text class="cost-value total-amount">¥{{finalAmount / 100}}</text>
      </view>
    </view>
  </view>

  <!-- 底部提交栏 -->
  <view class="submit-bar">
    <view class="submit-info">
      <view class="submit-total">
        <text class="submit-label">实付：</text>
        <text class="submit-amount">¥{{finalAmount / 100}}</text>
      </view>
      <view wx:if="{{totalDiscount > 0}}" class="submit-discount">
        <text class="discount-text">已优惠¥{{totalDiscount / 100}}</text>
      </view>
    </view>
    <button
      class="submit-btn {{submitting ? 'submitting' : ''}}"
      bindtap="submitOrder"
      disabled="{{submitting}}"
    >
      {{submitting ? '提交中...' : '提交订单'}}
    </button>
  </view>
</view>
