// pages/invitation-cash-new/invitation-cash-new.ts
import AuthManager from '../../utils/auth'
import InvitationService, { InvitationCode, InvitationRecord, InvitationReward } from '../../services/invitationService'

interface InvitationRecordDisplay extends InvitationRecord {
  displayName: string
  formattedTime: string
  hasOrdered: boolean
  maskedPhone: string
}

interface InvitationStats {
  totalRewardAmount: number
  totalInvitedUsers: number
  totalOrderedUsers: number
  formattedTotalReward: string
}

Page({
  data: {
    // 页面模式：false=未填写邀请码模式，true=已填写邀请码模式
    hasUsedInvitationCode: false,
    
    // 我的邀请码
    myInvitationCode: null as InvitationCode | null,
    
    // 输入邀请码相关
    inputCode: '',
    submitting: false,
    
    // 总奖励金额（包含所有状态的奖励）
    totalRewardAmount: 0,
    formattedTotalReward: '0.00',

    // 不可用金额（待发放的奖励金额）
    unavailableAmount: 0,
    formattedUnavailableAmount: '0.00',
    
    // 邀请统计
    totalInvitedUsers: 0,
    totalOrderedUsers: 0,

    // 邀请记录列表
    invitationRecords: [] as InvitationRecordDisplay[],
    recordsLoading: false,
    hasMoreRecords: true,
    currentPage: 1,
    pageSize: 20,
    // 邀请记录总数（从接口获取的实际总数）
    totalRecords: 0,

    // 我的奖励列表
    myRewards: [] as InvitationReward[],
    rewardsLoading: false,
    hasMoreRewards: true,
    rewardsCurrentPage: 1,
    rewardsPageSize: 10,

    // 页面加载状态
    pageLoading: true
  },

  onLoad() {
    this.initializePage()
  },

  onShow() {
    // 页面显示时刷新数据
    if (AuthManager.isLoggedIn()) {
      this.refreshPageData()
    }
  },

  onPullDownRefresh() {
    this.refreshPageData()
  },

  onReachBottom() {
    // 加载更多邀请记录和奖励记录
    this.loadMoreInvitationRecords()
    this.loadMoreRewards()
  },

  /**
   * 初始化页面
   */
  async initializePage() {
    if (!AuthManager.isLoggedIn()) {
      this.showLoginModal()
      return
    }

    try {
      this.setData({ pageLoading: true })
      
      // 并行加载所有必要数据
      await Promise.all([
        this.loadMyInvitationCode(),
        this.checkInvitationCodeUsedStatus(),
        this.loadInvitationStats(),
        this.loadInvitationRecords(),
        this.loadMyRewards()
      ])
      
    } catch (error) {
      console.error('页面初始化失败:', error)
      wx.showToast({
        title: '页面加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ pageLoading: false })
    }
  },

  /**
   * 刷新页面数据
   */
  async refreshPageData() {
    try {
      await Promise.all([
        this.loadMyInvitationCode(),
        this.checkInvitationCodeUsedStatus(),
        this.loadInvitationStats(),
        this.loadInvitationRecords(true),
        this.loadMyRewards(true)
      ])
      
      wx.stopPullDownRefresh()
    } catch (error) {
      console.error('刷新数据失败:', error)
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 显示登录弹窗
   */
  showLoginModal() {
    wx.showModal({
      title: '登录提示',
      content: '请先登录以使用邀请功能',
      confirmText: '立即登录',
      showCancel: false,
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/login'
          })
        }
      }
    })
  },

  /**
   * 加载我的邀请码
   */
  async loadMyInvitationCode() {
    try {
      let codeData = await InvitationService.getMyInvitationCode()
      
      // 如果没有邀请码，自动生成一个
      if (!codeData) {
        codeData = await InvitationService.generateInvitationCode()
      }
      
      this.setData({
        myInvitationCode: codeData
      })
    } catch (error: any) {
      console.error('加载邀请码失败:', error)
      wx.showToast({
        title: '加载邀请码失败',
        icon: 'error'
      })
    }
  },

  /**
   * 检查用户是否已使用过邀请码
   */
  async checkInvitationCodeUsedStatus() {
    try {
      const hasUsed = await InvitationService.checkInvitationCodeUsedStatus()
      this.setData({
        hasUsedInvitationCode: hasUsed
      })
    } catch (error: any) {
      console.error('检查邀请码使用状态失败:', error)
      // 如果检查失败，默认显示输入框（保持原有行为）
      this.setData({
        hasUsedInvitationCode: false
      })
    }
  },

  /**
   * 加载邀请统计数据
   */
  async loadInvitationStats() {
    try {
      // 只加载邀请统计数据，总奖励金额由 loadMyRewards 方法处理
      const stats = await InvitationService.getInvitationStats()

      this.setData({
        totalInvitedUsers: stats.totalInvitedUsers || 0,
        totalOrderedUsers: stats.totalOrderedUsers || 0
      })
    } catch (error: any) {
      console.error('加载邀请统计失败:', error)
      // 统计数据加载失败不影响页面正常使用
    }
  },

  /**
   * 计算总奖励金额
   * 使用新的计算逻辑，包含所有状态的奖励
   */
  async calculateTotalRewardAmount(): Promise<number> {
    try {
      console.log('开始计算总奖励金额...')

      const { totalAmount } = await this.calculateRewardAmounts()

      console.log('计算总奖励金额完成:', {
        totalAmount,
        formattedAmount: this.formatAmount(totalAmount)
      })

      return totalAmount
    } catch (error: any) {
      console.error('计算总奖励金额失败:', error)
      wx.showToast({
        title: '计算奖励金额失败',
        icon: 'none',
        duration: 2000
      })
      return 0
    }
  },

  /**
   * 加载邀请记录
   */
  async loadInvitationRecords(isRefresh: boolean = false) {
    if (this.data.recordsLoading) return
    
    this.setData({ recordsLoading: true })

    try {
      const page = isRefresh ? 1 : this.data.currentPage
      const result = await InvitationService.getMyInvitationRecords(page, this.data.pageSize)
      
      const formattedRecords = this.formatInvitationRecords(result.records)
      
      this.setData({
        invitationRecords: isRefresh ? formattedRecords : [...this.data.invitationRecords, ...formattedRecords],
        hasMoreRecords: result.hasMore,
        currentPage: isRefresh ? 1 : page,
        // 更新总记录数，用于显示"共X人"
        totalRecords: result.total
      })
    } catch (error: any) {
      console.error('加载邀请记录失败:', error)
      if (!isRefresh) {
        wx.showToast({
          title: '加载邀请记录失败',
          icon: 'error'
        })
      }
    } finally {
      this.setData({ recordsLoading: false })
    }
  },

  /**
   * 加载更多邀请记录
   */
  async loadMoreInvitationRecords() {
    if (!this.data.hasMoreRecords || this.data.recordsLoading) return
    
    const nextPage = this.data.currentPage + 1
    this.setData({ currentPage: nextPage })
    await this.loadInvitationRecords()
  },

  /**
   * 格式化邀请记录
   */
  formatInvitationRecords(records: InvitationRecord[]): InvitationRecordDisplay[] {
    return records.map(record => ({
      ...record,
      // 使用接口返回的 inviteeNickname 字段作为用户昵称
      displayName: this.maskUserName(record.inviteeNickname || '用户'),
      formattedTime: this.formatTime(record.createdAt),
      // 使用接口返回的 inviteeHasOrdered 字段判断是否已下单
      hasOrdered: record.inviteeHasOrdered || false,
      maskedPhone: this.maskPhoneNumber(record.inviteePhone || '')
    }))
  },

  /**
   * 格式化金额
   * 后端返回的金额已经是以元为单位，直接格式化即可
   */
  formatAmount(amount: number): string {
    return amount.toFixed(2)
  },

  /**
   * 格式化过期时间显示
   */
  formatExpiresAt(expiresAt: string): string {
    if (!expiresAt) return ''

    try {
      const expireDate = new Date(expiresAt)
      const now = new Date()

      // 计算剩余天数
      const diffTime = expireDate.getTime() - now.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays < 0) {
        return '已过期'
      } else if (diffDays === 0) {
        return '今日过期'
      } else if (diffDays <= 7) {
        return `${diffDays}天后过期`
      } else {
        // 格式化为 YYYY-MM-DD
        const year = expireDate.getFullYear()
        const month = String(expireDate.getMonth() + 1).padStart(2, '0')
        const day = String(expireDate.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}过期`
      }
    } catch (error) {
      console.error('格式化过期时间失败:', error)
      return expiresAt.split('T')[0] + '过期'
    }
  },

  /**
   * 计算奖励金额统计
   * 需要获取所有奖励数据来计算总金额和不可用金额
   */
  async calculateRewardAmounts(): Promise<{totalAmount: number, unavailableAmount: number}> {
    try {
      // 获取所有奖励数据（使用较大的页面大小来获取所有数据）
      const result = await InvitationService.getMyInvitationRewards(1, 1000)
      const allRewards = result.rewards || []

      let totalAmount = 0
      let unavailableAmount = 0

      allRewards.forEach(reward => {
        const amount = reward.rewardAmount || 0
        totalAmount += amount

        // 如果是待发放状态，计入不可用金额
        if (reward.status === 'PENDING') {
          unavailableAmount += amount
        }
      })

      console.log('计算奖励金额统计完成:', {
        totalRewards: allRewards.length,
        totalAmount,
        unavailableAmount,
        availableAmount: totalAmount - unavailableAmount
      })

      return { totalAmount, unavailableAmount }
    } catch (error: any) {
      console.error('计算奖励金额统计失败:', error)
      return { totalAmount: 0, unavailableAmount: 0 }
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp: string | number): string {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    
    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`
    } else {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  },

  /**
   * 脱敏用户名
   */
  maskUserName(name: string): string {
    if (name.length <= 2) return name
    return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1)
  },

  /**
   * 脱敏手机号
   */
  maskPhoneNumber(phone: string): string {
    if (!phone || phone.length !== 11) return ''
    return phone.slice(0, 3) + '****' + phone.slice(7)
  },

  /**
   * 输入邀请码
   */
  onCodeInput(e: any) {
    const value = e.detail.value.toUpperCase()
    this.setData({
      inputCode: value
    })
  },

  /**
   * 提交邀请码
   */
  async submitInvitationCode() {
    const { inputCode, submitting, hasUsedInvitationCode } = this.data

    // 检查是否可以提交
    if (submitting || inputCode.length !== 4 || hasUsedInvitationCode) {
      return
    }

    this.setData({ submitting: true })

    try {
      const result = await InvitationService.useInvitationCode(inputCode)

      if (result && result.success !== false) {
        wx.showToast({
          title: '邀请码使用成功！获得20元现金优惠',
          icon: 'success',
          duration: 2000
        })

        // 切换到模式二
        this.setData({
          hasUsedInvitationCode: true,
          inputCode: ''
        })

        // 重新加载数据
        await this.refreshPageData()
      } else {
        const errorMessage = result?.message || '邀请码使用失败'
        wx.showToast({
          title: errorMessage,
          icon: 'error'
        })
      }
    } catch (error: any) {
      console.error('使用邀请码失败:', error)

      let errorMessage = '使用邀请码失败'
      if (error.message) {
        errorMessage = error.message
      }

      wx.showToast({
        title: errorMessage,
        icon: 'error'
      })
    } finally {
      this.setData({ submitting: false })
    }
  },

  /**
   * 复制我的邀请码
   */
  copyMyInvitationCode() {
    const { myInvitationCode } = this.data
    if (!myInvitationCode || !myInvitationCode.invitationCode) {
      wx.showToast({
        title: '邀请码不存在',
        icon: 'none'
      })
      return
    }

    const copyText = `使用我的邀请码 ${myInvitationCode.invitationCode}，双方都能领取现金奖励！`

    wx.setClipboardData({
      data: copyText,
      success: () => {
        wx.showToast({
          title: '已复制',
          icon: 'success'
        })
      },
      fail: (err) => {
        console.error('复制失败:', err)
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    })
  },

  /**
   * 加载我的奖励列表
   */
  async loadMyRewards(refresh: boolean = false) {
    try {
      if (refresh) {
        this.setData({
          rewardsCurrentPage: 1,
          myRewards: [],
          hasMoreRewards: true
        })
      }

      this.setData({ rewardsLoading: true })

      const page = refresh ? 1 : this.data.rewardsCurrentPage
      const result = await InvitationService.getMyInvitationRewards(page, this.data.rewardsPageSize)

      const newRewards = result.rewards || []
      const currentRewards = refresh ? [] : this.data.myRewards

      // 处理奖励数据，添加格式化的过期时间
      const processedNewRewards = newRewards.map(reward => ({
        ...reward,
        formattedExpiresAt: reward.expiresAt ? this.formatExpiresAt(reward.expiresAt) : ''
      }))

      // 更新数据，包括总奖励金额
      const updateData: any = {
        myRewards: [...currentRewards, ...processedNewRewards],
        hasMoreRewards: result.hasMore,
        rewardsCurrentPage: page,
        rewardsLoading: false
      }

      // 如果是刷新或第一次加载，重新计算奖励金额统计
      if (refresh || page === 1) {
        const { totalAmount, unavailableAmount } = await this.calculateRewardAmounts()
        updateData.totalRewardAmount = totalAmount
        updateData.formattedTotalReward = this.formatAmount(totalAmount)
        updateData.unavailableAmount = unavailableAmount
        updateData.formattedUnavailableAmount = this.formatAmount(unavailableAmount)
      }

      this.setData(updateData)

      console.log('加载我的奖励列表完成:', {
        page,
        newCount: newRewards.length,
        totalCount: this.data.myRewards.length,
        hasMore: result.hasMore,
        totalRewardAmount: this.data.totalRewardAmount,
        unavailableAmount: this.data.unavailableAmount
      })

    } catch (error: any) {
      console.error('加载我的奖励列表失败:', error)
      this.setData({ rewardsLoading: false })
      wx.showToast({
        title: '加载奖励列表失败',
        icon: 'none',
        duration: 2000
      })
    }
  },

  /**
   * 加载更多奖励
   */
  async loadMoreRewards() {
    if (this.data.rewardsLoading || !this.data.hasMoreRewards) {
      return
    }

    const nextPage = this.data.rewardsCurrentPage + 1
    this.setData({ rewardsCurrentPage: nextPage })
    await this.loadMyRewards()
  },

  /**
   * 格式化奖励状态
   */
  formatRewardStatus(status: string): string {
    const statusMap: Record<string, string> = {
      'PENDING': '待发放',
      'ISSUED': '已发放',
      'USED': '已使用',
      'EXPIRED': '已过期',
      'CANCELLED': '已取消'
    }
    return statusMap[status] || status
  },

  /**
   * 格式化奖励类型
   */
  formatRewardType(type: string): string {
    const typeMap: Record<string, string> = {
      'REGISTER': '注册奖励',
      'FIRST_ORDER': '首单奖励',
      'CONTINUOUS_ORDER': '连续订单奖励'
    }
    return typeMap[type] || type
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    const { myInvitationCode } = this.data
    if (myInvitationCode) {
      const shareContent = InvitationService.generateInviteShareContent(myInvitationCode.invitationCode)
      return {
        title: shareContent.title,
        path: shareContent.path,
        imageUrl: shareContent.imageUrl
      }
    }

    return {
      title: '邀请好友领现金',
      path: '/pages/invitation-cash-new/invitation-cash-new'
    }
  }
})
