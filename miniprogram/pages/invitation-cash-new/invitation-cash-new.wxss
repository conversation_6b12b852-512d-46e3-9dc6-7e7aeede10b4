/* pages/invitation-cash-new/invitation-cash-new.wxss */
.container {
  min-height: 100vh;
  background: #f5f7fa;
  padding-top: 20rpx;

}

/* 页面加载状态 */
.page-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f5f7fa;
}

.loading-content {
  text-align: center;
}

.loading-text {
  color: #666;
  font-size: 28rpx;
}

/* 页面内容 */
.page-content {
  padding-bottom: 40rpx;
  width: 96%;
}

/* 顶部标题区域 */
.header-section {
  position: relative;
  padding: 30rpx 0 40rpx;
  margin-bottom: 20rpx;
}

.header-bg {
  position: relative;
  z-index: 1;
}

.header-content {
  text-align: center;
  padding: 0 40rpx;
}

.main-title {
  display: block;
  color: #e60012;
  font-size: 52rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.sub-title {
  display: block;
  color: #e60012;
  font-size: 30rpx;
  font-weight: 500;
}

/* 模式一和模式二的通用样式 */
.mode-one,
.mode-two {
  margin: 0 30rpx;
}

/* 奖励卡片通用样式 */
.reward-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.reward-header {
  text-align: center;
  margin-bottom: 30rpx;
}

/* 左对齐的奖励标题 */
.reward-header.left-align {
  text-align: left;
}

.reward-title {
  display: block;
  color: #333;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.reward-subtitle {
  display: block;
  color: #666;
  font-size: 26rpx;
}

/* 奖励一区域：邀请码输入 */
.reward-one-section {
  margin-bottom: 20rpx;
}

.input-form {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.code-input {
  flex: 1;
  height: 88rpx;
  border: 2rpx solid #e1e5e9;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  text-align: center;
  letter-spacing: 8rpx;
  font-weight: bold;
  background: #fafbfc;
  transition: all 0.3s ease;
}

.code-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.submit-btn {
  width: 160rpx;
  height: 88rpx;
  background: #d1d5db;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.submit-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.submit-btn.active:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.submit-btn.disabled {
  background: #d1d5db;
  cursor: not-allowed;
}

.submit-text {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

/* 模式二：总奖励金额区域 */
.total-reward-section {
  margin-bottom: 20rpx;
}

/* 奖励列表前的总奖励金额显示 */
.total-reward-summary {
  margin: 0 30rpx 20rpx;
}

.total-reward-card {
  background: linear-gradient(135deg, #e60012 0%, #ff4d4f 100%);
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(230, 0, 18, 0.3);
  text-align: center;
}

.reward-amount-display {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.amount-label {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.amount-value {
  color: #ffffff;
  font-size: 64rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 不可用金额显示 */
.unavailable-amount-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 24rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.3);
}

.unavailable-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  font-weight: 400;
  margin-bottom: 8rpx;
}

.unavailable-value {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 600;
}

/* 奖励二区域：邀请码展示 */
.reward-two-section {
  margin: 0 30rpx 20rpx;
}

.invitation-code-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
  border: 2rpx dashed #dee2e6;
}

.code-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #e60012;
  letter-spacing: 2rpx;
}

.copy-btn {
  padding: 16rpx 40rpx;
  background: #e60012;
  border-radius: 30rpx;
  transition: all 0.2s ease;
}

.copy-btn:active {
  background: #cc0010;
  transform: scale(0.95);
}

.copy-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}


/* 奖励三区域 */
.reward-three-section {
  margin: 0 30rpx 20rpx;
}

/* 邀请列表区域 */
.invitation-list-section {
  margin: 0 30rpx;
  margin-top: 30rpx;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 8rpx;
}

.list-title {
  color: #333;
  font-size: 32rpx;
  font-weight: bold;
}

.list-count {
  color: #666;
  font-size: 26rpx;
}

/* 记录列表 */
.records-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f1f3f4;
  transition: background-color 0.2s ease;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:active {
  background-color: #f8f9fa;
}

.record-info-main {
  display: flex;
  flex-direction: column;
}

.record-name {
  color: #333;
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.record-phone {
  color: #888;
  font-size: 26rpx;
}

.record-details-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.record-time {
  color: #999;
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.record-status {
  flex-shrink: 0;
  text-align: right; /* 确保状态徽标靠右对齐 */
}

/* 状态徽标：使用更稳健的写法，避免在不同端出现不规则背景 */
.record-status .status-text {
  /* 让真正的胶囊样式交由容器 .status-chip 处理 */
  display: inline;            /* 仅保持内联排版即可 */
  line-height: 1;             /* 防止行高影响视觉居中 */
  font-size: 22rpx;
  font-weight: 500;
  vertical-align: middle;
}

/* 统一的状态胶囊容器，彻底保障垂直/水平居中 */
.record-status .status-chip {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  border-radius: 9999rpx;
  white-space: nowrap;
}

/* 颜色与背景由状态控制 */
.record-status .status-chip.ordered { background: #d4edda; color: #155724; }
.record-status .status-chip.not-ordered { background: #f8d7da; color: #721c24; }

/* 内部文字采用继承颜色，避免额外行高影响 */
.record-status .status-chip .status-text {
  line-height: 1;        /* 避免行高把容器撑高 */
  font-size: 22rpx;
  font-weight: 500;
  color: inherit;        /* 跟随容器颜色 */
}


.status-text.ordered {
  background: #d4edda;
  color: #155724;
}

.status-text.not-ordered {
  background: #f8d7da;
  color: #721c24;
}

/* 空状态 */
.empty-records {
  text-align: center;
  padding: 100rpx 40rpx;
  background: white;
  border-radius: 20rpx;
}

.empty-icon {
  display: block;
  font-size: 100rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  color: #666;
  font-size: 30rpx;
  margin-bottom: 12rpx;
}

.empty-hint {
  display: block;
  color: #999;
  font-size: 26rpx;
}

/* 加载状态 */
.loading-records {
  text-align: center;
  padding: 60rpx;
  background: white;
  border-radius: 20rpx;
}

.load-more {
  text-align: center;
  padding: 40rpx;
}

.load-more-text {
  color: #999;
  font-size: 26rpx;
}

/* 我的奖励列表 */
.my-rewards-section {
  margin-top: 40rpx;
  padding: 0 24rpx;
}

.rewards-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.reward-item {
  display: flex;
  align-items: center;     /* 恢复为center，确保所有子元素垂直居中 */
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 120rpx;
}

.reward-item:last-child {
  border-bottom: none;
}

.reward-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.reward-icon .icon-text {
  font-size: 36rpx;
}

.reward-info {
  flex: 1;
  min-width: 0;
  /* 移除flex布局，让内容自然排列 */
}

.reward-main {
  margin-bottom: 6rpx;  /* 减少间距 */
}

.reward-title {
  color: #333;
  font-size: 32rpx;
  font-weight: 600;
  line-height: 1.3;     /* 添加行高控制 */
}

.reward-detail {
  display: flex;
  flex-direction: column;
  gap: 2rpx;            /* 减少间距 */
}

.reward-desc {
  color: #666;
  font-size: 26rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.reward-time {
  color: #999;
  font-size: 24rpx;
}

/* 新的金额和状态组合区域 */
.reward-amount-status {
  margin-left: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;  /* 垂直居中 */
  min-width: 120rpx;
}

.reward-amount {
  color: #e60012;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

/* 奖励状态样式 */
.reward-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-bottom: 6rpx;
  text-align: center;
  min-width: 60rpx;
}

.status-issued {
  background: #e8f5e8;
  color: #52c41a;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-expired {
  background: #fff1f0;
  color: #ff4d4f;
}

/* 过期时间样式 */
.reward-expires {
  font-size: 22rpx;
  color: #999;
  text-align: center;
}

/* 空状态 */
.empty-rewards {
  text-align: center;
  padding: 80rpx 40rpx;
  background: white;
  border-radius: 20rpx;
}

.empty-rewards .empty-icon {
  display: block;
  font-size: 100rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-rewards .empty-text {
  display: block;
  color: #666;
  font-size: 30rpx;
  margin-bottom: 12rpx;
}

.empty-rewards .empty-hint {
  display: block;
  color: #999;
  font-size: 26rpx;
}

/* 加载状态 */
.loading-rewards {
  text-align: center;
  padding: 60rpx;
  background: white;
  border-radius: 20rpx;
}
