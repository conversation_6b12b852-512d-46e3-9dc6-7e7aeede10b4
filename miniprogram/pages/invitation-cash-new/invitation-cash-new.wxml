<!--pages/invitation-cash-new/invitation-cash-new.wxml-->
<view class="container">
  <!-- 页面加载状态 -->
  <view wx:if="{{pageLoading}}" class="page-loading">
    <view class="loading-content">
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 页面内容 -->
  <view wx:else class="page-content">
    <!-- 顶部标题区域 -->
    <view class="header-section">
      <view class="header-bg">
        <view class="header-content">
          <text class="main-title">现金奖励·可享0元购</text>
          <text class="sub-title">长期有效 上不封顶 一次邀请多重奖励</text>
        </view>
      </view>
    </view>

    <!-- 总奖励金额显示（在奖励一前面） -->
    <view class="total-reward-summary">
      <view class="total-reward-card">
        <view class="reward-amount-display">
          <text class="amount-label">我获得的总奖励金额</text>
          <text class="amount-value">¥{{formattedTotalReward}}</text>
        </view>
        <!-- 不可用金额显示 -->
        <view wx:if="{{unavailableAmount > 0}}" class="unavailable-amount-display">
          <text class="unavailable-label">不可用金额（待发放）</text>
          <text class="unavailable-value">¥{{formattedUnavailableAmount}}</text>
        </view>
      </view>
    </view>

    <!-- 模式一：未填写过邀请码 -->
    <view wx:if="{{!hasUsedInvitationCode}}" class="mode-one">
      <!-- 奖励一区域：填写邀请码得20现金优惠 -->
      <view class="reward-one-section">
        <view class="reward-card">
          <view class="reward-header left-align">
            <text class="reward-title">奖励一：填写邀请码得 20 ～ 100 现金优惠</text>
          </view>
          
          <view class="input-form">
            <input
              class="code-input"
              placeholder="请输入4位邀请码"
              value="{{inputCode}}"
              bindinput="onCodeInput"
              maxlength="4"
              type="text"
            />
            <view
              class="submit-btn {{inputCode.length === 4 ? 'active' : ''}} {{submitting || inputCode.length !== 4 ? 'disabled' : ''}}"
              bindtap="submitInvitationCode"
            >
              <text class="submit-text">{{submitting ? '领取中...' : '领取'}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 模式二：已填写过邀请码 -->
    <view wx:else class="mode-two">
      <!-- 已填写邀请码的提示信息可以在这里添加，如果需要的话 -->
    </view>

    <!-- 奖励二区域：我的邀请码 -->
    <view class="reward-two-section">
      <view class="reward-card">
        <view class="reward-header left-align">
          <text class="reward-title">奖励二：我的邀请码</text>
          <text class="reward-subtitle">分享邀请码，双方都得最高100奖励</text>
        </view>
        <view class="invitation-code-display">
          <text class="code-value">{{myInvitationCode.invitationCode || '----'}}</text>
          <view class="copy-btn" bindtap="copyMyInvitationCode">
            <text class="copy-text">复制</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 奖励三区域：邀请下单领现金（两种模式共有） -->
    <view class="reward-three-section">
      <view class="reward-card">
        <view class="reward-header left-align">
          <text class="reward-title">奖励三：邀请下单领现金</text>
          <text class="reward-subtitle">邀请的用户任意时间下单，可得奖励，长期有效，上不封顶</text>
        </view>
      </view>
    </view>

    <!-- 我的奖励列表 -->
    <view class="my-rewards-section">
      <view class="list-header">
        <text class="list-title">我的邀请奖励</text>
        <text class="list-count">共{{myRewards.length}}条</text>
      </view>

      <!-- 奖励记录列表 -->
      <view wx:if="{{myRewards.length > 0}}" class="rewards-list">
        <view
          wx:for="{{myRewards}}"
          wx:key="id"
          class="reward-item"
        >
          <view class="reward-icon">
            <text class="icon-text">💰</text>
          </view>
          <view class="reward-info">
            <view class="reward-main">
              <text class="reward-title">{{item.rewardTypeDesc || '奖励'}}</text>
            </view>
            <view class="reward-detail">
              <text class="reward-desc">{{item.configName || item.description || '邀请奖励'}}</text>
            </view>
          </view>
          <view class="reward-amount-status">
            <text class="reward-amount">+¥{{item.rewardAmount}}</text>
            <text class="reward-status {{item.status === 'ISSUED' ? 'status-issued' : item.status === 'PENDING' ? 'status-pending' : 'status-expired'}}">{{item.statusDesc}}</text>
            <text wx:if="{{item.formattedExpiresAt}}" class="reward-expires">{{item.formattedExpiresAt}}</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view wx:elif="{{!rewardsLoading}}" class="empty-rewards">
        <text class="empty-icon">💰</text>
        <text class="empty-text">暂无奖励记录</text>
        <text class="empty-hint">邀请好友注册下单即可获得奖励</text>
      </view>

      <!-- 加载状态 -->
      <view wx:if="{{rewardsLoading}}" class="loading-rewards">
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 加载更多 -->
      <view wx:if="{{hasMoreRewards && myRewards.length > 0}}" class="load-more" bindtap="loadMoreRewards">
        <text class="load-more-text">点击加载更多</text>
      </view>
    </view>

    <!-- 我的邀请列表 -->
    <view class="invitation-list-section">
      <view class="list-header">
        <text class="list-title">我的邀请列表</text>
        <text class="list-count">共{{totalRecords}}人</text>
      </view>

      <!-- 邀请记录列表 -->
      <view wx:if="{{invitationRecords.length > 0}}" class="records-list">
        <view
          wx:for="{{invitationRecords}}"
          wx:key="id"
          class="record-item"
        >
          <view class="record-info-main">
            <text class="record-name">{{item.displayName}}</text>
            <text class="record-phone">{{item.maskedPhone}}</text>
          </view>
          <view class="record-details-right">
            <text class="record-time">{{item.formattedTime}}</text>
            <view class="record-status">
              <view class="status-chip {{item.hasOrdered ? 'ordered' : 'not-ordered'}}">
                <text class="status-text">{{item.hasOrdered ? '已下单' : '未下单'}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view wx:elif="{{!recordsLoading}}" class="empty-records">
        <text class="empty-icon">👥</text>
        <text class="empty-text">暂无邀请记录</text>
        <text class="empty-hint">快去分享邀请码给好友吧</text>
      </view>

      <!-- 加载状态 -->
      <view wx:if="{{recordsLoading}}" class="loading-records">
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 加载更多 -->
      <view wx:if="{{hasMoreRecords && invitationRecords.length > 0}}" class="load-more">
        <text class="load-more-text">上拉加载更多</text>
      </view>
    </view>
  </view>
</view>
