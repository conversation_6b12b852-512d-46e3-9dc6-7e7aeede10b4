/* pages/settings/settings.wxss */
.container {
  padding: 20rpx;
  background-color: #f7f8fa;
  min-height: 100vh;
  box-sizing: border-box;
}

.setting-group {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  width: 96%;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f7f8fa;
  position: relative;
}

.setting-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 32rpx;
  color: #333;
}

.item-value-wrapper {
  display: flex;
  align-items: center;
}

.item-value {
  font-size: 32rpx;
  color: #999;
  text-align: right;
}

.arrow-right {
  font-size: 32rpx;
  color: #c8c8cc;
  margin-left: 10rpx;
}

/* 退出登录按钮 */
.logout-btn-container {
  padding: 30rpx 30rpx 20rpx; /* 顶部增加内边距形成空隙 */
}

.logout-btn {
  background-color: #e64340;
  color: #ffffff;
  font-size: 34rpx;
  border-radius: 8rpx; /* 圆角改小一些，在卡片内更协调 */
}

.logout-btn::after {
  border: none;
}

/* --- 修改昵称弹窗样式 --- */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-dialog {
  background-color: #fff;
  border-radius: 16rpx;
  width: 85%;
  max-width: 600rpx;
  overflow: hidden;
}

.modal-title {
  padding: 32rpx;
  font-size: 34rpx;
  font-weight: 500;
  text-align: center;
}

.modal-content {
  padding: 0 40rpx 40rpx;
}

.modal-input {
  background-color: #f7f7f7;
  border-radius: 8rpx;
  padding: 0 36rpx; /* 只保留水平内边距 */
  width: 100%;
  box-sizing: border-box;
  font-size: 32rpx;
  text-align: center;
  height: 104rpx; /* 显式设置高度，让浏览器自动垂直对齐内容 */
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #ebedf0;
}

.modal-btn {
  flex: 1;
  padding: 28rpx;
  font-size: 34rpx;
  text-align: center;
  background-color: #fff;
  border-radius: 0;
}

.modal-btn::after {
  border: none;
}

.cancel-btn {
  color: #333;
}

.confirm-btn {
  color: #07c160; /* 微信绿 */
  font-weight: 500;
}

/* 按钮竖线 */
.cancel-btn::after {
  content: ' ';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 1rpx;
  background-color: #ebedf0;
}