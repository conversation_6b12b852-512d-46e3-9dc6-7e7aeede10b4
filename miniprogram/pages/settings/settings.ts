// pages/settings/settings.ts
import AuthManager from '../../utils/auth';

Page({
  data: {
    userInfo: {
      nickname: '',
      phone: ''
    },
    // 用于弹窗输入绑定的临时昵称
    inputNickname: '',
    // 控制弹窗显示
    showNicknameModal: false,
  },

  onLoad() {
    this.getUserInfo();
  },

  /**
   * 获取并设置用户信息
   */
  async getUserInfo() {
    const userInfo = AuthManager.getUserInfo();
    if (userInfo) {
      this.setData({
        userInfo: {
          nickname: userInfo.nickName || '完善信息',
          phone: userInfo.phone || userInfo.phoneNumber || ''
        }
      });
    }
  },

  // --- 昵称修改弹窗相关方法 ---

  /**
   * 显示修改昵称弹窗
   */
  handleShowNicknameModal() {
    this.setData({
      // 预填入当前昵称
      inputNickname: this.data.userInfo.nickname,
      showNicknameModal: true,
    });
  },

  /**
   * 关闭修改昵称弹窗
   */
  handleCloseNicknameModal() {
    this.setData({
      showNicknameModal: false,
    });
  },

  /**
   * 确认修改昵称
   */
  handleConfirmNickname() {
    const newNickname = this.data.inputNickname.trim();
    // 关闭弹窗
    this.handleCloseNicknameModal();

    // 检查昵称是否有效且已更改
    if (newNickname && newNickname !== this.data.userInfo.nickname) {
      this.updateNickname(newNickname);
    } else if (!newNickname) {
      wx.showToast({ title: '昵称不能为空', icon: 'none' });
    }
  },

  /**
   * 调用后端接口更新昵称
   */
  async updateNickname(nickName: string) {
    wx.showLoading({
      title: '更新中...'
    });

    try {
      const result = await AuthManager.updateUserNickname(nickName);
      wx.hideLoading();

      if (result.success) {
        this.setData({
          'userInfo.nickname': nickName
        });
        wx.showToast({
          title: '昵称更新成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: result.message || '更新失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '更新失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 退出登录
   */
  logout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          AuthManager.logout();
          // 返回个人中心页面并刷新
          wx.reLaunch({
            url: '/pages/profile/profile'
          });
        }
      }
    });
  },

  /**
   * 空方法，用于阻止底层页面滚动
   */
  preventTouchMove() {},
});