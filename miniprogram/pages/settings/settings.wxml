<!--pages/settings/settings.wxml-->
<view class="container">
  <!-- 主设置项 -->
  <view class="setting-group">
    <view class="setting-item" bindtap="handleShowNicknameModal">
      <text class="item-label">昵称</text>
      <view class="item-value-wrapper">
        <text class="item-value">{{userInfo.nickname}}</text>
        <text class="arrow-right">></text>
      </view>
    </view>
    <view class="setting-item">
      <text class="item-label">手机号</text>
      <text class="item-value">{{userInfo.phone || '未绑定'}}</text>
    </view>

    <!-- 退出登录按钮 -->
    <view class="logout-btn-container">
      <button class="logout-btn" bindtap="logout">退出登录</button>
    </view>
  </view>

  <!-- 修改昵称弹窗 -->
  <view class="modal-mask" wx:if="{{showNicknameModal}}" catchtouchmove="preventTouchMove">
    <view class="modal-dialog">
      <view class="modal-title">修改昵称</view>
      <view class="modal-content">
        <input 
          class="modal-input"
          model:value="{{inputNickname}}" 
          placeholder="请输入新的昵称"
          maxlength="8"
          focus="{{true}}"
        />
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="handleCloseNicknameModal">取消</button>
        <button class="modal-btn confirm-btn" bindtap="handleConfirmNickname">确定</button>
      </view>
    </view>
  </view>
</view>