<!--pages/order-detail/order-detail.wxml-->
<wxs module="utils">
  function formatAmount(amount) {
    if (!amount || isNaN(amount)) {
      return '0.00';
    }
    return (amount / 100).toFixed(2);
  }

  function splitAttributes(attributes) {
    if (!attributes || typeof attributes !== 'string' || attributes.trim() === '') {
      return [];
    }

    var trimmed = attributes.trim();

    // 首先尝试中文逗号分割
    if (trimmed.indexOf('，') > -1) {
      return trimmed.split('，').filter(function(attr) {
        return attr && attr.trim();
      }).map(function(attr) {
        return attr.trim();
      });
    }

    // 然后尝试英文逗号分割
    if (trimmed.indexOf(',') > -1) {
      return trimmed.split(',').filter(function(attr) {
        return attr && attr.trim();
      }).map(function(attr) {
        return attr.trim();
      });
    }

    // 尝试分号分割
    if (trimmed.indexOf(';') > -1 || trimmed.indexOf('；') > -1) {
      var sep = trimmed.indexOf(';') > -1 ? ';' : '；';
      return trimmed.split(sep).filter(function(attr) {
        return attr && attr.trim();
      }).map(function(attr) {
        return attr.trim();
      });
    }

    // 如果没有找到分隔符，返回单个元素数组
    return [trimmed];
  }

  module.exports = {
    formatAmount: formatAmount,
    splitAttributes: splitAttributes
  };
</wxs>

<view class="order-detail-page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="content">
    <!-- 订单信息 -->
    <view class="order-info-section">
      <view class="section-title-with-status">
        <text class="section-title">订单信息</text>
        <text class="status-text status-{{orderInfo.statusClass}}">{{orderInfo.statusText}}</text>
      </view>
      <view class="info-list">
        <view class="info-item">
          <text class="label">订单号：</text>
          <view class="value-with-copy">
            <text class="value">{{orderInfo.orderNumber}}</text>
            <view class="copy-btn" bindtap="copyText" data-text="{{orderInfo.orderNumber}}" data-type="订单号">
              <text class="copy-icon">📋</text>
            </view>
          </view>
        </view>
        <view class="info-item">
          <text class="label">下单时间：</text>
          <text class="value">{{orderInfo.createTime}}</text>
        </view>
        <!-- 发货信息（仅在已发货时显示） -->
        <view wx:if="{{orderInfo.shippedAt}}" class="info-item">
          <text class="label">发货时间：</text>
          <text class="value">{{orderInfo.shippedAt}}</text>
        </view>
        <view wx:if="{{orderInfo.shippingCompany}}" class="info-item">
          <text class="label">快递公司：</text>
          <text class="value">{{orderInfo.shippingCompany}}</text>
        </view>
        <view wx:if="{{orderInfo.trackingNumber}}" class="info-item">
          <text class="label">快递单号：</text>
          <view class="value-with-copy">
            <text class="value">{{orderInfo.trackingNumber}}</text>
            <view class="copy-btn" bindtap="copyText" data-text="{{orderInfo.trackingNumber}}" data-type="快递单号">
              <text class="copy-icon">📋</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 收货地址 -->
    <view class="address-section">
      <view class="section-title">收货地址</view>
      <view class="address-info">
        <view class="address-row">
          <text class="recipient-name">{{orderInfo.shippingAddress.recipientName}}</text>
          <text class="recipient-phone">{{orderInfo.shippingAddress.phoneNumber}}</text>
        </view>
        <text class="address-detail">{{orderInfo.shippingAddress.fullAddress}}</text>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="products-section">
      <view class="section-title">商品信息</view>
      <view class="products-list">
        <view wx:for="{{orderInfo.items}}" wx:key="skuId" class="product-item">
          <image class="product-image" src="{{item.productImageUrl || 'https://via.placeholder.com/150x150?text=商品图片'}}" mode="aspectFill"></image>
          <view class="product-details">
            <text class="product-name">{{item.productName}}</text>
            <view class="product-attributes-container">
              <text wx:for="{{utils.splitAttributes(item.skuAttributes)}}" wx:key="*this" class="product-attributes">{{item}}</text>
            </view>
            <view class="product-price-quantity">
              <text class="product-price">¥{{utils.formatAmount(item.unitPrice)}}</text>
              <text class="product-quantity">×{{item.quantity}}</text>
            </view>
          </view>
          <view class="product-subtotal">
            <text class="subtotal-amount">¥{{utils.formatAmount(item.subtotal)}}</text>
          </view>
        </view>
      </view>
    </view>



    <!-- 费用明细 -->
    <view class="cost-section">
      <view class="section-title">费用明细</view>
      <view class="cost-list">
        <view class="cost-item">
          <text class="cost-label">商品总价</text>
          <text class="cost-value">¥{{utils.formatAmount(orderInfo.totalAmount)}}</text>
        </view>
        <view class="cost-item">
          <text class="cost-label">运费</text>
          <text class="cost-value">¥0.00</text>
        </view>
        <view class="cost-item total">
          <text class="cost-label">实付款</text>
          <text class="cost-value total-amount">¥{{utils.formatAmount(orderInfo.totalAmount)}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view wx:if="{{orderActions.length > 0}}" class="bottom-bar">
    <view class="actions-container">
      <button wx:for="{{orderActions}}" wx:key="action"
              class="action-btn {{item.type === 'primary' ? 'primary' : 'secondary'}} {{item.disabled ? 'disabled' : ''}}"
              bindtap="onOrderAction"
              data-action="{{item.action}}"
              disabled="{{item.disabled}}">
        {{item.text}}
      </button>
    </view>
  </view>
</view>
