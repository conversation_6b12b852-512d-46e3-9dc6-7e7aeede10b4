// pages/order-detail/order-detail.ts
import OrderService, { OrderStatus } from '../../services/orderService'
import PaymentService from '../../services/paymentService'
import RequestManager from '../../utils/request'
import { OrderInfo } from '../../types/order'
import { CreatePaymentRequest } from '../../types/payment'

interface OrderDetailData {
  loading: boolean
  orderInfo: OrderInfo | null
  orderActions: Array<{text: string, action: string, type?: string}>
  debugInfo?: any
}

Page<OrderDetailData>({
  data: {
    loading: true,
    orderInfo: null,
    orderActions: [],
    debugInfo: null
  },

  onLoad(options: any) {
    console.log('订单详情页面加载，参数:', options)
    
    const orderId = parseInt(options.orderId)
    if (!orderId) {
      wx.showToast({
        title: '订单ID无效',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.loadOrderDetail(orderId)
  },

  /**
   * 加载订单详情
   */
  async loadOrderDetail(orderId: number) {
    try {
      this.setData({ loading: true })

      // 先直接调用接口获取原始数据
      const rawResponse = await RequestManager.get(`/api/user/orders/${orderId}`)
      console.log('原始接口响应:', rawResponse)

      const orderInfo = await OrderService.getOrderById(orderId)

      console.log('订单详情加载结果:', orderInfo)

      if (!orderInfo) {
        wx.showModal({
          title: '订单不存在',
          content: '未找到该订单信息',
          showCancel: false,
          success: () => {
            wx.navigateBack()
          }
        })
        return
      }

      // 获取订单可用操作，传递订单信息用于退款时效判断
      const orderActions = OrderService.getOrderActions(orderInfo.status, orderInfo)

      console.log('订单操作按钮:', orderActions)

      // 添加状态显示信息
      const orderInfoWithStatus = {
        ...orderInfo,
        statusText: this.formatOrderStatus(orderInfo.status),
        statusClass: this.getStatusClass(orderInfo.status)
      }

      this.setData({
        orderInfo: orderInfoWithStatus,
        orderActions,
        debugInfo: rawResponse,
        loading: false
      })

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '订单详情'
      })

    } catch (error: any) {
      console.error('加载订单详情失败:', error)
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'error'
      })
      this.setData({ loading: false })
    }
  },

  /**
   * 处理订单操作
   */
  async onOrderAction(e: any) {
    const { action } = e.currentTarget.dataset
    const { orderInfo, orderActions } = this.data

    if (!orderInfo) return

    // 检查按钮是否被禁用
    const actionButton = orderActions.find(btn => btn.action === action)
    if (actionButton?.disabled) {
      wx.showToast({
        title: '该操作已过期',
        icon: 'none'
      })
      return
    }

    console.log('订单操作:', action, orderInfo)

    switch (action) {
      case 'pay':
        await this.payOrder(orderInfo)
        break
      case 'cancel':
        await this.cancelOrder(orderInfo.id)
        break
      case 'confirm':
        await this.confirmOrder(orderInfo.id)
        break
      case 'delete':
        await this.deleteOrder(orderInfo.id)
        break
      case 'logistics':
        this.viewLogistics(orderInfo.orderNumber)
        break
      case 'review':
        this.reviewOrder(orderInfo.id)
        break
      case 'rebuy':
        this.rebuyOrder(orderInfo)
        break
      case 'refund':
        this.applyRefund(orderInfo)
        break
      default:
        console.log('未知操作:', action)
    }
  },

  /**
   * 支付订单
   */
  async payOrder(order: OrderInfo) {
    try {
      wx.showLoading({ title: '正在跳转支付...' })

      const paymentData: CreatePaymentRequest = {
        orderNo: order.orderNumber,
        totalAmount: order.totalAmount,
        description: `订单支付 - ${order.orderNumber}`
      }

      // 使用完整支付流程：创建支付 -> 调起微信支付 -> 成功后跳转
      await PaymentService.processPayment(paymentData)

    } catch (error: any) {
      console.error('支付失败:', error)
      if (error.message !== '用户取消支付') {
        wx.showToast({
          title: error.message || '支付失败',
          icon: 'error'
        })
      } else {
        wx.showToast({
          title: '支付已取消',
          icon: 'none'
        })
      }
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 取消订单
   */
  async cancelOrder(orderId: number) {
    try {
      const result = await new Promise<boolean>((resolve) => {
        wx.showModal({
          title: '确认取消',
          content: '确定要取消这个订单吗？',
          success: (res) => resolve(res.confirm),
          fail: () => resolve(false)
        })
      })

      if (!result) return

      wx.showLoading({ title: '取消中...' })
      
      const success = await OrderService.cancelOrder(orderId)
      
      wx.hideLoading()
      
      if (success) {
        wx.showToast({
          title: '取消成功',
          icon: 'success'
        })
        // 重新加载订单详情
        setTimeout(() => {
          this.loadOrderDetail(orderId)
        }, 1500)
      } else {
        wx.showToast({
          title: '取消失败',
          icon: 'error'
        })
      }

    } catch (error: any) {
      wx.hideLoading()
      console.error('取消订单失败:', error)
      wx.showToast({
        title: error.message || '取消失败',
        icon: 'error'
      })
    }
  },

  /**
   * 确认收货
   */
  async confirmOrder(orderId: number) {
    try {
      const result = await new Promise<boolean>((resolve) => {
        wx.showModal({
          title: '确认收货',
          content: '确认已收到商品吗？',
          success: (res) => resolve(res.confirm),
          fail: () => resolve(false)
        })
      })

      if (!result) return

      wx.showLoading({ title: '确认中...' })
      
      const success = await OrderService.confirmReceived(orderId)
      
      wx.hideLoading()
      
      if (success) {
        wx.showToast({
          title: '确认成功',
          icon: 'success'
        })
        // 重新加载订单详情
        setTimeout(() => {
          this.loadOrderDetail(orderId)
        }, 1500)
      } else {
        wx.showToast({
          title: '确认失败',
          icon: 'error'
        })
      }

    } catch (error: any) {
      wx.hideLoading()
      console.error('确认收货失败:', error)
      wx.showToast({
        title: error.message || '确认失败',
        icon: 'error'
      })
    }
  },

  /**
   * 删除订单
   */
  async deleteOrder(orderId: number) {
    try {
      const result = await new Promise<boolean>((resolve) => {
        wx.showModal({
          title: '确认删除',
          content: '确定要删除这个订单吗？删除后无法恢复。',
          success: (res) => resolve(res.confirm),
          fail: () => resolve(false)
        })
      })

      if (!result) return

      wx.showLoading({ title: '删除中...' })
      
      const success = await OrderService.deleteOrder(orderId)
      
      wx.hideLoading()
      
      if (success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
        // 返回订单列表
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: '删除失败',
          icon: 'error'
        })
      }

    } catch (error: any) {
      wx.hideLoading()
      console.error('删除订单失败:', error)
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'error'
      })
    }
  },

  /**
   * 查看物流
   */
  viewLogistics(orderNumber: string) {
    wx.navigateTo({
      url: `/pages/logistics/logistics?orderNumber=${orderNumber}`
    })
  },

  /**
   * 评价订单
   */
  reviewOrder(orderId: number) {
    wx.navigateTo({
      url: `/pages/review/review?orderId=${orderId}`
    })
  },

  /**
   * 再次购买
   */
  rebuyOrder(order: OrderInfo) {
    wx.showModal({
      title: '再次购买',
      content: '将订单中的商品重新加入购物车？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '已加入购物车',
            icon: 'success'
          })
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/cart/cart'
            })
          }, 1500)
        }
      }
    })
  },

  /**
   * 申请退款
   */
  applyRefund(order: OrderInfo) {
    wx.navigateTo({
      url: `/pages/refund-apply/refund-apply?orderId=${order.id}`
    })
  },

  /**
   * 格式化订单状态
   */
  formatOrderStatus(status: string): string {
    return OrderService.formatOrderStatus(status)
  },

  /**
   * 获取状态样式类名
   */
  getStatusClass(status: string): string {
    const classMap: Record<string, string> = {
      [OrderStatus.PENDING_PAYMENT]: 'pending-payment',
      [OrderStatus.PAID_SHIPPED]: 'paid-shipped',
      [OrderStatus.SHIPPED]: 'shipped'
    }
    return classMap[status] || 'default'
  },

  /**
   * 复制文本到剪贴板
   * 支持复制订单号、快递单号等文本信息
   */
  copyText(e: any) {
    const text = e.currentTarget.dataset.text
    const type = e.currentTarget.dataset.type || '文本'

    if (!text) {
      wx.showToast({
        title: '复制内容为空',
        icon: 'error'
      })
      return
    }

    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: `${type}已复制`,
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    })
  }
})
