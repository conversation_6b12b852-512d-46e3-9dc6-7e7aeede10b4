/* pages/order-detail/order-detail.wxss */
.order-detail-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #999;
  font-size: 28rpx;
}

/* 内容区域 */
.content {
  padding: 20rpx;
}

/* 通用区块样式 */
.address-section,
.products-section,
.order-info-section,
.cost-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

/* 带状态的标题样式 */
.section-title-with-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title-with-status .section-title {
  margin-bottom: 0;
}

.section-title-with-status .status-text {
  font-size: 28rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  display: inline-block;
  margin-bottom: 0;
}

/* 订单状态 */
.status-header {
  text-align: center;
  padding: 20rpx 0;
}

.status-text {
  font-size: 36rpx;
  font-weight: 600;
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  display: inline-block;
  margin-bottom: 16rpx;
}

.status-text.status-pending-payment {
  background-color: #fff3cd;
  color: #856404;
}

.status-text.status-paid-shipped {
  background-color: #cce5ff;
  color: #004085;
}

.status-text.status-shipped {
  background-color: #e2e3ff;
  color: #383d41;
}



.order-number {
  font-size: 28rpx;
  color: #666;
  display: block;
}

/* 收货地址 */
.address-info {
  background-color: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
}

.address-row {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.recipient-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.recipient-phone {
  font-size: 28rpx;
  color: #666;
}

.address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

/* 商品信息 */
.products-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.product-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
}

.product-attributes-container {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  margin-bottom: 8rpx;
}

.product-attributes {
  font-size: 24rpx;
  color: #999;
  line-height: 1.3;
}

.product-price-quantity {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 26rpx;
  color: #ff3b30;
  font-weight: 600;
}

.product-quantity {
  font-size: 24rpx;
  color: #666;
}

.product-subtotal {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}

.subtotal-amount {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

/* 信息列表 */
.info-list,
.cost-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item,
.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child,
.cost-item:last-child {
  border-bottom: none;
}

.label,
.cost-label {
  color: #666;
  font-size: 28rpx;
}

.value,
.cost-value {
  color: #333;
  font-size: 28rpx;
  text-align: right;
}

/* 带复制按钮的值容器 */
.value-with-copy {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12rpx;
}

.value-with-copy .value {
  text-align: right;
  flex: 1;
}

/* 复制按钮样式 */
.copy-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  transition: background-color 0.2s;
}

.copy-btn:active {
  background-color: #e0e0e0;
}

.copy-icon {
  font-size: 24rpx;
  color: #666;
}

.cost-item.total {
  padding-top: 20rpx;
  border-top: 2rpx solid #eee;
  margin-top: 10rpx;
}

.cost-item.total .cost-label {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.total-amount {
  font-size: 32rpx;
  color: #ff3b30;
  font-weight: 700;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.actions-container {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  min-width: 120rpx;
  text-align: center;
}

.action-btn.secondary {
  background-color: #f8f9fa;
  color: #666;
  border: 2rpx solid #ddd;
}

.action-btn.primary {
  background-color: #007aff;
  color: #fff;
}

.action-btn:active {
  opacity: 0.8;
}

.action-btn.disabled {
  background-color: #f5f5f5 !important;
  color: #ccc !important;
  border-color: #eee !important;
  cursor: not-allowed;
}

.action-btn.disabled:active {
  opacity: 1;
}

.debug-content {
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
  max-height: 400rpx;
  overflow-y: scroll;
}

.debug-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
  word-break: break-all;
}
