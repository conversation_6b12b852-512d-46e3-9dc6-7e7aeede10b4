<!--pages/refund-apply/refund-apply.wxml-->
<wxs module="utils">
  function formatAmount(amount) {
    if (!amount || isNaN(amount)) {
      return '0.00';
    }
    return (amount / 100).toFixed(2);
  }

  module.exports = {
    formatAmount: formatAmount
  };
</wxs>

<view class="refund-apply-page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="content">
    <!-- 订单信息 -->
    <view class="order-section">
      <view class="section-title">订单信息</view>
      <view class="order-info">
        <view class="order-item">
          <text class="label">订单号：</text>
          <text class="value">{{orderInfo.orderNumber}}</text>
        </view>
        <view class="order-item">
          <text class="label">订单状态：</text>
          <text class="value">{{orderInfo.orderStatusText}}</text>
        </view>
        <view class="order-item">
          <text class="label">订单金额：</text>
          <text class="value amount">¥{{utils.formatAmount(orderInfo.totalAmount)}}</text>
        </view>
      </view>
    </view>

    <!-- 退款类型 -->
    <view class="refund-type-section">
      <view class="section-title">退款类型</view>
      <radio-group bindchange="onRefundTypeChange">
        <!-- 仅退款：只有未发货状态(paid_shipped)才显示 -->
        <label wx:if="{{orderInfo.orderStatus === 'paid_shipped'}}" class="radio-item">
          <radio value="refund_only" checked="{{refundType === 'refund_only'}}" />
          <text class="radio-text">仅退款</text>
          <text class="radio-desc">不需要退货，直接退款</text>
        </label>
        <!-- 退货退款：已发货状态(shipped/completed)才显示 -->
        <label wx:if="{{orderInfo.orderStatus === 'shipped' || orderInfo.orderStatus === 'completed'}}" class="radio-item">
          <radio value="return_refund" checked="{{refundType === 'return_refund'}}" />
          <text class="radio-text">退货退款</text>
          <text class="radio-desc">需要退货后退款</text>
        </label>
        <!-- 如果订单状态不明确，显示所有选项但添加说明 -->
        <view wx:if="{{orderInfo.orderStatus !== 'paid_shipped' && orderInfo.orderStatus !== 'shipped' && orderInfo.orderStatus !== 'completed'}}" class="refund-type-notice">
          <text class="notice-text">根据订单状态自动确定退款类型</text>
        </view>
      </radio-group>
    </view>

    <!-- 退款商品 -->
    <view class="refund-items-section">
      <view class="section-title">退款商品</view>
      <view class="items-list">
        <view wx:for="{{orderInfo.items}}" wx:key="orderItemId" class="item-card">
          <view class="item-info">
            <image class="item-image" src="{{item.productImageUrl || '/images/default-product.png'}}" mode="aspectFill"></image>
            <view class="item-details">
              <text class="item-name">{{item.productName}}</text>
              <text class="item-price">¥{{utils.formatAmount(item.price)}}</text>
              <text class="item-quantity">数量：{{item.quantity}}</text>
            </view>
          </view>
          
          <view wx:if="{{item.canRefund}}" class="item-refund">
            <view class="quantity-row">
              <text class="quantity-label">退款数量：</text>
              <view class="quantity-controls">
                <view class="quantity-btn {{(item.refundQuantity || 0) <= 0 ? 'disabled' : ''}}"
                      bindtap="onQuantityChange"
                      data-item-id="{{item.orderItemId}}"
                      data-action="decrease">-</view>
                <text class="quantity-value">{{item.refundQuantity || 0}}</text>
                <view class="quantity-btn {{(item.refundQuantity || 0) >= item.maxRefundQuantity ? 'disabled' : ''}}"
                      bindtap="onQuantityChange"
                      data-item-id="{{item.orderItemId}}"
                      data-action="increase">+</view>
              </view>
            </view>
            <view class="refund-amount-row">
              <text class="refund-amount">退款：¥{{utils.formatAmount(item.refundAmount || 0)}}</text>
            </view>
          </view>
          
          <view wx:else class="item-cannot-refund">
            <text class="cannot-refund-text">该商品不支持退款</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 退款原因 -->
    <view class="refund-reason-section">
      <view class="section-title">退款原因</view>
      <picker bindchange="onReasonChange" value="{{selectedReasonIndex}}" range="{{refundReasons}}" range-key="name">
        <view class="picker-container">
          <text class="picker-text">{{selectedReasonText || '请选择退款原因'}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
    </view>

    <!-- 退款说明 -->
    <view class="refund-description-section">
      <view class="section-title">退款说明</view>
      <textarea class="description-input" 
                placeholder="请详细说明退款原因（选填）" 
                maxlength="200"
                value="{{refundDescription}}"
                bindinput="onDescriptionInput"></textarea>
      <text class="char-count">{{refundDescription.length}}/200</text>
    </view>

   

    <!-- 退款金额 -->
    <view class="refund-amount-section">
      <view class="amount-row">
        <text class="amount-label">退款金额：</text>
        <text class="amount-value">¥{{utils.formatAmount(refundAmount)}}</text>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <button class="submit-btn" bindtap="submitRefundRequest" disabled="{{loading}}">
      提交申请
    </button>
  </view>
</view>
