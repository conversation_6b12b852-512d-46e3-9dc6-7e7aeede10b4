// pages/refund-apply/refund-apply.ts
import RefundService from '../../services/refundService'
import {
  RefundPageData,
  RefundEligibilityResponse,
  RefundReasonOption,
  RefundType,
  RefundReason,
  RefundItem,
  RefundItemForSubmit,
  CreateRefundRequest
} from '../../types/refund'

Page<RefundPageData>({
  data: {
    loading: true,
    orderInfo: null,
    refundReasons: [],
    selectedReason: '' as RefundReason | '',
    selectedReasonIndex: -1,
    selectedReasonText: '',
    refundType: RefundType.REFUND_ONLY,
    refundAmount: 0,
    refundDescription: '',
    evidenceImages: [],
    selectedItems: []
  },

  onLoad(options: any) {
    console.log('退款申请页面加载，参数:', options)
    
    const orderId = parseInt(options.orderId)
    if (!orderId) {
      wx.showToast({
        title: '订单ID无效',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.loadRefundData(orderId)
  },

  /**
   * 加载退款相关数据
   */
  async loadRefundData(orderId: number) {
    try {
      this.setData({ loading: true })

      // 并行加载退款资格和退款原因
      const [eligibilityData, reasons] = await Promise.all([
        RefundService.checkRefundEligibility(orderId),
        RefundService.getRefundReasons()
      ])

      if (!eligibilityData.canRefund) {
        wx.showModal({
          title: '无法申请退款',
          content: eligibilityData.reason || '该订单不符合退款条件',
          showCancel: false,
          success: () => {
            wx.navigateBack()
          }
        })
        return
      }

      console.log('退款资格检查数据:', eligibilityData)

      // 解析收货地址
      let shippingAddress = null
      try {
        if (eligibilityData.order.shippingAddressSnapshot) {
          shippingAddress = JSON.parse(eligibilityData.order.shippingAddressSnapshot)
        }
      } catch (error) {
        console.error('解析收货地址失败:', error)
      }

      // 订单状态映射为中文
      const getOrderStatusText = (status: string): string => {
        const statusMap: { [key: string]: string } = {
          'pending_payment': '待支付',
          'paid_shipped': '待发货',
          'shipped': '待收货'
        }
        return statusMap[status] || status
      }

      // 转换为页面需要的订单信息格式（金额转换为分）
      const orderInfo = {
        orderId: eligibilityData.order.id,
        orderNumber: eligibilityData.order.orderNumber,
        orderStatus: eligibilityData.order.status,
        orderStatusText: getOrderStatusText(eligibilityData.order.status),
        totalAmount: Math.round(eligibilityData.order.totalAmount * 100), // 元转分
        items: eligibilityData.refundableItems.map(item => ({
          orderItemId: item.orderItemId,
          productName: item.productName,
          quantity: item.quantity,
          price: Math.round(item.unitPrice * 100), // 元转分
          canRefund: true,
          maxRefundQuantity: item.availableQuantity,
          refundQuantity: item.availableQuantity, // 默认全部退款
          refundAmount: Math.round(item.unitPrice * item.availableQuantity * 100), // 元转分
          productImageUrl: item.productImageUrl || ''
        }))
      }

      // 初始化选中的商品项（金额转换为分）
      const selectedItems: RefundItem[] = eligibilityData.refundableItems.map(item => ({
        orderItemId: item.orderItemId,
        refundQuantity: item.availableQuantity,
        refundAmount: Math.round(item.unitPrice * item.availableQuantity * 100) // 元转分
      }))

      // 计算总退款金额（已经是分为单位）
      const totalRefundAmount = selectedItems.reduce((sum, item) => sum + item.refundAmount, 0)

      // 根据订单状态确定退款类型
      let finalRefundType = eligibilityData.suggestedRefundType

      // 确保退款类型与订单状态匹配
      if (orderInfo.orderStatus === 'paid_shipped') {
        finalRefundType = RefundType.REFUND_ONLY
      } else if (orderInfo.orderStatus === 'shipped' || orderInfo.orderStatus === 'completed') {
        finalRefundType = RefundType.RETURN_REFUND
      }

      this.setData({
        orderInfo,
        refundReasons: reasons,
        refundType: finalRefundType,
        refundAmount: totalRefundAmount,
        selectedItems,
        loading: false
      })

      console.log('页面数据设置完成:', {
        orderInfo,
        refundType: eligibilityData.suggestedRefundType,
        refundAmount: totalRefundAmount,
        selectedItems
      })

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '申请退款'
      })

    } catch (error: any) {
      console.error('加载退款数据失败:', error)
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'error'
      })
      this.setData({ loading: false })
    }
  },

  /**
   * 选择退款类型
   */
  onRefundTypeChange(e: any) {
    const refundType = e.detail.value as RefundType
    this.setData({ refundType })
  },

  /**
   * 选择退款原因
   */
  onReasonChange(e: any) {
    const index = e.detail.value
    const selectedReasonItem = this.data.refundReasons[index]
    console.log('选择的退款原因:', selectedReasonItem)
    if (selectedReasonItem) {
      console.log('退款原因代码:', selectedReasonItem.code)
      this.setData({
        selectedReason: selectedReasonItem.code as RefundReason,
        selectedReasonIndex: index,
        selectedReasonText: selectedReasonItem.name
      })
    }
  },

  /**
   * 输入退款说明
   */
  onDescriptionInput(e: any) {
    this.setData({
      refundDescription: e.detail.value
    })
  },

  /**
   * 修改商品退款数量
   */
  onQuantityChange(e: any) {
    const { itemId, action } = e.currentTarget.dataset
    const { selectedItems, orderInfo } = this.data

    if (!orderInfo) return

    const orderItem = orderInfo.items.find(item => item.orderItemId === parseInt(itemId))
    if (!orderItem) return

    // 检查是否为禁用状态
    const currentQuantity = orderItem.refundQuantity || 0
    if (action === 'decrease' && currentQuantity <= 0) {
      return // 已经是最小值，不能再减少
    }
    if (action === 'increase' && currentQuantity >= orderItem.maxRefundQuantity) {
      return // 已经是最大值，不能再增加
    }

    // 计算新数量
    let newQuantity = currentQuantity
    if (action === 'increase') {
      newQuantity = Math.min(newQuantity + 1, orderItem.maxRefundQuantity)
    } else if (action === 'decrease') {
      newQuantity = Math.max(newQuantity - 1, 0)
    }

    // 更新选中商品列表
    const updatedItems = selectedItems.map(item => {
      if (item.orderItemId === parseInt(itemId)) {
        return {
          ...item,
          refundQuantity: newQuantity,
          refundAmount: orderItem.price * newQuantity
        }
      }
      return item
    })

    // 更新订单信息中的退款数量和金额
    const updatedOrderInfo = {
      ...orderInfo,
      items: orderInfo.items.map(item => {
        if (item.orderItemId === parseInt(itemId)) {
          return {
            ...item,
            refundQuantity: newQuantity,
            refundAmount: item.price * newQuantity
          }
        }
        return item
      })
    }

    // 重新计算总退款金额
    const totalAmount = updatedItems.reduce((sum, item) => sum + item.refundAmount, 0)

    this.setData({
      selectedItems: updatedItems,
      orderInfo: updatedOrderInfo,
      refundAmount: totalAmount
    })
  },

  /**
   * 选择证据图片
   */
  async chooseEvidenceImages() {
    try {
      const { evidenceImages } = this.data
      const remainingCount = 9 - evidenceImages.length

      if (remainingCount <= 0) {
        wx.showToast({
          title: '最多上传9张图片',
          icon: 'none'
        })
        return
      }

      const res = await wx.chooseMedia({
        count: remainingCount,
        mediaType: ['image'],
        sourceType: ['album', 'camera']
      })

      // TODO: 上传图片到服务器，这里暂时使用本地路径
      const newImages = res.tempFiles.map(file => file.tempFilePath)
      
      this.setData({
        evidenceImages: [...evidenceImages, ...newImages]
      })

    } catch (error) {
      console.error('选择图片失败:', error)
    }
  },

  /**
   * 删除证据图片
   */
  removeEvidenceImage(e: any) {
    const index = e.currentTarget.dataset.index
    const { evidenceImages } = this.data
    
    evidenceImages.splice(index, 1)
    this.setData({ evidenceImages })
  },

  /**
   * 预览证据图片
   */
  previewEvidenceImage(e: any) {
    const index = e.currentTarget.dataset.index
    const { evidenceImages } = this.data
    
    wx.previewImage({
      current: evidenceImages[index],
      urls: evidenceImages
    })
  },

  /**
   * 提交退款申请
   */
  async submitRefundRequest() {
    try {
      // 验证表单
      if (!this.validateForm()) {
        return
      }

      wx.showLoading({ title: '提交中...' })

      const { orderInfo, refundType, selectedReason, refundDescription, refundAmount, selectedItems, evidenceImages } = this.data

      if (!orderInfo) {
        throw new Error('订单信息不存在')
      }

      // 退款原因映射 - 处理前后端不一致的问题
      let mappedRefundReason = selectedReason
      if (selectedReason === 'seven_days_no_reason') {
        mappedRefundReason = 'change_mind' // 映射为"不想要了"
        console.log('退款原因映射: seven_days_no_reason -> change_mind')
      }

      // 转换退款商品明细（分转元）
      const refundItemsForSubmit: RefundItemForSubmit[] = selectedItems
        .filter(item => item.refundQuantity > 0)
        .map(item => ({
          orderItemId: item.orderItemId,
          refundQuantity: item.refundQuantity,
          refundAmount: item.refundAmount / 100 // 分转元
        }))

      const request: CreateRefundRequest = {
        orderId: orderInfo.orderId,
        refundType,
        refundReason: mappedRefundReason as RefundReason,
        refundDescription,
        refundAmount: refundAmount / 100, // 分转元
        refundItems: refundItemsForSubmit,
        evidenceImages
      }

      console.log('准备提交的退款申请数据:', request)
      console.log('退款原因详细信息:', {
        originalReason: selectedReason,
        mappedReason: mappedRefundReason,
        finalReason: mappedRefundReason as RefundReason,
        type: typeof mappedRefundReason
      })

      const result = await RefundService.createRefundRequest(request)

      wx.hideLoading()
      
      wx.showModal({
        title: '申请成功',
        content: `退款申请已提交，申请单号：${result.refundNumber}`,
        showCancel: false,
        success: () => {
          // 跳转到退款详情页面
          wx.redirectTo({
            url: `/pages/refund-detail/refund-detail?refundId=${result.id}`
          })
        }
      })

    } catch (error: any) {
      wx.hideLoading()
      console.error('提交退款申请失败:', error)
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'error'
      })
    }
  },

  /**
   * 验证表单
   */
  validateForm(): boolean {
    const { selectedReason, refundAmount, selectedItems } = this.data

    if (!selectedReason) {
      wx.showToast({
        title: '请选择退款原因',
        icon: 'none'
      })
      return false
    }

    if (refundAmount <= 0) {
      wx.showToast({
        title: '退款金额必须大于0',
        icon: 'none'
      })
      return false
    }

    const validItems = selectedItems.filter(item => item.refundQuantity > 0)
    if (validItems.length === 0) {
      wx.showToast({
        title: '请选择要退款的商品',
        icon: 'none'
      })
      return false
    }

    return true
  }
})
