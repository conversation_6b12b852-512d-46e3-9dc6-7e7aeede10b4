/* pages/refund-apply/refund-apply.wxss */
.refund-apply-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #999;
  font-size: 28rpx;
}

/* 内容区域 */
.content {
  padding: 16rpx;
  box-sizing: border-box;
}

/* 通用区块样式 */
.order-section,
.refund-type-section,
.refund-items-section,
.refund-reason-section,
.refund-description-section,
.evidence-section,
.refund-amount-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

/* 订单信息 */
.order-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-item .label {
  color: #666;
  font-size: 28rpx;
}

.order-item .value {
  color: #333;
  font-size: 28rpx;
}

.order-item .value.amount {
  color: #ff3b30;
  font-weight: 600;
}

/* 退款类型 */
.radio-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
}

.radio-item radio {
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.radio-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.radio-desc {
  color: #999;
  margin-left: 40rpx;
}

/* 退款类型提示 */
.refund-type-notice {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.notice-text {
  font-size: 26rpx;
  color: #6c757d;
  text-align: center;
}

/* 退款商品 */
.items-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.item-card {
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  padding: 16rpx;
  box-sizing: border-box;
  width: 100%;
}

.item-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.item-price {
  font-size: 26rpx;
  color: #ff3b30;
  font-weight: 600;
}

.item-quantity {
  font-size: 24rpx;
  color: #999;
}

.item-refund {
  border-top: 1rpx solid #eee;
  padding-top: 16rpx;
  width: 100%;
  box-sizing: border-box;
  padding-left: 8rpx;
  padding-right: 8rpx;
}

.quantity-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
  width: 100%;
  box-sizing: border-box;
  padding: 0 4rpx;
}

.quantity-label {
  font-size: 26rpx;
  color: #666;
  flex: 0 0 auto;
  margin-right: 12rpx;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 4rpx;
  flex: 0 0 auto;
  width: 120rpx;
  justify-content: space-between;
}

.quantity-btn {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 4rpx;
  background-color: #fff;
  font-size: 20rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  line-height: 1;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
}

/* 按钮点击效果 */
.quantity-btn:active {
  background-color: #f0f0f0;
  border-color: #bbb;
  transform: scale(0.95);
}

/* 按钮禁用状态 */
.quantity-btn.disabled {
  background-color: #f5f5f5;
  color: #ccc;
  border-color: #eee;
  cursor: not-allowed;
}

.quantity-value {
  font-size: 24rpx;
  color: #333;
  width: 32rpx;
  text-align: center;
  flex-shrink: 0;
}

.refund-amount-row {
  margin-top: 12rpx;
  text-align: right;
}

.refund-amount {
  font-size: 26rpx;
  color: #ff3b30;
  font-weight: 600;
}

.item-cannot-refund {
  border-top: 1rpx solid #eee;
  padding-top: 16rpx;
  text-align: center;
}

.cannot-refund-text {
  font-size: 26rpx;
  color: #999;
}

/* 退款原因选择器 */
.picker-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  background-color: #fafafa;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 退款说明 */
.description-input {
  width: 100%;
  /* min-height: 200rpx; */
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
  box-sizing: border-box;
}

.char-count {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 8rpx;
}

/* 证据图片 */
.images-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
}

.evidence-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.image-delete {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff3b30;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

.add-image-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.add-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 24rpx;
  color: #999;
}

.image-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 退款金额 */
.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.amount-label {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.amount-value {
  font-size: 36rpx;
  color: #ff3b30;
  font-weight: 700;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn[disabled] {
  background-color: #ccc;
  color: #999;
}
