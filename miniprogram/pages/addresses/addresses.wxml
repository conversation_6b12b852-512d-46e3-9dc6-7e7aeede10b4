<!--pages/addresses/addresses.wxml-->
<view class="container">
  <!-- Loading State -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- Address List -->
  <view wx:else class="address-list">
    <!-- Empty State -->
    <view wx:if="{{addresses.length === 0}}" class="empty-state">
      <image class="empty-icon" src="/images/empty-address.png" mode="aspectFit"></image>
      <text class="empty-text">暂无收货地址</text>
      <text class="empty-desc">添加收货地址，享受便捷购物体验</text>
      <view class="add-first-btn" bindtap="addAddress">
        <text>添加收货地址</text>
      </view>
    </view>

    <!-- Address Items -->
    <view wx:else>
      <view
        class="address-item {{item.isDefault ? 'default-address' : ''}}"
        wx:for="{{addresses}}"
        wx:key="id"
        bindtap="selectAddress"
        data-id="{{item.id}}"
      >
        <!-- Address Info -->
        <view class="address-info">
          <view class="address-header">
            <text class="recipient-name">{{item.recipientName}}</text>
            <text class="recipient-phone">{{item.phoneNumber}}</text>
            <view wx:if="{{item.isDefault}}" class="default-badge">默认</view>
          </view>

          <view class="address-detail">
            <text class="address-text">{{item.regionProvince}} {{item.regionCity}} {{item.regionDistrict}} {{item.streetAddress}}</text>
          </view>
        </view>

        <!-- Action Buttons -->
        <view wx:if="{{!selectMode}}" class="address-actions">
          <view class="action-btn" bindtap="editAddress" data-id="{{item.id}}" catchtap>
            <image class="action-icon" src="/images/icons/edit.png"></image>
            <text class="action-text">编辑</text>
          </view>

          <view wx:if="{{!item.isDefault}}" class="action-btn" bindtap="setDefaultAddress" data-id="{{item.id}}" catchtap>
            <image class="action-icon" src="/images/icons/default.png"></image>
            <text class="action-text">设为默认</text>
          </view>

          <view class="action-btn delete-btn" bindtap="deleteAddress" data-id="{{item.id}}" catchtap>
            <image class="action-icon" src="/images/icons/delete.png"></image>
            <text class="action-text">删除</text>
          </view>
        </view>

        <!-- Select Mode Indicator -->
        <view wx:if="{{selectMode}}" class="select-indicator">
          <text class="select-text">点击选择</text>
          <text class="select-arrow">></text>
        </view>
      </view>
    </view>
  </view>

  <!-- Add Address Button -->
  <view wx:if="{{addresses.length > 0}}" class="add-address-btn" bindtap="addAddress">
    <image class="add-icon" src="/images/icons/add.png"></image>
    <text class="add-text">新增收货地址</text>
  </view>
</view>
