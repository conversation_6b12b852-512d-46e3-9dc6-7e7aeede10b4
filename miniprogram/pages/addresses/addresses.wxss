/* pages/addresses/addresses.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  padding-top: 0;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  padding: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #6b7280;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  opacity: 0.6;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #374151;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.add-first-btn {
  background-color: #000000;
  color: #ffffff;
  padding: 24rpx 48rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* Address List */
.address-list {
  padding: 24rpx;
  width: 96%;
}

.address-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #f3f4f6;
  position: relative;
}

/* 默认地址样式增强 */
.address-item.default-address {
  border: 2rpx solid #ef4444;
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.15);
}

.address-item.default-address::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #ef4444 0%, #f87171 100%);
  border-radius: 16rpx 16rpx 0 0;
}

/* Address Info */
.address-info {
  margin-bottom: 24rpx;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.recipient-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-right: 24rpx;
}

.recipient-phone {
  font-size: 28rpx;
  color: #6b7280;
  flex: 1;
}

.default-badge {
  background-color: #ef4444;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.address-detail {
  margin-top: 8rpx;
}

.address-text {
  font-size: 28rpx;
  color: #4b5563;
  line-height: 1.5;
}

/* Address Actions */
.address-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f3f4f6;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  background-color: #f9fafb;
  border: 1rpx solid #e5e7eb;
  min-width: 120rpx;
}

.action-btn.delete-btn {
  background-color: #fef2f2;
  border-color: #fecaca;
}

.action-icon {
  width: 28rpx;
  height: 28rpx;
}

.action-text {
  font-size: 24rpx;
  color: #374151;
  text-align: center;
  white-space: nowrap;
}

.delete-btn .action-text {
  color: #dc2626;
}

/* Add Address Button */
.add-address-btn {
  position: fixed;
  bottom: 40rpx;
  left: 24rpx;
  right: 24rpx;
  background-color: #000000;
  color: #ffffff;
  padding: 24rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.add-icon {
  width: 32rpx;
  height: 32rpx;
}

.add-text {
  font-size: 32rpx;
  font-weight: 500;
}

/* Select Mode */
.select-indicator {
  display: flex;
  align-items: center;
  padding: 0 24rpx;
}

.select-text {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

.select-arrow {
  font-size: 24rpx;
  color: #999;
}
