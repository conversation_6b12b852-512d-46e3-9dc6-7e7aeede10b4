// pages/addresses/addresses.ts
import AddressService from '../../services/addressService'
import { Address } from '../../types/address'

Page({
  data: {
    addresses: [] as Address[],
    loading: false,
    selectMode: false  // 是否为选择模式
  },

  onLoad(options: any) {
    // 检查是否为选择模式
    if (options.mode === 'select') {
      this.setData({ selectMode: true })
      wx.setNavigationBarTitle({
        title: '选择收货地址'
      })
    }

    this.loadAddresses()
  },

  onShow() {
    // 页面显示时重新加载地址列表，以防从编辑页面返回后数据有变化
    this.loadAddresses()
  },

  onPullDownRefresh() {
    this.loadAddresses()
    wx.stopPullDownRefresh()
  },

  /**
   * 加载地址列表
   */
  async loadAddresses() {
    this.setData({ loading: true })

    try {
      // 使用排序后的地址列表，默认地址显示在最前面
      const addresses = await AddressService.getSortedAddresses()
      console.log('加载地址列表:', addresses)

      this.setData({
        addresses,
        loading: false
      })
    } catch (error) {
      console.error('加载地址列表失败:', error)
      this.setData({ loading: false })

      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 新增地址
   */
  addAddress() {
    wx.navigateTo({
      url: '/pages/address-edit/address-edit'
    })
  },

  /**
   * 编辑地址
   */
  editAddress(e: any) {
    const addressId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/address-edit/address-edit?id=${addressId}`
    })
  },

  /**
   * 设置默认地址
   */
  async setDefaultAddress(e: any) {
    const addressId = e.currentTarget.dataset.id

    wx.showLoading({
      title: '设置中...'
    })

    try {
      const result = await AddressService.setDefaultAddress(addressId)
      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: result.message,
          icon: 'success'
        })

        // 重新加载地址列表
        this.loadAddresses()
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('设置默认地址失败:', error)

      wx.showToast({
        title: '设置失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 删除地址
   */
  deleteAddress(e: any) {
    const addressId = e.currentTarget.dataset.id
    const address = this.data.addresses.find(addr => addr.id === addressId)

    if (!address) {
      wx.showToast({
        title: '地址不存在',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '删除地址',
      content: `确定要删除"${address.recipientName}"的收货地址吗？`,
      confirmText: '删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.performDeleteAddress(addressId)
        }
      }
    })
  },

  /**
   * 执行删除地址操作
   */
  async performDeleteAddress(addressId: number) {
    wx.showLoading({
      title: '删除中...'
    })

    try {
      const result = await AddressService.deleteAddress(addressId)
      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: result.message,
          icon: 'success'
        })

        // 重新加载地址列表
        this.loadAddresses()
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('删除地址失败:', error)

      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 选择地址（用于订单页面选择地址）
   */
  selectAddress(e: any) {
    const addressId = e.currentTarget.dataset.id
    const address = this.data.addresses.find(addr => addr.id === addressId)

    if (address) {
      // 通过页面栈传递选中的地址ID
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 2]

      if (prevPage) {
        prevPage.setData({ selectedAddressId: address.id })
      }

      wx.navigateBack()
    }
  },



  /**
   * 分享
   */
  onShareAppMessage() {
    return {
      title: '收货地址管理',
      path: '/pages/addresses/addresses'
    }
  }
})
