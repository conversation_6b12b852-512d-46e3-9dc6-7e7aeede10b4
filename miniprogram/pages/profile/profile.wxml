<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- User Header -->
  <view class="user-header">
    <view class="user-info">
      <!-- 头像区域 -->
      <view class="avatar-section">
        <button class="avatar-btn"
                open-type="chooseAvatar"
                bindchooseavatar="onChooseAvatar">
          <image class="avatar" src="{{userInfo.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
          <!-- <view class="avatar-mask">
            <text class="avatar-tip">点击更换</text>
          </view> -->
        </button>
      </view>

      <view class="user-details">
        <!-- 未登录状态 -->
        <view wx:if="{{!isLoggedIn}}" class="login-tip">
          <text class="login-text" bindtap="wechatLogin">点击登录，享受完整功能！</text>
        </view>

        <!-- 已登录状态 -->
        <view wx:else>
          <!-- 昵称区域 -->
          <view class="nickname-section">
            <view wx:if="{{userInfo.nickname === '完善信息'}}" class="nickname-input-container">
              <input name="nickname"
                     type="nickname"
                     class="nickname-input"
                     placeholder="请输入昵称"
                     value="{{inputNickname}}"
                     bindinput="onNicknameInput"
                     bindblur="onNicknameBlur"
                     bindconfirm="onNicknameConfirm" />
            </view>
            <text wx:else class="username">{{userInfo.nickname || '用户昵称'}}</text>
          </view>

          <!-- 手机号显示区域 -->
          <view class="phone-section">
            <view wx:if="{{userInfo.hasPhoneNumber}}" class="phone-info">
              <text class="phone">手机号: {{userInfo.phone}}</text>
            </view>
            <button wx:else
                    class="get-phone-btn"
                    open-type="getPhoneNumber"
                    bindgetphonenumber="getPhoneNumber">
              完善手机
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- Order Status -->
  <view class="order-section">
    <view class="section-header">
      <text class="section-title">我的订单</text>
      <view class="more-link" bindtap="goToOrders">
        <text>全部订单</text>
        <text class="arrow">></text>
      </view>
    </view>

    <view class="order-status-grid">
      <view class="order-status-item" bindtap="goToOrders" data-status="pending">
        <view class="status-icon">
          <view wx:if="{{orderCounts.pending_payment > 0}}" class="badge">{{orderCounts.pending_payment}}</view>
          <image src="/images/icons/payment.png"></image>
        </view>
        <text class="status-text">待付款</text>
      </view>

      <view class="order-status-item" bindtap="goToOrders" data-status="paid_shipped">
        <view class="status-icon">
          <view wx:if="{{orderCounts.paid_shipped > 0}}" class="badge">{{orderCounts.paid_shipped}}</view>
          <image src="/images/icons/processing.png"></image>
        </view>
        <text class="status-text">待发货</text>
      </view>

      <view class="order-status-item" bindtap="goToOrders" data-status="shipped">
        <view class="status-icon">
          <view wx:if="{{orderCounts.shipped > 0}}" class="badge">{{orderCounts.shipped}}</view>
          <image src="/images/icons/shipped.png"></image>
        </view>
        <text class="status-text">待收货</text>
      </view>

      <view class="order-status-item" bindtap="goToRefunds">
        <view class="status-icon">
          <image src="/images/icons/refund.png"></image>
        </view>
        <text class="status-text">退款/售后</text>
      </view>
    </view>
  </view>

  <!-- Invitation Section -->
  <view wx:if="{{isLoggedIn}}" class="invitation-section">
    <!-- 新版邀请奖励组件 -->
    <invitation-rewards
      hasUsedInvitationCode="{{invitationData.hasUsedInvitationCode}}"
      totalRewardAmount="{{invitationData.totalRewardAmount}}"
      invitationStats="{{invitationData.stats}}"
      bind:fillCodeReward="onFillCodeReward"
      bind:inviteReward="onInviteReward"
      bind:registerReward="onRegisterReward"
      bind:shareReward="onShareReward">
    </invitation-rewards>
  </view>

  <!-- My Services -->
  <view class="services-section">
    <text class="section-title">我的服务</text>

    <view class="services-grid">
      <view class="service-item" bindtap="goToAddresses">
        <view class="service-icon">
          <image src="/images/icons/address.png"></image>
        </view>
        <text class="service-text">收货地址</text>
      </view>

      <view class="service-item" bindtap="goToCustomerService">
        <view class="service-icon">
          <image src="/images/icons/customer-service.png"></image>
        </view>
        <text class="service-text">联系客服</text>
      </view>

      <view class="service-item" bindtap="goToHelp">
        <view class="service-icon">
          <image src="/images/icons/help.png"></image>
        </view>
        <text class="service-text">帮助中心</text>
      </view>

      <view class="service-item" bindtap="goToSettings">
        <view class="service-icon">
          <image src="/images/icons/settings.png"></image>
        </view>
        <text class="service-text">设置中心</text>
      </view>
    </view>
  </view>
</view>
