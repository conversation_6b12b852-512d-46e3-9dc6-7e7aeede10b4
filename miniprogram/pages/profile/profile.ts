// pages/profile/profile.ts
import AuthManager from '../../utils/auth'
import OrderService from '../../services/orderService'
import InvitationService from '../../services/invitationService'


Page({
  data: {
    userInfo: {
      nickname: '用户昵称',
      phone: '138****8888',
      avatar: '/images/default-avatar.png',
      hasPhoneNumber: false
    },
    prescriptionCount: 2,
    isLoggedIn: false,
    inputNickname: '',         // 输入的昵称
    orderCounts: {
      pending_payment: 0,
      paid_shipped: 0,
      shipped: 0,
      completed: 0,
      cancelled: 0,
      refunded: 0,
      total: 0
    },
    // 邀请相关数据
    invitationData: {
      hasUsedInvitationCode: false,
      totalRewardAmount: 0,
      stats: {}
    }
  },

  onLoad() {
    // 页面加载时进行初始化
    this.checkLoginAndGetUserInfo()
  },

  onShow() {
    // 页面显示时检查登录状态是否有变化
    // 如果用户在其他地方登录/退出，这里需要更新状态
    this.refreshLoginStatus()

    // 如果已登录，加载订单统计和邀请数据
    if (this.data.isLoggedIn) {
      this.loadOrderCounts()
      this.loadInvitationData()
    }
  },

  async checkLoginAndGetUserInfo() {
    // 检查登录状态
    if (!AuthManager.isLoggedIn()) {
      // 未登录，显示默认头像并弹出登录提示
      this.setData({
        isLoggedIn: false,
        userInfo: {
          nickname: '用户昵称',
          phone: '138****8888', // 未登录时显示示例脱敏手机号
          avatar: '/images/default-avatar.png', // 未登录时使用默认头像
          hasPhoneNumber: false
        }
      })
      this.showLoginModal()
      return
    }

    // 已登录，获取用户信息（会优先使用本地头像）
    this.setData({ isLoggedIn: true })
    await this.getUserInfo()
  },

  async refreshLoginStatus() {
    // 静默检查登录状态，不弹出登录框
    const isLoggedIn = AuthManager.isLoggedIn()
    const currentLoginStatus = this.data.isLoggedIn

    // 只有登录状态发生变化时才更新
    if (isLoggedIn !== currentLoginStatus) {
      this.setData({ isLoggedIn })

      if (isLoggedIn) {
        // 用户在其他地方登录了，更新用户信息（会优先使用本地头像）
        await this.getUserInfo()
      } else {
        // 用户在其他地方退出了，重置为默认头像
        this.setData({
          userInfo: {
            nickname: '用户昵称',
            phone: '138****8888',
            avatar: '/images/default-avatar.png', // 退出登录使用默认头像
            hasPhoneNumber: false
          }
        })
        console.log('检测到退出登录，已切换到默认头像')
      }
    }
  },

  showLoginModal() {
    wx.showModal({
      title: '登录提示',
      content: '请先登录以获取完整功能',
      confirmText: '立即登录',
      showCancel: false, // 不显示取消按钮，强制登录
      success: (res) => {
        if (res.confirm) {
          // 用户点击立即登录，触发微信授权
          this.wechatLogin()
        }
      }
    })
  },

  async wechatLogin() {
    try {
      const result = await AuthManager.wechatLogin()

      if (result.success) {
        // 登录成功后刷新页面数据，优先使用本地头像
        this.setData({ isLoggedIn: true })
        await this.getUserInfo()

        console.log('登录成功，页面数据已更新')
      }
      // 错误提示已经在 AuthManager.wechatLogin() 中处理了，这里不需要重复处理
    } catch (error: any) {
      console.error('页面登录调用失败:', error)
      // 错误提示已经在 AuthManager.wechatLogin() 中处理了
    }
  },



  /**
   * 手机号脱敏处理：显示前3位和后4位，中间用****替代
   */
  maskPhoneNumber(phone: string): string {
    if (!phone || phone.length !== 11) {
      return phone
    }
    return phone.substring(0, 3) + '****' + phone.substring(7)
  },

  async getUserInfo() {
    // 从AuthManager获取用户信息
    const userInfo = AuthManager.getUserInfo()
    if (userInfo) {
      const phone = userInfo.phone || userInfo.phoneNumber || ''
      const hasPhoneNumber = !!(phone && phone.trim())

      // 获取头像路径（优先使用本地缓存）
      const avatarPath = await AuthManager.getUserAvatarPath()

      this.setData({
        userInfo: {
          nickname: userInfo.nickName || '完善信息',
          phone: hasPhoneNumber ? this.maskPhoneNumber(phone) : phone, // 对手机号进行脱敏处理
          avatar: avatarPath,
          hasPhoneNumber: hasPhoneNumber
        }
      })
    }
  },

  /**
   * 选择头像回调（新方案）
   */
  async onChooseAvatar(e: any) {
    console.log('选择头像事件:', e)

    const { avatarUrl } = e.detail
    if (avatarUrl) {
      try {
        // 先临时显示新头像
        this.setData({
          'userInfo.avatar': avatarUrl
        })

        // 下载并保存头像到本地
        await AuthManager.updateUserAvatar(avatarUrl)

        wx.showToast({
          title: '头像保存成功',
          icon: 'success',
          duration: 2000
        })

        // 重新获取用户信息，确保显示本地头像路径
        if (AuthManager.isLoggedIn()) {
          await this.getUserInfo()
        }

      } catch (error) {
        console.error('保存头像失败:', error)

        wx.showToast({
          title: '头像保存失败，但已临时更新',
          icon: 'none',
          duration: 3000
        })
      }
    }
  },

  /**
   * 昵称输入事件
   */
  onNicknameInput(e: any) {
    const inputValue = e.detail.value
    this.setData({
      inputNickname: inputValue
    })
  },

  /**
   * 昵称失去焦点事件（自动保存）
   */
  onNicknameBlur(e: any) {
    const nickName = e.detail.value
    this.saveNickname(nickName)
  },

  /**
   * 昵称确认事件（回车键，自动保存）
   */
  onNicknameConfirm(e: any) {
    const nickName = e.detail.value
    this.saveNickname(nickName)
  },

  /**
   * 保存昵称的通用方法
   */
  async saveNickname(nickName: string) {
    if (nickName && nickName.trim()) {
      // 检查是否有变化
      const currentNickname = this.data.userInfo.nickname
      if (nickName.trim() === currentNickname) {
        return // 没有变化，不需要保存
      }

      // 显示加载提示
      wx.showLoading({
        title: '更新中...'
      })

      try {
        // 调用服务端接口更新昵称
        const result = await AuthManager.updateUserNickname(nickName.trim())

        wx.hideLoading()

        if (result.success) {
          // 更新页面显示
          this.setData({
            'userInfo.nickname': nickName.trim(),
            inputNickname: ''
          })

          wx.showToast({
            title: result.message || '昵称更新成功',
            icon: 'success',
            duration: 1500
          })
        } else {
          // 更新失败，恢复输入框状态
          this.setData({
            inputNickname: ''
          })

          wx.showToast({
            title: result.message || '昵称更新失败',
            icon: 'none',
            duration: 2000
          })
        }
      } catch (error: any) {
        wx.hideLoading()

        // 更新失败，恢复输入框状态
        this.setData({
          inputNickname: ''
        })

        wx.showToast({
          title: '昵称更新失败，请重试',
          icon: 'none',
          duration: 2000
        })
      }
    } else {
      // 如果输入为空，恢复到原来的状态
      this.setData({
        inputNickname: ''
      })

      wx.showToast({
        title: '昵称不能为空',
        icon: 'none',
        duration: 1500
      })
    }
  },



  /**
   * 获取手机号授权回调
   */
  getPhoneNumber(e: any) {
    console.log('getPhoneNumber event:', e)

    if (e.detail.code) {
      // 用户同意授权，获取到code
      this.requestPhoneNumber(e.detail.code)
    } else {
      // 用户拒绝授权
      wx.showToast({
        title: '需要手机号授权才能继续',
        icon: 'none',
        duration: 2000
      })
    }
  },

  /**
   * 调用后端接口获取手机号
   */
  async requestPhoneNumber(code: string) {
    wx.showLoading({
      title: '获取手机号中...'
    })

    try {
      const result = await AuthManager.getPhoneNumber(code)

      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: result.message,
          icon: 'success'
        })

        // 更新页面显示
        this.getUserInfo()
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none',
          duration: 2000
        })
      }
    } catch (error: any) {
      wx.hideLoading()
      wx.showToast({
        title: '获取手机号失败，请重试',
        icon: 'none'
      })
    }
  },

  goToOrders(e?: any) {
    const status = (e && e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.status) || ''
    const url = status ? `/pages/orders/orders?status=${status}` : '/pages/orders/orders'

    wx.navigateTo({
      url
    })
  },

  goToRefunds() {
    wx.navigateTo({
      url: '/pages/orders/orders?status=refund_afterSale'
    })
  },


  goToAddresses() {
    wx.navigateTo({
      url: '/pages/addresses/addresses'
    })
  },

  goToCustomerService() {
    wx.switchTab({
      url: '/pages/service/service'
    })
  },

  goToHelp() {
    wx.navigateTo({
      url: '/pages/help/help'
    })
  },

  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  /**
   * 加载邀请相关数据
   */
  async loadInvitationData() {
    try {
      // 检查用户是否已使用邀请码
      const hasUsedCode = await InvitationService.checkInvitationCodeUsedStatus()

      // 获取邀请奖励数据
      const rewardsResult = await InvitationService.getMyInvitationRewards(1, 100)
      const totalRewardAmount = rewardsResult.totalRewardAmount || 0

      // 获取邀请统计
      const stats = await InvitationService.getInvitationStats()

      this.setData({
        'invitationData.hasUsedInvitationCode': hasUsedCode,
        'invitationData.totalRewardAmount': totalRewardAmount,
        'invitationData.stats': stats
      })

    } catch (error) {
      console.error('加载邀请数据失败:', error)
    }
  },

  // 跳转到新版邀请领现金页面
  goToInvitationCashNew() {
    wx.navigateTo({
      url: '/pages/invitation-cash-new/invitation-cash-new'
    })
  },

  /**
   * 填写邀请码奖励事件处理
   */
  onFillCodeReward() {
    // 跳转到邀请页面的填写邀请码部分
    this.goToInvitationCashNew()
  },

  /**
   * 邀请奖励事件处理
   */
  onInviteReward() {
    // 跳转到邀请页面
    this.goToInvitationCashNew()
  },

  /**
   * 邀请注册奖励事件处理
   */
  onRegisterReward() {
    // 跳转到邀请页面
    this.goToInvitationCashNew()
  },

  /**
   * 分享奖励事件处理
   */
  onShareReward() {
    // 跳转到邀请页面
    this.goToInvitationCashNew()
  },

  /**
   * 加载订单统计数据
   */
  async loadOrderCounts() {
    try {
      const counts = await OrderService.getOrderCounts()
      this.setData({
        orderCounts: counts
      })
    } catch (error) {
      console.error('加载订单统计失败:', error)
    }
  },

  /**
   * 跳转到订单页面
   */
  goToOrders(e: any) {
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    const dataset = e.currentTarget.dataset
    let status = ''

    // 根据点击的状态确定跳转参数
    if (dataset.status) {
      switch (dataset.status) {
        case 'pending':
          status = 'pending_payment'
          break
        case 'paid_shipped':
          status = 'paid_shipped'
          break
        case 'shipped':
          status = 'shipped'
          break
        default:
          status = ''
      }
    }

    // 跳转到订单页面
    wx.navigateTo({
      url: `/pages/orders/orders${status ? '?status=' + status : ''}`
    })
  },


  onShareAppMessage() {
    return {
      title: '我的',
      path: '/pages/profile/profile'
    }
  },

  
})
