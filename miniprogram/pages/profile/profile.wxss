/* pages/profile/profile.wxss */
.container {
  padding-bottom: 32rpx;
  padding-top: 10rpx;
  width: 100%;
}

/* User Header */
.user-header {
  background: linear-gradient(135deg, rgb(60, 55, 50) 0%, rgb(75, 70, 70, 0.8) 100%);
  padding: 56rpx 24rpx 56rpx; /* 对称上下内边距，便于在灰色背景内视觉居中 */
  position: relative;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar {
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  border: 4rpx solid #ffffff;
  display: block;
  object-fit: cover;
  position: absolute;
  top: 0;
}

.user-details {
  color: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: center; /* 垂直居中对齐 */
  height: 120rpx; /* 与头像高度保持一致，确保垂直居中 */
}

.username-container {
  margin-bottom: 8rpx;
}

.username {
  font-size: 36rpx;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.complete-info {
  color: #fbbf24;
  text-decoration: underline;
  cursor: pointer;
}

.complete-tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  display: block;
  margin-top: 4rpx;
}

.phone {
  font-size: 28rpx;
  opacity: 0.9;
}

.login-tip {
  margin-top: 8rpx;
}

.login-text {
  font-size: 28rpx;
  color: #fbbf24;
  opacity: 0.9;
  cursor: pointer;
}

.complete-info-tip {
  margin-top: 8rpx;
}

.complete-info-text {
  font-size: 24rpx;
  color: #fbbf24;
  opacity: 0.9;
}

.logout-btn {
  padding: 12rpx 24rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.logout-btn text {
  font-size: 28rpx;
  color: #ffffff;
}

/* 手机号相关样式 */
.phone-section {
  margin-bottom: 8rpx;
}

.phone-info {
  margin-bottom: 8rpx;
}

.phone {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
  margin-bottom: 8rpx;
}

.get-phone-btn {
  background: linear-gradient(135deg, #07c160, #05a850);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  padding: 12rpx 24rpx;
  margin: 8rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
}

.get-phone-btn::after {
  border: none;
}

.get-phone-btn.button-hover {
  background: linear-gradient(135deg, #05a850, #048a43);
  transform: scale(0.98);
}

/* 头像相关样式（新方案） */
.avatar-section {
  position: relative;
  margin-right: 32rpx;
  width: 120rpx;
  height: 120rpx;
  flex-shrink: 0;
  margin-left: 20rpx;
}

.avatar-btn {
  position: relative;
  padding: 0 !important;
  margin: 0;
  border: none;
  background: transparent;
  border-radius: 50%;
  width: 120rpx !important;
  height: 120rpx !important;
  display: block;
  box-sizing: border-box;
  line-height: normal;
  text-align: left;
}

.avatar-btn::after {
  border: none;
  background: none;
  width: auto;
  height: auto;
  left: auto;
  right: auto;
  top: auto;
  bottom: auto;
  transform: none;
}

.avatar-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.avatar-btn:active .avatar-mask {
  opacity: 1;
}

.avatar-tip {
  color: white;
  font-size: 20rpx;
  text-align: center;
}

/* 昵称相关样式（新方案） */
.nickname-section {
  margin-bottom: 8rpx;
}

.nickname-input-container {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 16rpx 20rpx;
  margin-bottom: 8rpx;
  border: 2rpx solid transparent;
  transition: border-color 0.3s ease;
}

.nickname-input-container:focus-within {
  border-color: rgba(255, 255, 255, 0.3);
}

.nickname-input {
  width: 100%;
  color: white;
  font-size: 32rpx;
  background: transparent;
  border: none;
  min-height: 60rpx;
  line-height: 60rpx;
}

.nickname-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}





/* Order Section */
.order-section {
  margin: 32rpx 24rpx 0;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  padding: 24rpx;
  width: calc(100% - 48rpx);
  box-sizing: border-box;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
}

.more-link {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #6b7280;
}

.arrow {
  margin-left: 8rpx;
  font-size: 32rpx;
}

.order-status-grid {
  display: flex;
  justify-content: space-around;
  text-align: center;
  flex-wrap: wrap;
}

.order-status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
  position: relative;
}

.status-icon image {
  width: 40rpx;
  height: 40rpx;
}

.badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: rgb(250, 0, 17);
  color: #ffffff;
  font-size: 20rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-text {
  font-size: 24rpx;
  color: #374151;
}

/* Invitation Section */
.invitation-section {
  margin: 32rpx 24rpx 0; /* 上边距与order-section保持一致，去掉下边距避免重复 */
  width: calc(100% - 48rpx); /* 宽度与其他组件保持一致 */
  box-sizing: border-box;
}

/* 新版邀请页面入口样式 - 橙色背景，居中显示 */
.invitation-banner.new-version {
  background: linear-gradient(135deg, #ff9500 0%, #ff6b00 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(255, 149, 0, 0.3);
}

.invitation-banner:active {
  transform: scale(0.98);
  transition: all 0.2s ease;
}

.banner-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.banner-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.banner-title {
  display: block;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-align: center;
}

.banner-subtitle {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-size: 26rpx;
  text-align: center;
}

.banner-right {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
}

.banner-icon {
  font-size: 48rpx;
}

.banner-arrow {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

/* Services Section */
.services-section {
  margin: 32rpx 24rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  width: calc(100% - 48rpx);
  box-sizing: border-box;
}

.services-grid {
  display: flex;
  justify-content: space-around;
  text-align: center;
  flex-wrap: wrap;
  margin-top: 24rpx;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.service-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
}

.service-icon image {
  width: 40rpx;
  height: 40rpx;
}

.service-text {
  font-size: 24rpx;
  color: #374151;
  text-align: center;
}

/* 测试功能样式 */
.test-item {
  opacity: 0.8;
}

.test-item .service-icon {
  background-color: #fff3cd;
  border: 2rpx solid #ffeaa7;
}

.test-icon {
  font-size: 32rpx;
}

.test-item .service-text {
  color: #856404;
  font-size: 22rpx;
}

/* 演示功能样式 */
.demo-item {
  opacity: 0.9;
}

.demo-item .service-icon {
  background-color: #e1f5fe;
  border: 2rpx solid #81d4fa;
}

.demo-icon {
  font-size: 32rpx;
}

.demo-item .service-text {
  color: #0277bd;
  font-size: 22rpx;
}

/* 退出登录测试功能样式 */
.logout-test-item {
  opacity: 0.9;
}

.logout-test-item .service-icon {
  background-color: #ffebee;
  border: 2rpx solid #ef5350;
}

.logout-test-icon {
  font-size: 32rpx;
}

.logout-test-item .service-text {
  color: #d32f2f;
  font-size: 22rpx;
}
