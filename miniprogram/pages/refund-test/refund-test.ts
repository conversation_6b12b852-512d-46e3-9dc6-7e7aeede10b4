// pages/refund-test/refund-test.ts
// 退款功能测试页面

Page({
  data: {
    testOrderId: 1,
    testRefundId: 1
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '退款功能测试'
    })
  },

  /**
   * 测试申请退款
   */
  testApplyRefund() {
    wx.navigateTo({
      url: `/pages/refund-apply/refund-apply?orderId=${this.data.testOrderId}`
    })
  },

  /**
   * 测试退款列表
   */
  testRefundList() {
    wx.navigateTo({
      url: '/pages/orders/orders?status=refund_afterSale'
    })
  },

  /**
   * 测试退款详情
   */
  testRefundDetail() {
    wx.navigateTo({
      url: `/pages/refund-detail/refund-detail?refundId=${this.data.testRefundId}`
    })
  },

  /**
   * 输入测试订单ID
   */
  onOrderIdInput(e: any) {
    this.setData({
      testOrderId: parseInt(e.detail.value) || 1
    })
  },

  /**
   * 输入测试退款ID
   */
  onRefundIdInput(e: any) {
    this.setData({
      testRefundId: parseInt(e.detail.value) || 1
    })
  }
})
