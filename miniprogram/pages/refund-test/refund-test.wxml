<!--pages/refund-test/refund-test.wxml-->
<view class="refund-test-page">
  <view class="test-section">
    <text class="section-title">退款功能测试</text>
    
    <view class="input-group">
      <text class="input-label">测试订单ID：</text>
      <input class="test-input" 
             type="number" 
             value="{{testOrderId}}" 
             bindinput="onOrderIdInput" 
             placeholder="请输入订单ID" />
    </view>
    
    <view class="input-group">
      <text class="input-label">测试退款ID：</text>
      <input class="test-input" 
             type="number" 
             value="{{testRefundId}}" 
             bindinput="onRefundIdInput" 
             placeholder="请输入退款ID" />
    </view>
    
    <view class="button-group">
      <button class="test-btn" bindtap="testApplyRefund">测试申请退款</button>
      <button class="test-btn" bindtap="testRefundList">测试退款列表</button>
      <button class="test-btn" bindtap="testRefundDetail">测试退款详情</button>
    </view>
  </view>
  
  <view class="info-section">
    <text class="info-title">功能说明</text>
    <view class="info-list">
      <text class="info-item">• 申请退款：检查订单退款资格并创建退款申请</text>
      <text class="info-item">• 退款列表：查看用户的所有退款申请</text>
      <text class="info-item">• 退款详情：查看退款申请的详细信息和状态</text>
      <text class="info-item">• 支持退货退款和仅退款两种类型</text>
      <text class="info-item">• 支持上传证据图片和填写物流信息</text>
    </view>
  </view>
</view>
