// types/coupon.ts
// 优惠券相关的类型定义

// 优惠券基础信息
export interface Coupon {
  id: number
  userId: number
  couponCode: string
  couponType: string
  couponName: string
  amount: number // 优惠金额（分）
  minOrderAmount?: number // 最低订单金额要求（分）
  status: string
  statusDesc: string
  sourceType?: string // 来源类型
  sourceId?: number // 来源ID
  description?: string
  issuedAt: string // 发放时间
  expiresAt: string // 过期时间
  usedAt?: string // 使用时间
  orderId?: number // 使用的订单ID
  createdAt: string
  updatedAt: string
}

// 优惠券选择状态
export interface CouponSelection {
  coupon: Coupon
  selected: boolean
}

// 优惠券使用信息
export interface CouponUsage {
  couponId: number
  couponCode: string
  discountAmount: number // 实际优惠金额（分）
}

// 价格计算结果
export interface PriceCalculation {
  originalAmount: number // 原始金额（分）
  totalDiscount: number // 总优惠金额（分）
  finalAmount: number // 最终金额（分）
  usedCoupons: CouponUsage[] // 使用的优惠券
}

// 优惠券验证结果
export interface CouponValidation {
  isValid: boolean
  reason?: string // 无效原因
  maxDiscount?: number // 最大可优惠金额（分）
}

// 优惠券选择页面数据
export interface CouponSelectPageData {
  availableCoupons: CouponSelection[]
  selectedCoupons: CouponSelection[]
  orderAmount: number // 订单金额（分）
  loading: boolean
  invitationCoupons: CouponSelection[] // 邀请奖励优惠券
  regularCoupons: CouponSelection[] // 普通优惠券
}

// 优惠券类型枚举
export enum CouponType {
  INVITATION_REGISTER = 'INVITATION_REGISTER', // 邀请注册奖励
  INVITATION_ORDER = 'INVITATION_ORDER', // 邀请订单奖励
  REGULAR = 'REGULAR' // 普通优惠券
}

// 优惠券状态枚举
export enum CouponStatus {
  AVAILABLE = 'AVAILABLE', // 可用
  USED = 'USED', // 已使用
  EXPIRED = 'EXPIRED', // 已过期
  INVALID = 'INVALID' // 无效
}

// API响应类型
export interface CouponListResponse {
  code: string
  message: string
  data: Coupon[]
  success: boolean
}

export interface CouponValidationResponse {
  code: string
  message: string
  data: CouponValidation
  success: boolean
}

// 优惠券选择结果（用于页面间传递数据）
export interface CouponSelectResult {
  selectedCoupons: Coupon[]
  totalDiscount: number // 总优惠金额（分）
  finalAmount: number // 优惠后金额（分）
}
