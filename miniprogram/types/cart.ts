// types/cart.ts
// 购物车相关的类型定义

export interface CartItemDTO {
  id: number                    // 购物车项ID - 对应接口字段: id
  userId: number               // 用户ID - 对应接口字段: userId
  skuId: number                // SKU ID - 对应接口字段: skuId
  skuCode: string              // SKU编码 - 对应接口字段: skuCode
  productId: number            // 商品ID - 对应接口字段: productId
  productName: string          // 商品名称 - 对应接口字段: productName
  productImageUrl: string      // 商品图片URL - 对应接口字段: productImageUrl
  skuAttributes: string        // SKU属性描述 - 对应接口字段: skuAttributes
  unitPrice: number            // 单价 - 对应接口字段: unitPrice
  quantity: number             // 购买数量 - 对应接口字段: quantity
  subtotal: number             // 小计金额 - 对应接口字段: subtotal
  stockQuantity: number        // 库存数量 - 对应接口字段: stockQuantity
  inStock: boolean             // 是否有库存 - 对应接口字段: inStock
  addedAt: string              // 加入购物车时间 - 对应接口字段: addedAt
  updatedAt: string            // 最后更新时间 - 对应接口字段: updatedAt
}

export interface CartSummaryDTO {
  items: CartItemDTO[]         // 购物车项列表
  totalQuantity: number        // 商品总数量
  totalAmount: number          // 商品总金额
  validQuantity: number        // 有效商品数量
  validAmount: number          // 有效商品金额
  allInStock: boolean          // 是否全部有库存
}

export interface AddToCartRequest {
  skuId: number                // SKU ID
  quantity: number             // 购买数量
}

export interface UpdateCartItemRequest {
  quantity: number             // 购买数量
}

// API响应基础结构
export interface ApiResponse<T> {
  code: string
  message: string
  data: T
  timestamp: number
  success: boolean
}

// 具体的API响应类型
export interface CartItemListResponse extends ApiResponse<CartItemDTO[]> {}
export interface CartSummaryResponse extends ApiResponse<CartSummaryDTO> {}
export interface CartCountResponse extends ApiResponse<number> {}
export interface CartOperationResponse extends ApiResponse<void> {}

// 页面使用的购物车项数据结构（兼容现有页面）
export interface CartItem {
  id: number
  name: string                 // 对应 productName
  sku: string                  // 对应 skuAttributes
  price: number                // 对应 unitPrice
  quantity: number
  selected: boolean            // 页面选择状态，不是API字段
  image: string                // 对应 productImageUrl
  needPrescription?: boolean   // 业务逻辑字段
  skuId?: number              // 对应 skuId
  productId?: number          // 对应 productId
  subtotal?: number           // 对应 subtotal
  inStock?: boolean           // 对应 inStock
  stockQuantity?: number      // 对应 stockQuantity
}

// 购物车页面数据结构
export interface CartPageData {
  cartItems: CartItem[]
  allSelected: boolean
  totalPrice: number
  selectedCount: number
  loading: boolean
  refreshing: boolean
}

// 数据转换工具函数类型
export type CartItemConverter = {
  fromDTO: (dto: CartItemDTO) => CartItem
  toDTO: (item: CartItem) => Partial<CartItemDTO>
}
