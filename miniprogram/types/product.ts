// types/product.ts
// 商品相关的类型定义

// 商品图片信息（轻量级版本，只包含前端需要的字段）
export interface ProductImageDTO {
  imageUrl: string
  sortOrder?: number
}

export interface ProductDTO {
  id: number
  name: string
  slug?: string
  brandId?: number
  brandName?: string
  shortDescription?: string
  fullDescription?: string
  mainImageUrl?: string
  productImages?: ProductImageDTO[]
  detailImages?: ProductImageDTO[]
  isActive: boolean
  isFeatured?: boolean
  publishedAt?: string
  createdAt?: string
  updatedAt?: string
  minPrice: string
  maxPrice: string
  totalStock?: number
  categories?: CategoryDTO[]
  skus?: ProductSkuDTO[]
  priceRange?: string
  published?: boolean
}

// 商品SKU信息
export interface ProductSkuDTO {
  id: number
  productId: number
  skuCode: string
  nameExtension?: string
  price: number
  originalPrice?: number
  stockQuantity: number
  lowStockThreshold?: number
  weightKg?: number
  dimensionsCm?: string
  imageUrl?: string
  attributes?: { [key: string]: string }
  attributesJson?: string
  isActive: boolean
  createdAt?: string
  updatedAt?: string
  productName?: string
  discountRate?: number
  lowStock?: boolean
  fullName?: string
}

// 商品统计信息
export interface ProductStatistics {
  totalSkuCount: number
  activeSkuCount: number
  totalStockQuantity: number
  lowStockSkuCount: number
  minPrice: number
  maxPrice: number
}

// 商品及其SKU信息
export interface ProductWithSkusDTO {
  product: ProductDTO
  skus: ProductSkuDTO[]
  statistics: ProductStatistics
}

// 分类信息（简化版）
export interface CategoryDTO {
  id: number
  parentId?: number
  name: string
  slug?: string
  description?: string
  imageUrl?: string
  level: number
  path?: string
  sortOrder: number
  isActive: boolean
  createdAt?: string
  updatedAt?: string
  children?: CategoryDTO[]
  productCount?: number
  topLevel?: boolean
}

export interface ApiResponse<T> {
  code: string
  message: string
  data: T
  timestamp: number
  success: boolean
}

// 分页响应数据结构
export interface PagedData<T> {
  records: T[]
  total: number
  pageNum: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrevious: boolean
  empty: boolean
  size: number
}

export interface ProductListResponse extends ApiResponse<PagedData<ProductDTO>> {}
export interface ProductWithSkusResponse extends ApiResponse<ProductWithSkusDTO> {}

// 页面使用的商品数据结构
export interface ProductItem {
  id: number
  name: string
  brandName?: string
  shortDescription?: string
  mainImageUrl?: string
  minPrice: string
  maxPrice: string
  totalStock?: number
  isFeatured?: boolean
  isSearchResult?: boolean // 标记是否为搜索结果
}

// 商品列表页面数据结构
export interface ProductPageData {
  loading: boolean
  categoryId?: number
  categoryName?: string
  products: ProductItem[]
  hasMore: boolean
  page: number
  pageSize: number
}

// 商品查询参数
export interface ProductQueryParams {
  categoryId?: number
  brandId?: number
  keyword?: string
  page?: number
  pageSize?: number
  sortBy?: 'price' | 'sales' | 'created'
  sortOrder?: 'asc' | 'desc'
}
