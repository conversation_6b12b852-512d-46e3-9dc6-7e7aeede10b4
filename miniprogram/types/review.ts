// types/review.ts
// 评论相关的类型定义

// 评论图片信息
export interface ReviewImage {
  id: number
  imageUrl: string
  imageName: string
  description: string
  category: string
  businessId: number
  businessType: string
  tags?: string
  fileSize?: number
  width?: number
  height?: number
  fileType: string
  sortOrder: number
  uploadedBy?: number
  status: number
  createdAt: string
  updatedAt: string
}

// 评论回复信息
export interface ReplyDTO {
  id: number
  reviewId: number
  parentId?: number
  userId: number
  userName: string
  userAvatar: string
  replyToUserId?: number
  replyToUserName?: string
  content: string
  isAdminReply: boolean
  likeCount: number
  createdAt: string
  updatedAt: string
}

// 评论详情
export interface ReviewDTO {
  id: number
  productId: number
  productName: string
  productMainImage: string
  skuId: number
  skuName: string
  userId: number
  userName: string
  userAvatar: string
  isAnonymous: boolean
  rating: number
  title: string
  content: string
  replyCount: number
  likeCount: number
  isLiked: boolean
  isVerifiedPurchase: boolean
  adminReply?: string
  adminReplyTime?: string
  images: ReviewImage[]
  replies: ReplyDTO[]
  createdAt: string
  updatedAt: string
}

// 评论统计信息
export interface ReviewStatsDTO {
  productId: number
  totalReviews: number
  averageRating: number
  rating1Count: number
  rating2Count: number
  rating3Count: number
  rating4Count: number
  rating5Count: number
  withImagesCount: number
  verifiedPurchaseCount: number
  positiveRate: number
}

// 分页结果
export interface PageResult<T> {
  records: T[]
  total: number
  pageNum: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrevious: boolean
}

// API响应
export interface ApiResponse<T> {
  code: string
  message: string
  data: T
  timestamp: string
  success: boolean
}

// 评论列表响应
export interface ReviewListResponse extends ApiResponse<PageResult<ReviewDTO>> {}

// 评论统计响应
export interface ReviewStatsResponse extends ApiResponse<ReviewStatsDTO> {}

// 评论查询参数
export interface ReviewQueryParams {
  page?: number
  size?: number
  sortBy?: string
}
