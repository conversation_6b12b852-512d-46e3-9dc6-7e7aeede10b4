// types/category.ts
// 分类相关的类型定义

export interface CategoryDTO {
  id: number
  parentId?: number | null
  name: string
  slug?: string
  description?: string | null
  imageUrl?: string | null
  level: number
  path?: string
  sortOrder: number
  isActive: boolean
  createdAt?: string
  updatedAt?: string
  children?: CategoryDTO[] | null
  productCount?: number | null
  topLevel?: boolean
  treeType?: string
}

export interface ApiResponse<T> {
  code: string
  message: string
  data: T
  timestamp: number
  success: boolean
}

export interface CategoryTreeResponse extends ApiResponse<CategoryDTO[]> {}

// 页面使用的分类数据结构
export interface CategoryItem {
  id: number
  name: string
  icon?: string
  children?: SubCategoryItem[]
}

export interface SubCategoryItem {
  id: number
  name: string
  icon?: string
  parentId: number
}

// 页面数据结构
export interface CategoryPageData {
  loading: boolean
  activeCategory: string
  activeCategoryId?: number
  categories: CategoryItem[]
  subCategories: { [key: string]: SubCategoryItem[] }
}
