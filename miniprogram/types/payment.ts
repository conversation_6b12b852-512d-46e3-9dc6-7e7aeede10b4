// types/payment.ts
// 支付相关的类型定义

export interface CreatePaymentRequest {
  orderNo: string          // 订单号
  openId?: string          // 用户openId（后端自动获取）
  totalAmount: number      // 支付金额（分）
  description: string      // 商品描述
  attach?: string          // 附加数据
  clientIp?: string        // 客户端IP（后端自动获取）
}

export interface PaymentParams {
  timeStamp: string        // 时间戳
  nonceStr: string         // 随机字符串
  packageValue: string     // 统一下单接口返回的prepay_id参数值
  signType: string         // 签名类型
  paySign: string          // 签名
  orderNo?: string         // 订单号（用于页面跳转）
}

// 支付状态类型
export type PaymentStatus = 'SUCCESS' | 'NOTPAY' | 'CLOSED' | 'REFUND' | 'REVOKED' | 'USERPAYING' | 'PAYERROR'

// 支付状态响应（接口直接返回状态字符串）
export interface PaymentStatusResponse {
  status: PaymentStatus
  statusDesc?: string
  orderNo?: string
  totalAmount?: number
  paidAmount?: number
  payTime?: string
  transactionId?: string
  bankType?: string
  isPaySuccess?: boolean
}

// API响应基础结构
export interface ApiResponse<T> {
  code: string
  message: string
  data: T
  timestamp: number
  success: boolean
}

// 具体的API响应类型
export interface CreatePaymentResponse extends ApiResponse<PaymentParams> {}
// 支付状态查询响应（data 字段直接是状态字符串）
export interface PaymentStatusQueryResponse extends ApiResponse<PaymentStatus> {}

// 订单相关类型
export interface OrderItem {
  skuId: number
  skuCode: string
  productName: string
  skuAttributes: string
  unitPrice: number
  quantity: number
  subtotal: number
  productImageUrl: string
}

// 优惠券使用信息
export interface OrderCoupon {
  couponId: number
  couponCode: string
}

export interface CreateOrderRequest {
  addressId: number        // 收货地址ID
  items: OrderItem[]       // 订单商品列表
  totalAmount: number      // 订单总金额（分）
  notesToSeller?: string   // 用户留言（与后端字段名保持一致）
  coupons?: OrderCoupon[]  // 使用的优惠券列表
}

export interface OrderInfo {
  id: number
  orderNumber: string      // 修正：后端返回的字段名是 orderNumber
  status: string
  statusDescription?: string // 订单状态描述
  totalAmount: number
  createTime: string
  items: OrderItem[]
  shippingAddress: {
    recipientName: string
    phoneNumber: string
    fullAddress: string
  }
  // 发货信息
  shippingCompany?: string  // 快递公司名称
  trackingNumber?: string   // 快递单号
  shippedAt?: string        // 发货时间
}

// 结算页面数据结构
export interface CheckoutPageData {
  selectedItems: OrderItem[]
  totalAmount: number
  totalQuantity: number
  selectedAddress: any
  loading: boolean
  submitting: boolean
}

// 支付页面数据结构
export interface PaymentPageData {
  orderInfo: OrderInfo | null
  paymentParams: PaymentParams | null
  paymentStatus: string
  loading: boolean
}
