// types/address.ts
// 地址相关的类型定义

export interface Address {
  id: number             // 地址ID (服务端返回的是数字类型)
  userId: number         // 用户ID
  recipientName: string  // 收件人姓名
  phoneNumber: string    // 收件人电话
  regionProvince: string // 省份
  regionCity: string     // 城市
  regionDistrict: string // 区县
  streetAddress: string  // 详细街道地址
  postalCode?: string    // 邮政编码
  isDefault: boolean     // 是否为默认地址
  createdAt: string      // 创建时间
  updatedAt: string      // 更新时间
}

export interface AddressFormData {
  recipientName: string
  phoneNumber: string
  regionProvince: string
  regionCity: string
  regionDistrict: string
  streetAddress: string
  postalCode?: string
  isDefault: boolean
}

// 创建地址请求
export interface CreateAddressRequest {
  recipientName: string
  phoneNumber: string
  regionProvince: string
  regionCity: string
  regionDistrict: string
  streetAddress: string
  postalCode?: string
  isDefault?: boolean
}

// 更新地址请求
export interface UpdateAddressRequest {
  recipientName?: string
  phoneNumber?: string
  regionProvince?: string
  regionCity?: string
  regionDistrict?: string
  streetAddress?: string
  postalCode?: string
  isDefault?: boolean
}

// 地址列表页面数据结构
export interface AddressPageData {
  addresses: Address[]
  loading: boolean
}

// 地址编辑页面数据结构
export interface AddressEditPageData {
  formData: AddressFormData
  isEdit: boolean
  addressId?: number
  regions: RegionData[]
  selectedRegion: {
    province: string
    city: string
    district: string
  }
  showRegionPicker: boolean
}

// API响应结果
export interface ApiResult<T = any> {
  code: string
  message: string
  data: T
  timestamp: number
  success: boolean
}

// 地址列表API响应
export interface AddressListResponse extends ApiResult<Address[]> {}

// 单个地址API响应
export interface AddressResponse extends ApiResult<Address> {}

// 地区选择器数据结构
export interface RegionData {
  name: string
  code: string
  children?: RegionData[]
}

// 地址验证结果
export interface AddressValidation {
  isValid: boolean
  errors: string[]
}

// 地址操作结果
export interface AddressOperationResult {
  success: boolean
  message: string
  data?: Address
}
