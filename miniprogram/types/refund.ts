// types/refund.ts
// 退款相关的类型定义

// API响应基础结构
export interface ApiResponse<T> {
  code: number
  message: string
  data: T
  timestamp?: number
  success?: boolean
}

// 退款状态枚举
export enum RefundStatus {
  PENDING_REVIEW = 'pending_review',      // 待审核
  APPROVED = 'approved',                  // 已同意
  PENDING_REFUND = 'pending_refund',      // 等待退款处理
  REJECTED = 'rejected',                  // 已拒绝
  USER_SHIPPING = 'user_shipping',        // 用户寄回中
  MERCHANT_RECEIVED = 'merchant_received', // 商家已收货
  REFUNDED = 'refunded',                  // 已退款
  CANCELLED = 'cancelled'                 // 已取消
}

// 退款类型枚举
export enum RefundType {
  REFUND_ONLY = 'refund_only',           // 仅退款
  RETURN_REFUND = 'return_refund'        // 退货退款
}

// 退款原因枚举
export enum RefundReason {
  PRICE_OR_PROMOTION = 'price_or_promotion', // 买贵了/少用优惠
  SIZE_MISMATCH = 'size_mismatch',       // 尺寸不符
  SEVEN_DAYS_NO_REASON = 'seven_days_no_reason', // 七天无理由退货
  NOT_AS_DESCRIBED = 'not_as_described', // 与描述不符
  CHANGE_MIND = 'change_mind',           // 不想要了
  QUALITY_ISSUE = 'quality_issue',       // 质量问题
  WRONG_ITEM = 'wrong_item',             // 发错商品
  OTHER = 'other'                        // 其他原因
}

// 退款操作枚举
export enum RefundAction {
  CANCEL = 'cancel',                     // 取消申请
  SUBMIT_SHIPPING = 'submit_shipping',   // 提交物流信息
  EDIT_SHIPPING = 'edit_shipping',       // 修改物流信息
  APPROVE = 'approve',                   // 审核通过
  REJECT = 'reject',                     // 审核拒绝
  CONFIRM_RECEIPT = 'confirm_receipt',   // 确认收货
  PROCESS_REFUND = 'process_refund'      // 处理退款
}

// 退款原因选项
export interface RefundReasonOption {
  code: string
  name: string
  description: string
}

// 订单商品项
export interface OrderItemInfo {
  orderItemId: number
  productName: string
  quantity: number
  price: number
  canRefund: boolean
  maxRefundQuantity: number
  productImageUrl?: string
}

// 退款资格检查响应（根据实际接口返回格式）
export interface RefundEligibilityResponse {
  canRefund: boolean
  reason?: string // 当 canRefund 为 false 时，说明不能退款的原因
  suggestedRefundType: RefundType
  refundableItems: Array<{
    orderItemId: number
    productName: string
    quantity: number
    unitPrice: number
    totalPrice: number
    availableQuantity: number
    refundedQuantity: number
    productImageUrl?: string
  }>
  order: {
    id: number
    tenantId: number
    userId: number
    orderNumber: string
    status: string
    refundStatus: string | null
    totalAmount: number
    itemsTotalAmount: number
    discountAmount: number
    shippingFee: number
    refundedAmount: number
    shippingAddressSnapshot: string
    billingAddressSnapshot: string | null
    paymentMethod: string
    notesToSeller: string | null
    adminNotes: string | null
    isDeleted: boolean
    placedAt: string
    paidAt: string | null
    shippedAt: string | null
    deliveredAt: string | null
    cancelledAt: string | null
    completedAt: string | null
    refundedAt: string | null
    createdAt: string
    updatedAt: string
  }
}

// 退款商品明细（页面内部使用，金额以分为单位）
export interface RefundItem {
  orderItemId: number
  refundQuantity: number
  refundAmount: number // 分为单位
}

// 提交给服务端的退款商品明细（金额以元为单位）
export interface RefundItemForSubmit {
  orderItemId: number
  refundQuantity: number
  refundAmount: number // 元为单位
}

// 创建退款申请请求
export interface CreateRefundRequest {
  orderId: number
  refundType: RefundType
  refundReason: RefundReason
  refundDescription?: string
  refundAmount: number // 元为单位
  refundItems: RefundItemForSubmit[]
  evidenceImages?: string[]
}

// 退款申请基础信息
export interface RefundInfo {
  id: number
  refundNumber: string
  orderNumber: string
  refundType: RefundType
  refundTypeDesc: string
  refundReasonDesc: string
  refundAmount: number
  status: RefundStatus
  statusDesc: string
  createdAt: string
  updatedAt: string
  availableActions?: string[]
  productDetails: Array<{
    orderItemId: number
    productId: number
    productName: string
    productMainImageUrl: string
    skuNameExtension: string
    skuPrice: number
    skuAttributes: {
      [key: string]: string
    }
    quantity: number
    unitPriceAtPurchase: number
    totalPrice: number
    refundQuantity: number
    refundAmount: number
  }>
}

// 退款申请详情
export interface RefundDetail extends RefundInfo {
  orderId: number
  refundReason: RefundReason
  refundDescription?: string
  refundItems: Array<{
    orderItemId: number
    productName: string
    refundQuantity: number
    refundAmount: number
    productImageUrl?: string
  }>
  evidenceImages: string[]
  adminReviewNotes?: string
  reviewedAt?: string
  availableActions: string[]
  shippingInfo?: {
    id: number
    shippingCompany: string
    trackingNumber: string
    shippingNotes?: string
    shippedAt: string
    shippingStatus?: string
    senderName?: string
    senderPhone?: string
    senderAddress?: string
    receiverName?: string
    receiverPhone?: string
    receiverAddress?: string
  }
}

// 退货物流信息
export interface RefundShippingRequest {
  refundRequestId: number
  shippingCompany: string
  trackingNumber: string
  shippingNotes?: string
}

// 修改退货物流信息
export interface UpdateRefundShippingRequest {
  shippingCompany: string
  trackingNumber: string
  shippingNotes?: string
}

// 退货收货地址
export interface ReturnAddress {
  contactName: string
  contactPhone: string
  address: string
  zipCode: string
  notes: string
}

// 分页响应
export interface PageResponse<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// API响应类型
export interface RefundEligibilityApiResponse extends ApiResponse<RefundEligibilityResponse> {}
export interface RefundReasonsApiResponse extends ApiResponse<{ reasons: RefundReasonOption[] }> {}
export interface CreateRefundApiResponse extends ApiResponse<{
  id: number
  refundNumber: string
  status: RefundStatus
  statusDesc: string
  createdAt: string
}> {}
// 服务端返回的退款列表原始数据格式
export interface ServerRefundListData {
  records: Array<{
    id: number
    tenant_id: number
    user_id: number
    order_id: number
    order_number: string
    refund_number: string
    refund_type: string
    refund_reason: string
    refund_description: string
    refund_amount: number
    evidence_images: string
    refund_items: string
    status: string
    is_deleted: boolean
    created_at: string
    updated_at: string
    productDetails: Array<{
      orderItemId: number
      productId: number
      productName: string
      productNameSnapshot: string
      productMainImageUrl: string
      productShortDescription: string | null
      productFullDescription: string | null
      skuId: number
      skuCode: string
      skuNameExtension: string
      skuPrice: number
      skuOriginalPrice: number | null
      skuImageUrl: string | null
      skuAttributes: string // JSON 字符串
      skuAttributesSnapshot: string // JSON 字符串
      quantity: number
      unitPriceAtPurchase: number
      totalPrice: number
      refundQuantity: number
      refundAmount: number
      skuStockQuantity: number
      productBrandId: number | null
    }>
  }>
  total: number
  size: number
  current: number
  pages: number
  orders: any[]
  optimizeCountSql: boolean
  searchCount: boolean
  maxLimit: any
  countId: any
}

export interface RefundListApiResponse extends ApiResponse<ServerRefundListData> {}
// 服务端返回的退款详情原始数据格式
export interface ServerRefundDetailData {
  id: number
  tenant_id: number
  user_id: number
  user_nickname: string
  user_phone: string
  order_id: number
  order_number: string
  order_status: string
  order_total_amount: number
  refund_number: string
  refund_type: string
  refund_reason: string
  refund_description: string
  refund_amount: number
  evidence_images: string // JSON 字符串
  refund_items: string // JSON 字符串
  status: string
  is_deleted: boolean
  created_at: string
  updated_at: string
  statusLogs: Array<{
    id: number
    tenantId: number
    refundRequestId: number
    previousStatus: string | null
    currentStatus: string
    operatorType: string
    operatorId: number
    operatorName: string | null
    changeReason: string
    remark: string
    createdAt: string
  }>
  recordList: any[]
  shippingList: any[]
}

export interface RefundDetailApiResponse extends ApiResponse<ServerRefundDetailData> {}
export interface ReturnAddressApiResponse extends ApiResponse<ReturnAddress> {}

// 页面数据类型
export interface RefundPageData {
  loading: boolean
  orderInfo: {
    orderId: number
    orderNumber: string
    orderStatus: string
    totalAmount: number
    items: Array<{
      orderItemId: number
      productName: string
      quantity: number
      price: number
      canRefund: boolean
      maxRefundQuantity: number
      refundQuantity?: number
      refundAmount?: number
      productImageUrl?: string
    }>
  } | null
  refundReasons: RefundReasonOption[]
  selectedReason: RefundReason | ''
  selectedReasonIndex: number
  selectedReasonText: string
  refundType: RefundType
  refundAmount: number
  refundDescription: string
  evidenceImages: string[]
  selectedItems: RefundItem[]
}

export interface RefundListPageData {
  loading: boolean
  refreshing: boolean
  refunds: RefundInfo[]
  hasMore: boolean
  page: number
  currentStatus: RefundStatus | ''
  statusTabs: Array<{
    key: RefundStatus | ''
    name: string
    count: number
  }>
  RefundAction: {
    CANCEL: string
    SUBMIT_SHIPPING: string
  }
}

export interface RefundDetailPageData {
  loading: boolean
  refundDetail: RefundDetail | null
  returnAddress: ReturnAddress | null
  showShippingModal: boolean
  showEditShippingModal: boolean
  // 可选的物流公司列表（用于下拉选择）
  shippingCompanies: Array<{ code: string; name: string }>
  // 当前选择的索引（新提交弹窗）
  shippingCompanyIndex: number
  // 当前选择的索引（编辑弹窗）
  editShippingCompanyIndex: number
  shippingForm: {
    shippingCompany: string
    trackingNumber: string
    shippingNotes: string
  }
  editShippingForm: {
    shippingCompany: string
    trackingNumber: string
    shippingNotes: string
  }
  RefundAction: {
    CANCEL: string
    SUBMIT_SHIPPING: string
    EDIT_SHIPPING: string
  }
}
