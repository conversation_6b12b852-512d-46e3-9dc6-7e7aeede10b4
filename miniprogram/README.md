# 眼镜商城微信小程序

基于参考UI设计 `UI参考代码/eyewear-store-v2` 实现的微信小程序版本。

## 项目结构

```
miniprogram/
├── app.json          # 小程序配置文件
├── app.ts           # 小程序入口文件
├── app.wxss         # 全局样式文件
├── pages/           # 页面目录
│   ├── home/        # 首页
│   ├── category/    # 分类页
│   ├── cart/        # 购物车页
│   ├── profile/     # 个人中心页
│   └── service/     # 客服页
├── images/          # 图片资源目录
└── utils/           # 工具函数目录
```

## 主要功能

### 1. 首页 (home)
- 轮播图展示
- 快速分类入口（品牌镜片、品牌镜框、防控镜片、儿童镜框、更多）
- 品牌镜片展示区（蔡司、罗敦司得、尼康等）
- 品牌镜框展示区（夏蒙、暴龙、陌森等）
- 防控专区（控优点、新乐学、新趣控等）

### 2. 分类页 (category)
- 搜索功能
- 三级分类导航
- 左侧一级分类菜单
- 右侧品牌网格展示

### 3. 购物车页 (cart)
- 商品列表展示
- 单选/全选功能
- 数量调整
- 价格计算
- 结算功能
- 空购物车状态

### 4. 个人中心页 (profile)
- 用户信息展示
- 订单状态快捷入口（待付款、待提交处方、待发货、待收货）
- 资质认证状态
- 服务功能入口（资质认证、收货地址、我的收藏、联系客服、帮助中心）

### 5. 客服页 (service)
- 聊天界面
- 常见问题快捷回复
- 消息发送功能
- 自动回复机制

## 技术特点

1. **响应式设计**: 适配不同屏幕尺寸
2. **组件化开发**: 可复用的UI组件
3. **数据驱动**: 使用微信小程序数据绑定
4. **用户体验**: 流畅的交互动画和反馈

## 样式系统

采用类似Tailwind CSS的原子化样式类，包含：
- 布局类（flex, grid等）
- 间距类（padding, margin）
- 颜色类（背景色、文字色）
- 字体类（大小、粗细）
- 圆角、阴影等装饰类

## 当前状态

✅ **已完成**:
- 5个主要页面的完整实现
- 底部导航栏配置（暂时不显示图标）
- 所有必需的占位图片已创建
- 响应式布局和样式系统
- 基本的交互功能

## 待完善功能

1. **TabBar图标**: 当前tabBar暂时不显示图标，可以后续添加
2. **真实图片**: 将占位图片替换为实际的品牌logo和产品图片
3. **产品详情页**: 点击商品后的详情展示页面
4. **订单管理页面**: 订单列表、订单详情等
5. **地址管理页面**: 收货地址的增删改查
6. **支付功能集成**: 微信支付接口对接
7. **用户登录/注册**: 微信授权登录
8. **数据接口对接**: 替换模拟数据为真实API

## 使用说明

1. **导入项目**: 在微信开发者工具中导入 `miniprogram` 目录
2. **立即可用**: 当前版本可以直接运行，所有页面都能正常显示
3. **图片替换**: 根据需要将 `images/` 目录下的占位图片替换为真实图片
4. **功能扩展**: 根据实际需求添加更多页面和功能
5. **API对接**: 将模拟数据替换为真实的后端接口

## 注意事项

- ✅ 所有页面都可以正常运行和导航
- ✅ 占位图片已全部创建，不会出现图片加载错误
- ⚠️ 当前使用模拟数据，实际使用需要对接真实API
- ⚠️ TabBar暂时不显示图标，如需要可以添加相应的图标文件
- ⚠️ 部分功能（如支付、登录）需要配置相应的微信小程序权限
