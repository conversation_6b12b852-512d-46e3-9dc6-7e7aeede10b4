<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邀请奖励</title>
    <style>
        /* 基础样式重置和页面背景 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            background-color: #f0f2f5; /* 页面的浅灰色背景 */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }

        /* 邀请奖励横幅总容器 */
        .invitation-banner {
            max-width: 800px;
            width: 100%;
            background-color: #e60012; /* 主题红色背景 */
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* 顶部标题栏 */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 25px;
            color: white;
        }

        .header .title {
            font-size: 22px;
            font-weight: bold;
            margin: 0;
        }

        .header .share-link {
            font-size: 15px;
            color: white;
            text-decoration: none;
            transition: opacity 0.3s;
        }
        
        .header .share-link:hover {
            opacity: 0.8;
        }

        /* 新增：白色背景容器，用于包裹三个奖励卡片 */
        .rewards-wrapper {
            background-color: white; /* 设置为白色背景 */
            margin: 0 15px 20px 15px; /* 与红色背景的边距 */
            border-radius: 10px;      /* 圆角 */
            padding: 15px;            /* 内部留白 */
        }
        
        /* 奖励卡片区域容器 */
        .rewards-container {
            display: flex;
            justify-content: space-around;
            gap: 15px; /* 卡片之间的间距 */
        }

        /* 单个奖励卡片 */
        .reward-card {
            flex: 1;
            background-color: #fff9f0; /* 卡片的米白色背景 */
            border-radius: 10px;
            padding: 20px 15px;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        /* 卡片标题 */
        .reward-card .reward-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0 0 10px 0;
        }
        
        /* 奖励金额/内容 */
        .reward-card .reward-amount {
            font-size: 24px;
            font-weight: bold;
            color: #e60012;
            margin: 0 0 8px 0;
        }
        
        /* 奖励描述 */
        .reward-card .reward-description {
            font-size: 13px;
            color: #888;
            margin: 0 0 20px 0;
            min-height: 1.2em;
        }
        
        /* 卡片按钮 */
        .reward-card .reward-button {
            background-color: #e60012;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 10px 20px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            transition: opacity 0.3s;
        }

        .reward-card .reward-button:hover {
            opacity: 0.85;
        }

        /* 响应式设计：在小屏幕上垂直排列 */
        @media (max-width: 600px) {
            .rewards-container {
                flex-direction: column;
            }
            .header .title {
                font-size: 18px;
            }
            .header .share-link {
                font-size: 13px;
            }
        }
    </style>
</head>
<body>

    <div class="invitation-banner">
        <div class="header">
            <h1 class="title">邀请有礼</h1>
            <a href="#" class="share-link">分享赢好礼 &gt;</a>
        </div>
        <div class="rewards-wrapper">
            <div class="rewards-container">
                <div class="reward-card">
                    <div>
                        <h3 class="reward-title">填写邀请码奖励</h3>
                        <p class="reward-amount">20元红包</p>
                        <p class="reward-description">新用户专享 可用</p>
                    </div>
                    <button class="reward-button">立即领取</button>
                </div>
                <div class="reward-card">
                    <div>
                        <h3 class="reward-title">邀请奖励</h3>
                        <p class="reward-amount">30元优惠券</p>
                        <p class="reward-description">每成功邀请一位</p>
                    </div>
                    <button class="reward-button">去邀请</button>
                </div>
                <div class="reward-card">
                    <div>
                        <h3 class="reward-title">邀请注册奖励</h3>
                        <p class="reward-amount">50元红包+专属奖励</p>
                        <p class="reward-description">新用户完成首单</p>
                    </div>
                    <button class="reward-button">去查看</button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>